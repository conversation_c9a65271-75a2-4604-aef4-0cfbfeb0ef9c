# 任务完成工作流程

## 代码更改后的必须步骤

### 1. 代码质量检查

```bash
# 代码检查和修复
npm run lint

# TypeScript 类型检查
npm run type-check

# 代码格式化
npm run format
```

### 2. 测试验证

```bash
# 运行所有测试
npm run test

# 检查测试覆盖率 (必须达到 90%)
npm run test:coverage
```

### 3. 构建验证

```bash
# 库构建 (发布前必须)
npm run build:lib

# 开发构建验证
npm run build
```

### 4. CI 完整检查

```bash
# 运行完整 CI 流程
npm run ci
# 等价于: npm run lint:check && npm run type-check && npm run build:lib
```

## 发布前检查清单

### 自动化检查

- [ ] `npm run ci` 成功通过
- [ ] 测试覆盖率达到 90%
- [ ] 类型检查无错误
- [ ] ESLint 检查通过

### 手动检查

- [ ] 版本号已更新 (package.json)
- [ ] CHANGELOG 已更新
- [ ] README 文档已更新
- [ ] TypeScript 声明文件正确生成

## 错误处理指南

### 类型错误

1. 运行 `npm run type-check`
2. 修复所有类型问题
3. 确保严格模式兼容

### 测试失败

1. 运行 `npm run test:ui` 查看详情
2. 修复失败的测试用例
3. 确保覆盖率达标

### 构建失败

1. 检查 Vite 配置
2. 确认依赖项正确
3. 验证路径别名配置

### Lint 错误

1. 运行 `npm run lint` 自动修复
2. 手动修复无法自动修复的问题
3. 确保符合项目代码规范

## 性能验证

```bash
# 检查构建产物大小
npm run size:check

# 分析构建性能
npm run build:analyze
```

## Git 提交规范

- feat: 新功能
- fix: 错误修复
- docs: 文档更新
- style: 代码格式调整
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具变动
