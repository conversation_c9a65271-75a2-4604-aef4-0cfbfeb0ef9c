# HappyGrid 建议命令

## 开发命令

### 启动开发服务器

```bash
npm run dev
# 在端口 3200 启动开发服务器
```

### 构建命令

```bash
# 开发/演示构建
npm run build

# 库构建 (发布用)
npm run build:lib

# 分析构建
npm run build:analyze

# 清理并构建
npm run build:clean
```

### 测试命令

```bash
# 运行测试
npm run test

# 运行测试 UI
npm run test:ui

# 运行测试覆盖率 (要求 90% 覆盖率)
npm run test:coverage
```

### 代码质量命令

```bash
# 代码检查和修复
npm run lint

# 仅检查不修复
npm run lint:check

# 格式化代码
npm run format

# 检查格式
npm run format:check

# TypeScript 类型检查
npm run type-check
```

### CI/发布命令

```bash
# CI 完整检查 (类型检查 + lint + 构建)
npm run ci

# 发布前检查 (自动运行)
npm run prepublishOnly
```

### 工具命令

```bash
# 包大小分析
npm run size:check

# 依赖更新
npm run deps:update
```

## macOS 系统命令

- `ls` - 列出文件
- `find` - 查找文件
- `grep` - 搜索文本 (建议使用 `rg` ripgrep)
- `git` - 版本控制
- `cd` - 切换目录
