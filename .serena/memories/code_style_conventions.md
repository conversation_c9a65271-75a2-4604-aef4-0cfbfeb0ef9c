# HappyGrid 代码风格和约定

## TypeScript 配置

- **严格模式**: 启用 TypeScript 严格检查
- **目标**: ES2020
- **模块**: ESNext with bundler resolution
- **路径映射**: 使用 @ 前缀的别名

## ESLint 规则

- **基础**: Vue 3 + TypeScript + Prettier
- **引号**: 单引号 (`'single'`)
- **分号**: 不使用分号 (`semi: never`)
- **缩进**: 2 空格
- **逗号**: 不使用尾随逗号 (`comma-dangle: never`)
- **行长度**: 100 字符限制

## Vue 规范

- **组件命名**: PascalCase (如 `TableHeader`)
- **属性命名**: camelCase
- **事件命名**: camelCase
- **多词组件名**: 关闭 (允许如 `Table`)
- **Props**: 必须定义类型，建议默认值

## TypeScript 规则

- **未使用变量**: 错误级别，忽略下划线开头的参数
- **显式 any**: 警告级别
- **函数返回类型**: 可选
- **空断言**: 警告级别

## 文件组织

### 目录结构原则

- **组件**: 每个组件一个目录，包含 `.vue`、`.test.ts`、`index.ts`
- **类型**: 按功能模块分类到 `types/` 目录
- **工具**: 按功能分类到 `utils/` 目录
- **组合式API**: 所有在 `composables/` 目录

### 命名约定

- **文件名**: PascalCase (组件) 或 kebab-case (其他)
- **接口**: PascalCase，如 `TableConfig`
- **类型别名**: PascalCase
- **常量**: SCREAMING_SNAKE_CASE
- **函数**: camelCase

## 导入/导出

- **优先**: 命名导出而非默认导出
- **路径**: 使用路径别名 (`@/` 开头)
- **顺序**: 外部依赖 → 内部模块 → 类型导入

## 注释规范

- **JSDoc**: 公共API必须有完整文档
- **行内**: 解释复杂逻辑
- **TODO**: 使用 `// TODO:` 格式

## Vue Composition API

- **ref vs reactive**: 优先使用 `ref`
- **computed**: 纯计算属性
- **watch**: 明确指定依赖项
- **生命周期**: 使用组合式API钩子

## 测试约定

- **文件**: `.test.ts` 或 `.spec.ts` 后缀
- **覆盖率**: 90% 阈值要求
- **工具**: Vitest + Vue Test Utils
- **模拟**: 使用 `vi.mock()` 进行模块模拟
