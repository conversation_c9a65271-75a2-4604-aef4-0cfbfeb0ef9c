# HappyGrid 项目概览

## 项目目标

HappyGrid 是一个高性能的 Vue 3 表格组件库，专为 B2B 系统设计，具备完整的 TypeScript 支持。

## 核心技术栈

- **前端框架**: Vue 3 (Composition API)
- **编程语言**: TypeScript (严格模式)
- **样式框架**: Tailwind CSS 4 (使用 CSS 变量和自定义属性)
- **构建工具**: Vite (支持开发模式和库模式)
- **测试框架**: Vitest + @vue/test-utils + jsdom
- **代码质量**: ESLint + Prettier
- **图标系统**: Iconify (Lucide 和 MDI 图标集)

## 项目结构

```
src/
├── components/         # Vue 组件
│   ├── Table/         # 核心表格组件
│   ├── ThemeProvider/ # 主题提供者
│   ├── ThemeSwitcher/ # 主题切换器
│   └── Icon/          # 图标组件
├── composables/       # Vue 3 组合式 API
├── types/            # TypeScript 类型定义
├── utils/            # 工具函数
├── styles/           # 样式和主题
└── playground/       # 演示组件
```

## 核心特性

- **虚拟滚动**: 高效处理大数据集
- **键盘导航**: 完整的无障碍支持
- **内联编辑**: 单元格级别编辑
- **多列排序**: 支持自定义排序函数
- **高级过滤**: 正则表达式搜索和高亮
- **响应式设计**: 移动优先设计
- **数据导出**: CSV 和 Excel 导出
- **错误边界**: 优雅的错误处理
- **主题系统**: 3 种预设主题，支持完全自定义

## 构建系统

- **开发模式**: `npm run dev` - 运行在端口 3200
- **库模式**: `npm run build:lib` - 生成 ES 和 CommonJS 模块
- **类型声明**: 自动生成 TypeScript 声明文件

## 测试要求

- 覆盖率阈值: 90% (分支、函数、行、语句)
- 测试环境: jsdom
- 所有主要组件和组合式API都有专门的测试文件
