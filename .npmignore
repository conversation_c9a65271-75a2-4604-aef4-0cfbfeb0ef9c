# Development files
src/
tests/
docs/
playground/

# Configuration files
vite.config.ts
vitest.config.ts
tsconfig.json
tsconfig.build.json
tailwind.config.js
.eslintrc.*
.prettierrc.*
.gitignore

# Demo and test files
**/*.test.ts
**/*.spec.ts
**/*.test.tsx
**/*.spec.tsx
**/test/**
**/tests/**
src/main.ts
src/App.vue
src/*Demo.vue
src/gridDemo.vue
src/lightweightTest.vue
src/multiDemo.vue
src/simpleTest.vue
index.html

# Documentation and markdown files
*.md
!README.md
CHANGELOG.md
LICENSE.md

# Development dependencies
node_modules/
.npm/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts (keep only dist for distribution)
coverage/
*.tgz
.nyc_output/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache directories
.cache/
.parcel-cache/

# Temporary files
tmp/
temp/