import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'
import tailwindcss from '@tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'

  return {
    plugins: [
      vue(),
      tailwindcss(),
      ...(isLib
        ? [
            dts({
              include: ['src/**/*'],
              exclude: [
                'src/**/*.test.ts',
                'src/**/*.spec.ts',
                'src/main.ts',
                'src/App.vue',
                'src/*Demo.vue',
                'src/playground/**/*',
                'src/demo-views/**/*',
                'src/router/**/*'
              ],
              outDir: 'dist',
              copyDtsFiles: true,
              rollupTypes: false,
              staticImport: true
            })
          ]
        : [])
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@/components': resolve(__dirname, 'src/components'),
        '@/composables': resolve(__dirname, 'src/composables'),
        '@/types': resolve(__dirname, 'src/types'),
        '@/utils': resolve(__dirname, 'src/utils'),
        '@/styles': resolve(__dirname, 'src/styles')
      }
    },
    build: isLib
      ? {
          lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'HappyGrid',
            fileName: (format) => `index.${format === 'es' ? 'js' : 'cjs'}`,
            formats: ['es', 'cjs']
          },
          rollupOptions: {
            external: ['vue', '@iconify/vue', '@iconify/icons-lucide', '@iconify/icons-mdi'],
            output: {
              exports: 'named',
              globals: {
                vue: 'Vue',
                '@iconify/vue': 'IconifyVue',
                '@iconify/icons-lucide': 'IconifyIconsLucide',
                '@iconify/icons-mdi': 'IconifyIconsMdi'
              }
            },
            // 优化外部依赖处理
            treeshake: {
              moduleSideEffects: false,
              propertyReadSideEffects: false,
              tryCatchDeoptimization: false,
              // 更激进的tree-shaking
              unknownGlobalSideEffects: false
            }
          },
          cssCodeSplit: false,
          // 启用压缩和优化
          minify: 'esbuild',
          target: 'es2020',
          // 移除调试代码
          define: {
            __DEV__: false
          }
        }
      : undefined,
    test: {
      globals: true,
      environment: 'jsdom',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'html', 'lcov'],
        threshold: {
          global: {
            branches: 90,
            functions: 90,
            lines: 90,
            statements: 90
          }
        }
      }
    }
  }
})
