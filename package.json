{"name": "@happy-table/vue3", "version": "0.1.6", "description": "A high-performance Vue 3 table component for B2B systems with TypeScript support", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./style.css": "./dist/vue3.css"}, "files": ["dist"], "scripts": {"dev": "vite --port 3200", "build": "vite build", "build:lib": "npm run type-check && vite build --mode lib", "build:analyze": "vite build --mode lib -- --analyze", "build:clean": "rimraf dist && npm run build:lib", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --noEmit", "ci": "npm run lint:check && npm run type-check && npm run build:lib", "prepublishOnly": "npm run ci", "size:check": "npx vite-bundle-analyzer dist", "deps:update": "npx npm-check-updates -u"}, "dependencies": {"@iconify/icons-lucide": "^1.2.135", "@iconify/icons-mdi": "^1.2.48", "@iconify/vue": "^5.0.0", "@tailwindcss/vite": "^4.1.12", "@vitest/coverage-v8": "^3.2.4", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.34.0", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/ui": "^3.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-vue": "^10.4.0", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0", "vite": "^7.1.3", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4", "vue-tsc": "^3.0.6"}, "keywords": ["vue", "vue3", "table", "grid", "component", "typescript", "b2b", "data-table", "virtual-scroll", "tailwind"], "author": "<EMAIL>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/kaizhoumasha/happyGrid.git"}, "bugs": {"url": "https://github.com/kaizhoumasha/happyGrid/issues"}, "homepage": "https://github.com/kaizhoumasha/happyGrid#readme"}