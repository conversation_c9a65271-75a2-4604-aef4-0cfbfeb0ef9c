# **表格固定列功能技术设计文档**

| 版本 | 日期 | 作者 | 变更描述 |
| :---- | :---- | :---- | :---- |
| 1.0 | 2025-09-01 | Gemini | 初稿，定义了基于 Intersection Observer 的方案 |

## **1\. 引言**

### **1.1. 背景**

在现代数据驱动的应用中，数据表格是信息呈现的核心组件。当表格列数过多导致需要水平滚动时，关键信息列（如 ID、名称、操作项）会移出可视区域，影响用户的数据比对和操作效率。为了优化用户体验，需要在水平滚动时将这些关键列固定在表格的左侧或右侧。

### **1.2. 目标**

本文档旨在设计并实现一个高性能、体验良好、易于维护的表格固定列功能。该功能应满足以下要求：

* 支持左侧单列或多列固定。  
* 支持右侧单列或多列固定。  
* 支持左右两侧同时固定。  
* 支持列宽不固定的场景。  
* 在滚动发生时，为固定列与滚动区域的边界提供清晰的视觉提示（如阴影），且该提示仅在“激活”状态下显示。  
* 方案应具备良好的性能，避免因高频事件监听导致页面卡顿。

### **1.3. 术语定义**

* **固定列 (Fixed Column)**: 在表格容器发生水平滚动时，其位置相对于容器边缘保持不变的列。  
* **临界列 (Boundary Column)**: 左侧固定列中最靠右的一列，或右侧固定列中最靠左的一列。视觉提示（阴影）将应用在这些列上。  
* **激活状态 (Active State)**: 表格发生水平滚动，导致固定列与滚动内容产生视觉分离的状态。  
* **哨兵 (Sentinel)**: 一个置于滚动区域边界的、用于被 Intersection Observer 监视的零尺寸 HTML 元素。

## **2\. 技术方案选型**

我们评估了以下三种主流技术方案：

| 方案 | 描述 | 优点 | 缺点 |
| :---- | :---- | :---- | :---- |
| **方案一：纯 CSS position: sticky** | 使用 CSS 的 position: sticky 属性将单元格（\<th\>, \<td\>）固定在父滚动容器的边缘。 | 1\. 实现简单，代码量少。\<br\>2. 浏览器原生支持，性能极高。 | 1\. 无法动态计算多列固定的 left/right 偏移，难以支持不固定列宽。\<br\>2. 无法仅在滚动时显示阴影等视觉提示，交互体验不佳。 |
| **方案二：JS \+ scroll 事件** | 通过监听容器的 scroll 事件，实时计算 scrollLeft 来判断滚动状态，并动态添加/移除 CSS 类。 | 1\. 逻辑直观，控制精确。\<br\>2. 可以完美实现所有功能需求。 | 1\. scroll 事件触发频率极高，在复杂页面或大数据量表格中可能引发性能问题，导致滚动卡顿。 |
| **方案三：position: sticky \+ Intersection Observer (选定方案)** | **结合方案一和方案二的优点。使用 position: sticky 负责布局和固定，使用 Intersection Observer API 负责高效地监测滚动状态。** | 1\. **高性能**：Intersection Observer 只在目标元素进入/离开视口时触发，避免了 scroll 事件的性能瓶颈。\<br\>2. **功能完备**：可以精确地在滚动开始和结束时切换样式。\<br\>3. **代码解耦**：布局（CSS）和逻辑（JS）分离，更易维护。 | 1\. 实现逻辑比 scroll 事件略复杂。\<br\>2. 需要处理 Intersection Observer 的浏览器兼容性（现代浏览器已全面支持）。 |

### **2.1. 选定理由**

方案三（position: sticky \+ Intersection Observer）在实现全部功能需求的同时，提供了最佳的性能表现，是构建流畅、响应迅速的现代 Web 应用的最佳实践。因此，我们选定此方案作为最终实现。

## **3\. 详细设计**

### **3.1. HTML 结构**

为了实现功能，我们需要一个清晰的 HTML 结构。

1. **容器**: 表格必须被一个设置了 overflow-x: auto 的 div 容器包裹。  
2. **CSS 类**:  
   * 为所有左侧固定的单元格添加 .sticky-left 类。  
   * 为所有右侧固定的单元格添加 .sticky-right 类。  
3. **哨兵元素**:  
   * 在第一个**非固定列**的表头 (\<th\>) 内放置一个左侧哨兵 \<div class="sentinel-left"\>\</div\>。  
   * 在最后一个**非固定列**的表头 (\<th\>) 内放置一个右侧哨兵 \<div class="sentinel-right"\>\</div\>。

\<div class="table-container"\>  
  \<table\>  
    \<thead\>  
      \<tr\>  
        \<\!-- Left Sticky Columns \--\>  
        \<th class="sticky-left"\>ID\</th\>  
        \<th class="sticky-left"\>姓名\</th\>  
          
        \<\!-- Scrollable Area \--\>  
        \<th\>  
          \<div class="sentinel-left"\>\</div\> \<\!-- Left Sentinel \--\>  
          部门  
        \</th\>  
        \<th\>...\</th\>  
        \<th\>  
          更新时间  
          \<div class="sentinel-right"\>\</div\> \<\!-- Right Sentinel \--\>  
        \</th\>  
          
        \<\!-- Right Sticky Columns \--\>  
        \<th class="sticky-right"\>状态\</th\>  
        \<th class="sticky-right"\>操作\</th\>  
      \</tr\>  
    \</thead\>  
    \<tbody\>  
      \<\!-- Table rows with corresponding .sticky-left and .sticky-right classes on tds \--\>  
    \</tbody\>  
  \</table\>  
\</div\>

### **3.2. CSS 实现**

CSS 负责布局和最终的视觉样式。

/\* 1\. 容器样式 \*/  
.table-container {  
  width: 100%;  
  overflow-x: auto; /\* 启用水平滚动 \*/  
}

table {  
  border-collapse: collapse;  
}

th, td {  
  /\* 必须设置背景色，否则滚动时下方内容会透过来 \*/  
  background-color: \#fff;  
}

/\* 2\. 固定列基础样式 \*/  
.sticky-left,  
.sticky-right {  
  position: \-webkit-sticky;  
  position: sticky;  
  z-index: 2; /\* 确保固定列在滚动内容的上方 \*/  
}

/\* 3\. 临界列的阴影样式（激活状态） \*/  
/\* 左侧临界列：即最后一个 .sticky-left \*/  
.sticky-left:last-of-type.is-scrolling {  
  box-shadow: 5px 0 8px \-4px rgba(0, 0, 0, 0.2);  
  transition: box-shadow 0.2s ease-in-out;  
}

/\* 右侧临界列：即第一个 .sticky-right \*/  
.sticky-right:first-of-type.is-scrolling {  
  box-shadow: \-5px 0 8px \-4px rgba(0, 0, 0, 0.2);  
  transition: box-shadow 0.2s ease-in-out;  
}

/\* 4\. 哨兵样式 \*/  
.sentinel-left,  
.sentinel-right {  
  width: 1px;  
  height: 1px;  
}

### **3.3. JavaScript 实现**

JavaScript 负责动态计算偏移量和监测滚动状态。

#### **步骤 1: 初始化固定列位置**

此函数用于处理不固定列宽的场景，它会动态计算每个固定列的 left 和 right 偏移值。

function initializeStickyColumns() {  
  const table \= document.querySelector('.table-container table');  
  if (\!table) return;

  // 处理左侧固定列  
  let leftOffset \= 0;  
  const leftStickyCells \= table.querySelectorAll('.sticky-left');  
  leftStickyCells.forEach(cell \=\> {  
    cell.style.left \= \`${leftOffset}px\`;  
    leftOffset \+= cell.offsetWidth;  
  });

  // 处理右侧固定列  
  let rightOffset \= 0;  
  const rightStickyCells \= Array.from(table.querySelectorAll('.sticky-right')).reverse();  
  rightStickyCells.forEach(cell \=\> {  
    cell.style.right \= \`${rightOffset}px\`;  
    rightOffset \+= cell.offsetWidth;  
  });  
}

该函数应在页面加载完成时和窗口大小变化 (resize) 时调用。

#### **步骤 2: 使用 Intersection Observer 监测状态**

我们创建两个观察者，分别监视左右两个哨兵元素。

function setupScrollObservers() {  
  const container \= document.querySelector('.table-container');  
  if (\!container) return;

  const leftBoundary \= container.querySelector('.sticky-left:last-of-type');  
  const rightBoundary \= container.querySelector('.sticky-right:first-of-type');  
  const leftSentinel \= container.querySelector('.sentinel-left');  
  const rightSentinel \= container.querySelector('.sentinel-right');

  const observerOptions \= {  
    root: container, // 在容器内部观察  
    threshold: 1.0,  // 元素完全可见时触发  
  };

  // 左侧观察者  
  if (leftBoundary && leftSentinel) {  
    const leftObserver \= new IntersectionObserver((\[entry\]) \=\> {  
      // 当左侧哨兵不可见时（isIntersecting: false），说明向左滚动了  
      // 此时为左侧临界列添加 is-scrolling 类  
      leftBoundary.classList.toggle('is-scrolling', \!entry.isIntersecting);  
    }, observerOptions);  
    leftObserver.observe(leftSentinel);  
  }

  // 右侧观察者  
  if (rightBoundary && rightSentinel) {  
    const rightObserver \= new IntersectionObserver((\[entry\]) \=\> {  
      // 当右侧哨兵不可见时，说明滚动条未在最右端  
      // 此时为右侧临界列添加 is-scrolling 类  
      rightBoundary.classList.toggle('is-scrolling', \!entry.isIntersecting);  
    }, observerOptions);  
    rightObserver.observe(rightSentinel);  
  }  
}

该函数应在页面加载完成时调用一次。

#### **步骤 3: 整合**

document.addEventListener('DOMContentLoaded', () \=\> {  
  initializeStickyColumns();  
  setupScrollObservers();

  window.addEventListener('resize', initializeStickyColumns);  
});

## **4\. 性能与兼容性**

### **4.1. 性能**

本方案核心优势在于性能。Intersection Observer 是异步执行的，且不会在主线程上被高频触发，因此即便是对于包含大量数据和复杂 DOM 结构的表格，也能确保滚动的流畅性，不会造成页面掉帧或卡顿。

### **4.2. 浏览器兼容性**

* **position: sticky**: 主流现代浏览器（Chrome, Firefox, Safari, Edge）均支持。不支持 IE11。  
* **Intersection Observer**: 主流现代浏览器均支持。不支持 IE11。

对于需要支持 IE11 的遗留项目，本方案不适用，需要降级为传统的、基于 scroll 事件并手动计算 top, left 的 JS 方案。但对于现代 Web 应用，该方案兼容性良好。

## **5\. 总结**

本设计文档提出的 **CSS position: sticky \+ Intersection Observer** 组合方案，是一个现代、高效且体验优秀的表格固定列解决方案。它成功地将布局的复杂性交给了浏览器原生 CSS 处理，同时利用高效的 Intersection Observer API 来精确控制交互状态，是当前该类需求的最佳工程实践。