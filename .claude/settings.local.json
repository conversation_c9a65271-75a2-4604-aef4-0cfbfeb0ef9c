{"permissions": {"allow": ["mcp__serena__search_for_pattern", "mcp__serena__list_dir", "mcp__sequential-thinking__sequentialthinking", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_hover", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_drag", "mcp__playwright__browser_close", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_snapshot", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__find_file", "mcp__serena__get_symbols_overview", "mcp__serena__get_current_config", "mcp__serena__switch_modes", "mcp__serena__write_memory", "mcp__serena__think_about_collected_information", "mcp__serena__list_memories", "mcp__serena__read_memory", "mcp__serena__restart_language_server", "mcp__serena__activate_project", "mcp__serena__find_symbol", "mcp__playwright__browser_press_key", "mcp__fetch__imageFetch", "mcp__serena__replace_symbol_body", "mcp__serena__replace_regex", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__summarize_changes", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm run test:*)", "Bash(npm run lint:*)", "Bash(npm run type-check:*)", "Bash(npm run build:lib:*)", "Bash(npx vue-tsc:*)", "<PERSON><PERSON>(cat:*)", "Bash(npx vitest:*)", "Bash(npm run dev)", "mcp__playwright__browser_resize", "Bash(grep:*)", "Bash(find:*)", "mcp__serena__think_about_task_adherence", "Bash(pnpm run:*)", "mcp__serena__insert_after_symbol", "WebSearch", "mcp__playwright__browser_select_option", "Bash(node:*)", "mcp__playwright__browser_wait_for", "<PERSON><PERSON>(sed:*)", "mcp__playwright__browser_install", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "mcp__metaso__metaso_web_search", "Bash(npm test:*)"], "deny": [], "ask": []}}