# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a high-performance Vue 3 table component library for B2B systems with TypeScript support. The project uses Vue 3, TypeScript, Tailwind CSS 4, and Vite for development and build tooling.

## Development Commands

```bash
# Start development server (runs on port 3200)
npm run dev

# Build for production (demo/development)
npm run build

# Build library for distribution
npm run build:lib

# Run tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests with coverage (90% threshold required)
npm run test:coverage

# Run a single test file
npx vitest src/components/Table/__tests__/Table.test.ts

# Lint and fix code
npm run lint

# Check linting without fixing
npm run lint:check

# Format code
npm run format

# Check formatting
npm run format:check

# TypeScript type checking
npm run type-check

# Build with bundle analysis
npm run build:analyze

# Clean build (removes dist and rebuilds)
npm run build:clean

# Check bundle size
npm run size:check

# Run full CI pipeline (lint, type-check, build)
npm run ci
```

## Project Architecture

### Core Components Structure

- **Table.vue**: Main table component with error boundaries, loading states, and keyboard navigation
- **TableHeader.vue**, **TableBody.vue**, **TableRow.vue**, **TableCell.vue**: Modular table sub-components
- **TablePagination.vue**: Pagination component with configurable options
- **ThemeProvider.vue**: Theme context provider for theme system

### Composables Pattern

The project uses Vue 3 composables for reusable logic:

- **useTheme**: Theme management with localStorage persistence
- **useSorting**: Multi-column sorting with custom sort functions
- **useFiltering**: Text filtering with regex support and highlighting
- **usePagination**: Pagination state management
- **Virtual Scrolling Module** (`/src/composables/virtual/`):
  - **useVirtual**: Unified virtual scrolling (vertical + horizontal)
  - **useVerticalVirtual**: Optimized vertical virtual scrolling
  - **useHorizontalVirtual**: Column-level horizontal virtual scrolling
  - **useVirtualMetrics**: Performance monitoring and FPS tracking
  - **useVirtualCache**: Predictive style caching and dimension management

### Type System

Comprehensive TypeScript types are defined in `/src/types/`:

- **table.ts**: Core table configuration types
- **theme.ts**: Theme system types
- **events.ts**: Event handling types
- **slots.ts**: Slot system types

### Theme System

Built on CSS variables with Tailwind CSS 4:

- Supports 3 preset themes: Default Light, Dark, Enterprise Blue
- CSS custom properties for full customization
- Smooth theme transitions with animation support
- WCAG-compliant accessibility features

## Build System

The project uses Vite with dual build modes:

- **Development mode**: Regular Vue app build for demos and testing
- **Library mode** (`npm run build:lib`): Creates distributable ES and CommonJS modules with TypeScript declarations

### Path Aliases

```typescript
'@': './src'
'@/components': './src/components'
'@/composables': './src/composables'
'@/types': './src/types'
'@/utils': './src/utils'
'@/styles': './src/styles'
'@/src/demo-views': './src/demo-views'
```

## Testing

Uses Vitest with comprehensive coverage requirements:

- **Coverage threshold**: 90% for branches, functions, lines, and statements
- **Test environment**: jsdom for DOM testing
- **Test utilities**: @vue/test-utils for Vue component testing
- **Global test setup**: Configured with globals enabled for describe/it/expect
- **Test organization**: All test files are organized in `__tests__` subdirectories following Vitest best practices
- All major components and composables have dedicated test files
- Coverage reports generated in text, HTML, and LCOV formats

### Test File Structure

Following Vitest best practices, all test files are organized in `__tests__` subdirectories:

```
src/
├── components/
│   ├── Icon/__tests__/
│   ├── Table/__tests__/
│   └── ThemeProvider/__tests__/
├── composables/__tests__/
└── utils/__tests__/
```

This structure provides:
- Clear separation between source code and tests
- Better organization for large test suites
- Easier navigation and maintenance
- Automatic test discovery by Vitest

## Key Features Implementation

- **Dual-axis Virtual scrolling**:
  - **Vertical**: Handles large datasets efficiently (40+ rows threshold)
  - **Horizontal**: Column-level virtualization for wide tables (20+ columns threshold)
  - **Performance optimizations**: Predictive caching, dynamic buffering, scroll velocity adaptation
  - **Smart thresholds**: Auto-enable based on data size (similar to VXE Table's gt parameter)
- **Keyboard navigation**: Full accessibility support with arrow keys and shortcuts
- **Inline editing**: Cell-level editing capabilities
- **Multi-column sorting**: With custom sort function support
- **Advanced filtering**: Text search with regex and highlighting
- **Responsive design**: Mobile-first approach with configurable breakpoints
- **Data export**: CSV and Excel export capabilities
- **Error boundaries**: Graceful error handling with retry mechanisms

## Code Quality Standards

- TypeScript strict mode enabled
- ESLint + Prettier for code consistency
- Comprehensive unit tests required for all new features
- CSS-in-JS approach avoided in favor of Tailwind CSS utilities
- Component props use comprehensive TypeScript interfaces

## Performance Requirements & Solutions

### Core Performance Principles

1. **Frame-Perfect Rendering**: All UI updates must sync with browser refresh rate (60fps)
2. **Minimal Reflows**: Batch DOM operations and use CSS transforms where possible
3. **Event Throttling**: Heavy operations must be throttled appropriately
4. **Memory Efficiency**: Clean up resources properly to prevent memory leaks

### Implementation Guidelines

#### Scroll Event Handling
- **MUST use `requestAnimationFrame`** for scroll event processing
- **NEVER use `setTimeout`** for animation or scroll-related updates
- Implement throttling with RAF to prevent excessive updates per frame
```typescript
// ✅ Correct approach
let rafId: number | null = null
const handleScroll = () => {
  if (rafId !== null) return
  rafId = requestAnimationFrame(() => {
    // Update logic here
    rafId = null
  })
}

// ❌ Avoid this
setTimeout(() => { /* update */ }, 50)
```

#### Fixed Column Shadow Behavior
- **Immediate Response**: Shadows must appear immediately when `scrollLeft > 0`
- **No Delay**: Use direct scroll position checks, not just sentinel visibility
- **Smooth Transitions**: CSS transitions for shadow appearance/disappearance

#### Resource Management
- **Cleanup on Unmount**: Cancel all pending RAF, remove event listeners
- **Passive Listeners**: Use `{ passive: true }` for scroll/touch events
- **RAF Cleanup Pattern**:
```typescript
// Store RAF IDs for cleanup
let scrollRafId: number | null = null

// Cleanup function
const cleanup = () => {
  if (scrollRafId !== null) {
    cancelAnimationFrame(scrollRafId)
    scrollRafId = null
  }
}
```

#### Initialization Timing
- **DOM Ready**: Wait 2-3 frames after mount for stable layout
- **Use RAF instead of setTimeout** for initialization delays
```typescript
// ✅ Correct initialization
let frameCount = 0
const init = () => {
  frameCount++
  if (frameCount >= 2) {
    // DOM is stable, initialize
  } else {
    requestAnimationFrame(init)
  }
}
requestAnimationFrame(init)
```

### Performance Monitoring

- Track FPS for virtual scrolling operations
- Monitor cache hit rates for style calculations
- Measure actual scroll responsiveness
- Use Chrome DevTools Performance tab for profiling

## Virtual Scrolling Configuration

### Basic Setup

```typescript
const tableConfig: TableConfig = {
  // ... other config
  virtual: {
    // Vertical virtual scrolling (legacy compatibility)
    enabled: true,
    threshold: 40, // Enable when rows > 40
    itemHeight: 48, // Fixed row height in px
    bufferSize: 10, // Buffer rows before/after visible area
    overscan: 5, // Pre-render rows for smooth scrolling
    adaptive: true, // Auto-enable based on data size

    // Horizontal virtual scrolling (NEW)
    horizontal: {
      enabled: true,
      threshold: 20, // Enable when columns > 20
      columnWidth: 'auto', // 'auto' | number
      bufferColumns: 5, // Buffer columns before/after visible area
      overscanColumns: 3, // Pre-render columns for smooth scrolling
      enableFixedColumns: true, // Support fixed left/right columns
      adaptiveColumnWidth: false // Dynamic column width calculation
    }
  }
}
```

### Performance Monitoring

Access real-time virtual scrolling metrics:

```typescript
// Get performance metrics
const metrics = tableRef.value?.getVirtualMetrics()
const horizontalMetrics = tableRef.value?.getHorizontalMetrics()

console.log('Vertical scrolling FPS:', metrics.scrollFPS)
console.log('Horizontal scrolling FPS:', horizontalMetrics.horizontalScrollFPS)
console.log('Cache hit rate:', metrics.cacheHitRate + '%')
```

### Advanced Methods

```typescript
// Vertical scrolling
tableRef.value?.scrollToIndex(100) // Scroll to specific row
tableRef.value?.scrollToTop() // Scroll to top

// Horizontal scrolling
tableRef.value?.scrollToColumn(5) // Scroll to specific column
tableRef.value?.scrollToLeft() // Scroll to leftmost
tableRef.value?.getColumnWidth(3) // Get column width
tableRef.value?.getTotalWidth() // Get total table width
```
