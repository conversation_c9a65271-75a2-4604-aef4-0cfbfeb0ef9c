# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
dist-umd
*.local

claudedocs/*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.playwright-mcp
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
coverage
.nyc_output

# Environment variables
.env
.env.local
.env.*.local

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml

# Build artifacts
*.tsbuildinfo

# Temporary files
.tmp
.temp