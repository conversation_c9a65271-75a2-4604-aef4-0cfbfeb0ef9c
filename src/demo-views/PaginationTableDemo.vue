<script setup lang="ts">
import VueTable from '../components/Table/Table.vue'
import type { TableConfig } from '../types'
import { paginationTableData } from './mock_data'

// 分页表格配置
const paginationTableConfig: TableConfig = {
  columns: [
    { key: 'order', title: '订单号', width: 150, sortable: true },
    { key: 'customer', title: '客户', width: 120, sortable: true },
    { key: 'amount', title: '金额', width: 120, align: 'right', sortable: true },
    { key: 'date', title: '日期', width: 150, sortable: true },
    { key: 'status', title: '状态', width: 100, align: 'center' }
  ],
  data: paginationTableData,
  pagination: {
    enabled: true,
    pageSize: 3,
    currentPage: 1,
    total: 5
  },
  border: true,
  stripe: { enabled: true, type: "default" },
  hover: 'row',
  size: 'medium'
}
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h2>分页表格演示</h2>
      <p class="demo-description">带有分页功能的表格，适用于大数据量场景</p>
    </div>

    <div class="demo-content">
      <VueTable :config="paginationTableConfig" />
    </div>

    <div class="demo-features">
      <h3>功能特性：</h3>
      <ul>
        <li>✅ 分页导航</li>
        <li>✅ 每页显示条数配置</li>
        <li>✅ 总数据量显示</li>
        <li>✅ 当前页状态</li>
        <li>✅ 订单数据管理</li>
        <li>✅ 状态标识</li>
        <li>✅ 金额数据格式化</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.demo-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-header h2 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.demo-description {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.demo-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features h3 {
  color: #1f2937;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.demo-features ul {
  margin: 0;
  padding-left: 1.5rem;
}

.demo-features li {
  margin-bottom: 0.5rem;
  color: #059669;
  font-weight: 500;
}

@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }

  .demo-content,
  .demo-features {
    padding: 1rem;
  }
}
</style>
