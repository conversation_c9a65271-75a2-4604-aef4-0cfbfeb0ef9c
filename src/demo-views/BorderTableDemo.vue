<script setup lang="ts">
import VueTable from '../components/Table/Table.vue'
import type { TableConfig } from '../types'
import { borderTableData } from './mock_data'

// 示例数据
const sampleData = borderTableData

const columns = [
  { key: 'seq', type: 'selection' as const, title: '序号', width: 60, fixed: 'left' as const },
  // { key: 'id', title: 'ID', width: 80, sortable: true, fixed: 'left' as const },
  { key: 'name', title: '姓名', width: 120, sortable: true, fixed: 'left' as const },
  { key: 'age', title: '年龄', width: 100, align: 'center' as const, sortable: true },
  { key: 'email', title: '邮箱地址', width: 200, sortable: true },
  { key: 'phone', title: '联系电话', width: 150, sortable: true },
  { key: 'department', title: '所属部门', width: 150, sortable: true },
  { key: 'position', title: '职位名称', width: 150, sortable: true },
  { key: 'level', title: '职级', width: 100, align: 'center' as const, sortable: true },
  { key: 'salary', title: '月薪', width: 120, align: 'right' as const, sortable: true },
  { key: 'bonus', title: '年终奖', width: 120, align: 'right' as const, sortable: true },
  { key: 'hireDate', title: '入职日期', width: 150, sortable: true },
  { key: 'workYears', title: '工作年限', width: 100, align: 'center' as const, sortable: true },
  { key: 'education', title: '学历', width: 100, align: 'center' as const, sortable: true },
  { key: 'address', title: '家庭住址', width: 260, sortable: true },
  { key: 'emergencyContact', title: '紧急联系人', width: 200, sortable: true },
  // { key: 'status', title: '状态', width: 100, align: 'center' as const, fixed: 'right' as const },
  { key: 'actions', title: '操作', width: 150, align: 'center' as const, fixed: 'right' as const }
]

// 默认边框（false，仅外边框）- 启用拖拽功能
const defaultBorderConfig: TableConfig = {
  columns,
  data: sampleData,
  stripe: { enabled: true, type: 'default' },
  hover: 'row',
  size: 'medium',
  dragDrop: { 
    enabled: true,
    allowSpecialColumnDrag: false, // 禁止拖拽特殊列
    disabledColumnTypes: ['seq', 'selection']
  }
}

// 完整边框 - 禁用拖拽功能
const fullBorderConfig: TableConfig = {
  columns,
  data: sampleData,
  border: 'full',
  stripe: { enabled: true, type: 'default' },
  hover: 'row',
  size: 'medium',
  dragDrop: { enabled: false } // 完全禁用拖拽
}

// 仅外边框
const outerBorderConfig: TableConfig = {
  columns,
  data: sampleData,
  stripe: { enabled: true, type: 'default' },
  border: 'outer',
  hover: 'none',
  size: 'medium'
}

// 仅内边框
const innerBorderConfig: TableConfig = {
  columns,
  data: sampleData,
  border: 'inner',
  stripe: { enabled: true, type: 'default' },
  hover: 'row',
  size: 'medium'
}

// 无边框 - 允许拖拽特殊列
const noneBorderConfig: TableConfig = {
  columns,
  data: sampleData,
  border: 'none',
  stripe: { enabled: true, type: 'default' },
  hover: 'row',
  size: 'medium',
  dragDrop: { 
    enabled: true,
    allowSpecialColumnDrag: true, // 允许拖拽特殊列
    disabledColumnTypes: []
  }
}
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h2>边框配置演示</h2>
      <p class="demo-description">参考 VXE Table 的边框配置项，支持多种边框样式</p>
    </div>

    <div class="demo-content">
      <!-- 默认边框（false） -->
      <div class="demo-section">
        <h3>默认边框 (default/false)</h3>
        <p class="section-description">仅显示外边框，这是默认配置。✅ 启用拖拽，但禁止拖拽 seq/selection 列</p>
        <VueTable :config="defaultBorderConfig" />
      </div>

      <!-- 完整边框 -->
      <div class="demo-section">
        <h3>完整边框 (border="full")</h3>
        <p class="section-description">显示外边框和所有内部边框。❌ 完全禁用拖拽功能</p>
        <VueTable :config="fullBorderConfig" />
      </div>

      <!-- 仅外边框 -->
      <div class="demo-section">
        <h3>仅外边框 (border="outer")</h3>
        <p class="section-description">只显示表格的外边框，无内部边框</p>
        <VueTable :config="outerBorderConfig" />
      </div>

      <!-- 仅内边框 -->
      <div class="demo-section">
        <h3>仅内边框 (border="inner")</h3>
        <p class="section-description">只显示内部边框，无外边框</p>
        <VueTable :config="innerBorderConfig" />
      </div>

      <!-- 无边框 -->
      <div class="demo-section">
        <h3>无边框 (border="none")</h3>
        <p class="section-description">不显示任何边框。✅ 启用拖拽，允许拖拽所有列（包括特殊列）</p>
        <VueTable :config="noneBorderConfig" />
      </div>
    </div>

    <div class="demo-features">
      <h3>边框配置说明：</h3>
      <ul>
        <li>
          <code>border: false</code> 或 <code>border: "default"</code> - 默认配置，仅显示外边框
        </li>
        <li><code>border: "full"</code> - 完整边框，显示外边框和所有内部边框</li>
        <li><code>border: "outer"</code> - 仅外边框，不显示内部边框</li>
        <li><code>border: "inner"</code> - 仅内边框，不显示外边框</li>
        <li><code>border: "none"</code> - 无边框，清爽风格</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
@reference "@/styles/base.css";

.demo-page {
  @apply p-6 max-w-6xl mx-auto;
}

.demo-header {
  @apply mb-8 text-center;
}

.demo-header h2 {
  @apply text-3xl font-bold text-gray-800 mb-2;
}

.demo-description {
  @apply text-gray-600 text-lg;
}

.demo-content {
  @apply space-y-8;
}

.demo-section {
  @apply space-y-4;
}

.demo-section h3 {
  @apply text-xl font-semibold text-gray-800;
}

.section-description {
  @apply text-gray-600 text-sm mb-4;
}

.demo-features {
  @apply mt-8 p-6 bg-gray-50 rounded-lg;
}

.demo-features h3 {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.demo-features ul {
  @apply space-y-2;
}

.demo-features li {
  @apply text-gray-700;
}

.demo-features code {
  @apply bg-gray-200 px-2 py-1 rounded text-sm font-mono;
}
</style>
