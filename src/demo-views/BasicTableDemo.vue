<script setup lang="ts">
import { h } from 'vue'
import VueTable from '../components/Table/Table.vue'
import type { TableConfig } from '../types'
import { basicTableData } from './mock_data'

// 基础表格配置
const basicTableConfig: TableConfig = {
  columns: [
    {
      key: 'name',
      title: '姓名',
      width: 150,
      sortable: true,
      fixed: 'left',
      resizable: true,
      render: ({ value }) => h('span', { style: { color: 'red' } }, String(value))
    },
    {
      key: 'age',
      title: '年龄',
      width: 100,
      align: 'center',
      sortable: true,
      resizable: true,
      template: '📍 {{value}}'
    },
    { key: 'email', title: '邮箱', width: 250, sortable: true, resizable: true },
    { key: 'phone', title: '电话', width: 150, sortable: true, resizable: true },
    { key: 'department', title: '部门', width: 150, sortable: true, resizable: false },
    { key: 'position', title: '职位', width: 150, sortable: true, resizable: true },
    { key: 'salary', title: '薪资', width: 120, align: 'right', sortable: true, resizable: true },
    { key: 'hireDate', title: '入职日期', width: 150, sortable: true, resizable: true },
    { key: 'status', title: '状态', width: 100, align: 'center', fixed: 'right', resizable: true },
    { key: 'actions', title: '操作', width: 120, align: 'center', fixed: 'right', resizable: true }
  ],
  data: basicTableData,
  border: false,
  stripe: { enabled: true, type: "default" },
  hover: 'row',
  size: 'medium',
  height: '240px',
  width: '100%'
}
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h2>基础表格演示</h2>
      <p class="demo-description">基本的表格功能演示，包含固定列、自定义渲染、排序等功能</p>
    </div>

    <div class="demo-content">
      <VueTable :config="basicTableConfig" />
    </div>

    <div class="demo-features">
      <h3>功能特性：</h3>
      <ul>
        <li>✅ 左右固定列</li>
        <li>✅ 自定义渲染函数</li>
        <li>✅ 模板字符串渲染</li>
        <li>✅ 列宽度调整</li>
        <li>✅ 排序功能</li>
        <li>✅ 斑马纹样式</li>
        <li>✅ 悬停高亮</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.demo-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-header h2 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.demo-description {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.demo-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features h3 {
  color: #1f2937;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.demo-features ul {
  margin: 0;
  padding-left: 1.5rem;
}

.demo-features li {
  margin-bottom: 0.5rem;
  color: #059669;
  font-weight: 500;
}

@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }

  .demo-content,
  .demo-features {
    padding: 1rem;
  }
}
</style>
