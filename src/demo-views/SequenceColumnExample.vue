<template>
  <div class="sequence-column-examples">
    <h1 class="text-2xl font-bold mb-6">序号列功能演示</h1>

    <!-- 基础序号列示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">1. 基础序号列</h2>
      <p class="text-gray-600 mb-4">参考 VXE Table 的配置方式，使用 type: 'seq'</p>
      <Table :config="basicConfig" />
    </section>

    <!-- 自定义起始序号示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">2. 自定义起始序号</h2>
      <p class="text-gray-600 mb-4">使用 seqConfig.startIndex 设置起始序号</p>
      <Table :config="startIndexConfig" />
    </section>

    <!-- 自定义格式化示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">3. 自定义格式化</h2>
      <p class="text-gray-600 mb-4">使用 seqConfig.format 自定义序号显示格式</p>
      <Table :config="formatConfig" />
    </section>

    <!-- 分页模式示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">4. 分页模式</h2>
      <p class="text-gray-600 mb-4">序号在分页模式下会自动计算跨页序号</p>
      <Table :config="paginationConfig" />
    </section>

    <!-- 固定列示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">5. 固定序号列</h2>
      <p class="text-gray-600 mb-4">序号列默认固定在左侧，也可以手动设置为右侧或不固定</p>
      <Table :config="fixedConfig" />
    </section>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import Table from '@/components/Table/Table.vue'
import type { TableConfig, TableRow } from '@/types'
import { sequenceLargeData, sequenceSampleData } from './mock_data'

// 示例数据
const sampleData: TableRow[] = sequenceSampleData

// 更多数据用于分页示例
const largeData: TableRow[] = sequenceLargeData

// 1. 基础序号列配置
const basicConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'seq',
      title: '#',
      type: 'seq'
      // width 将自动设置为 60px（序号列默认）
    },
    { key: 'name', title: 'Name' }, // width 将自动计算为约 124px
    { key: 'age', title: 'Age' }, // width 将自动计算为 108px
    { key: 'department', title: 'Department' } // width 将自动计算为约 172px
  ],
  data: sampleData
})

// 2. 自定义起始序号配置
const startIndexConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'seq',
      title: '序号',
      type: 'seq',
      width: 80, // 自定义序号列宽度为 80px
      seqConfig: {
        startIndex: 10
      }
    },
    { key: 'name', title: '姓名' }, // width 将自动计算
    { key: 'department', title: '部门' }, // width 将自动计算
    { key: 'email', title: '邮箱' } // width 将自动计算
  ],
  data: sampleData
})

// 3. 自定义格式化配置
const formatConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'seq',
      title: '编号',
      type: 'seq',
      width: 100, // 格式化序号需要更宽，设置为 100px
      seqConfig: {
        format: (index: number) => `No.${index.toString().padStart(3, '0')}`
      }
    },
    { key: 'name', title: '姓名' }, // width 将自动计算
    { key: 'age', title: '年龄' }, // width 将自动计算
    { key: 'department', title: '部门' } // width 将自动计算
  ],
  data: sampleData
})

// 4. 分页模式配置
const paginationConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'seq',
      title: '序号',
      type: 'seq'
      // width 将自动设置为 60px（序号列默认）
    },
    { key: 'name', title: '姓名' }, // width 将自动计算
    { key: 'email', title: '邮箱', width: 200 }, // width 将自动计算
    { key: 'department', title: '部门' } // width 将自动计算
  ],
  data: largeData,
  pagination: {
    enabled: true,
    pageSize: 5,
    currentPage: 1,
    total: largeData.length,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: true
  }
})

// 5. 固定列配置
const fixedConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'seq',
      title: 'No.',
      type: 'seq',
      width: 60
      // fixed: 'left' 是序号列的默认值，可以省略
    },
    { key: 'name', title: 'Name', width: 150, fixed: 'left' },
    { key: 'email', title: 'Email Address', width: 200 },
    { key: 'department', title: 'Department Name', width: 200 },
    { key: 'age', title: 'Age', width: 100 },
    {
      key: 'status',
      title: 'Status',
      width: 120,
      render: () => '✅ Active'
    },
    {
      key: 'salary',
      title: 'Salary',
      width: 150,
      render: () => '$50,000'
    },
    {
      key: 'actions',
      title: 'Actions',
      width: 150,
      fixed: 'right',
      render: () => 'Edit | Delete'
    }
  ],
  data: sampleData,
  stripe: { enabled: true, type: "default" },
  width: 'auto', // 限制表格宽度以触发横向滚动
  height: 400
})
</script>

<style scoped>
@reference '../styles/base.css';

sequence-column-examples {
  @apply max-w-7xl mx-auto p-6;
}

section {
  @apply bg-white rounded-lg border border-gray-200 p-6;
}

h1 {
  @apply text-gray-900;
}

h2 {
  @apply text-gray-800;
}

p {
  @apply text-gray-600 text-sm;
}
</style>
