<script setup lang="ts">
import VueTable from '../components/Table/Table.vue'
import type { TableConfig } from '../types'
import { toolbarTableData } from './mock_data'

// 带工具栏的表格配置
const toolbarTableConfig: TableConfig = {
  columns: [
    { key: 'id', title: 'ID', width: 80, sortable: true },
    { key: 'product', title: '产品名称', width: 200, sortable: true },
    { key: 'price', title: '价格', width: 120, align: 'right', sortable: true },
    { key: 'category', title: '分类', width: 150, sortable: true },
    { key: 'stock', title: '库存', width: 100, align: 'center', sortable: true }
  ],
  data: toolbarTableData,
  toolbar: {
    title: '产品管理'
  },
  border: true,
  stripe: { enabled: false, type: "none" },
  hover: 'row',
  size: 'medium'
}
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h2>带工具栏表格演示</h2>
      <p class="demo-description">具有工具栏的表格，适用于管理类页面场景</p>
    </div>

    <div class="demo-content">
      <VueTable :config="toolbarTableConfig" />
    </div>

    <div class="demo-features">
      <h3>功能特性：</h3>
      <ul>
        <li>✅ 表格工具栏</li>
        <li>✅ 产品数据管理</li>
        <li>✅ 数值右对齐</li>
        <li>✅ 居中对齐</li>
        <li>✅ 列排序功能</li>
        <li>✅ 悬停高亮效果</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.demo-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-header h2 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.demo-description {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.demo-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features h3 {
  color: #1f2937;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.demo-features ul {
  margin: 0;
  padding-left: 1.5rem;
}

.demo-features li {
  margin-bottom: 0.5rem;
  color: #059669;
  font-weight: 500;
}

@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }

  .demo-content,
  .demo-features {
    padding: 1rem;
  }
}
</style>
