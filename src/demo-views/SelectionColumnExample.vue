<template>
  <div class="selection-column-examples">
    <h1 class="text-2xl font-bold mb-6">选择列功能演示</h1>

    <!-- 多选模式示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">1. 多选模式 (Checkbox)</h2>
      <p class="text-gray-600 mb-4">
        参考 VXE Table 的配置方式，使用 type: 'selection' 和 mode: 'multiple'
      </p>
      <div class="mb-4">
        <p class="text-sm text-gray-500">
          已选择: {{ multipleSelectedRows.length }} 行
          <span v-if="multipleSelectedRows.length > 0">
            - {{ multipleSelectedRows.map(row => row.name).join(', ') }}
          </span>
        </p>
      </div>
      <Table
        :config="multipleConfig"
        @row-select="handleMultipleSelection"
      />
    </section>

    <!-- 单选模式示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">2. 单选模式 (Radio)</h2>
      <p class="text-gray-600 mb-4">使用 mode: 'single' 配置单选模式</p>
      <div class="mb-4">
        <p class="text-sm text-gray-500">
          已选择: {{ singleSelectedRow ? singleSelectedRow.name : '无' }}
        </p>
      </div>
      <Table
        :config="singleConfig"
        @row-select="handleSingleSelection"
      />
    </section>

    <!-- 禁用部分行示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">3. 禁用部分行选择</h2>
      <p class="text-gray-600 mb-4">使用 getCheckboxProps 禁用特定行的选择</p>
      <div class="mb-4">
        <p class="text-sm text-gray-500">
          已选择: {{ disabledSelectedRows.length }} 行
          <span v-if="disabledSelectedRows.length > 0">
            - {{ disabledSelectedRows.map(row => row.name).join(', ') }}
          </span>
        </p>
      </div>
      <Table
        :config="disabledConfig"
        @row-select="handleDisabledSelection"
      />
    </section>

    <!-- 分页模式示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">4. 分页模式选择</h2>
      <p class="text-gray-600 mb-4">在分页模式下保持选择状态</p>
      <div class="mb-4">
        <p class="text-sm text-gray-500">
          总选择数: {{ paginationSelectedRows.length }} 行
          <span v-if="paginationSelectedRows.length > 0">
            - {{ paginationSelectedRows.map(row => row.name).join(', ') }}
          </span>
        </p>
      </div>
      <Table
        :config="paginationConfig"
        @row-select="handlePaginationSelection"
      />
    </section>

    <!-- 自定义选择列示例 -->
    <section class="mb-8">
      <h2 class="text-lg font-semibold mb-4">5. 自定义选择列</h2>
      <p class="text-gray-600 mb-4">自定义选择列标题和样式</p>
      <div class="mb-4">
        <p class="text-sm text-gray-500">已选择: {{ customSelectedRows.length }} 行</p>
      </div>
      <Table
        :config="customConfig"
        @row-select="handleCustomSelection"
      />
    </section>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import Table from '@/components/Table/Table.vue'
import type { TableConfig, TableRow } from '@/types'
import { selectionLargeData, selectionSampleData } from './mock_data'

// 示例数据
const sampleData: TableRow[] = selectionSampleData

// 更多数据用于分页示例
const largeData: TableRow[] = selectionLargeData

// 选择状态
const multipleSelectedRows = ref<TableRow[]>([])
const singleSelectedRow = ref<TableRow | null>(null)
const disabledSelectedRows = ref<TableRow[]>([])
const paginationSelectedRows = ref<TableRow[]>([])
const customSelectedRows = ref<TableRow[]>([])

// 1. 多选模式配置
const multipleConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'selection',
      title: '',
      type: 'selection',
      width: 60,
      selectionConfig: {
        enabled: true,
        mode: 'multiple',
        showSelectAll: true
      }
    },
    { key: 'name', title: 'Name', fixed: 'left' },
    { key: 'age', title: 'Age', sortable: true },
    { key: 'department', title: 'Department' },
    { key: 'email', title: 'Email' }
  ],
  data: sampleData
})

// 2. 单选模式配置
const singleConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'seq',
      title: '序号',
      type: 'seq',
      width: 60,
      seqConfig: {
        startIndex: 10
      }
    },
    {
      key: 'selection',
      title: '选择',
      type: 'selection',
      width: 80,
      selectionConfig: {
        enabled: true,
        mode: 'single'
      }
    },
    { key: 'name', title: 'Name' },
    { key: 'age', title: 'Age', sortable: true },
    { key: 'department', title: 'Department' }
  ],
  data: sampleData
})

// 3. 禁用部分行配置
const disabledConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'selection',
      title: '',
      type: 'selection',
      width: 60,
      selectionConfig: {
        enabled: true,
        mode: 'multiple',
        showSelectAll: true,
        getCheckboxProps: (row: TableRow) => ({
          disabled: row.status === 'inactive'
        })
      }
    },
    { key: 'name', title: 'Name' },
    { key: 'status', title: 'Status' },
    { key: 'department', title: 'Department' }
  ],
  data: sampleData
})

// 4. 分页模式配置
const paginationConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'selection',
      title: '',
      type: 'selection',
      width: 60,
      selectionConfig: {
        enabled: true,
        mode: 'multiple',
        showSelectAll: true,
        preserveSelectedRowKeys: true
      }
    },
    { key: 'name', title: 'Name' },
    { key: 'email', title: 'Email', width: 200 },
    { key: 'department', title: 'Department' }
  ],
  data: largeData,
  pagination: {
    enabled: true,
    pageSize: 5,
    currentPage: 1,
    total: largeData.length,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: true
  }
})

// 5. 自定义选择列配置
const customConfig = reactive<TableConfig>({
  columns: [
    {
      key: 'selection',
      title: '✓ 选择',
      type: 'selection',
      width: 100,
      fixed: 'left',
      selectionConfig: {
        enabled: true,
        mode: 'multiple',
        showSelectAll: true
      }
    },
    { key: 'name', title: 'Name', width: 150, fixed: 'left' },
    { key: 'age', title: 'Age', width: 100, fixed: 'left' },
    { key: 'email', title: 'Email Address', width: 200 },
    { key: 'department', title: 'Department Name', width: 200 },
    {
      key: 'actions',
      title: 'Actions',
      width: 150,
      fixed: 'right',
      render: () => 'Edit | Delete'
    }
  ],
  data: sampleData,
  stripe: { enabled: true, type: "default" }
})

// 事件处理
const handleMultipleSelection = (selectedRows: TableRow[]): void => {
  multipleSelectedRows.value = selectedRows
}

const handleSingleSelection = (selectedRows: TableRow[]): void => {
  singleSelectedRow.value = selectedRows.length > 0 ? selectedRows[0] : null
}

const handleDisabledSelection = (selectedRows: TableRow[]): void => {
  disabledSelectedRows.value = selectedRows
}

const handlePaginationSelection = (selectedRows: TableRow[]): void => {
  paginationSelectedRows.value = selectedRows
}

const handleCustomSelection = (selectedRows: TableRow[]): void => {
  customSelectedRows.value = selectedRows
}
</script>

<style scoped>
@reference '../styles/base.css';

.selection-column-examples {
  @apply max-w-7xl mx-auto p-6;
}

section {
  @apply bg-white rounded-lg border border-gray-200 p-6;
}

h1 {
  @apply text-gray-900;
}

h2 {
  @apply text-gray-800;
}

p {
  @apply text-gray-600 text-sm;
}
</style>
