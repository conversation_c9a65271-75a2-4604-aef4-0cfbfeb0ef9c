import type { TableRow } from '../types'

const firstNames = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>'
]
const lastNames = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>'
]
const departments = [
  'Engineering',
  'Product',
  'Design',
  'Operations',
  'Marketing',
  'Human Resources',
  'Finance'
]
const positions = ['Engineer', 'Manager', 'Specialist', 'Director', 'VP', 'Assistant', 'Analyst']
const statuses = ['Active', 'Inactive', 'On Leave', 'Probation']
const educationLevels = ['Bachelor', 'Master', 'PhD']
const levels = ['P4', 'P5', 'P6', 'P7', 'P8']
const ratings = ['A', 'A+', 'B', 'B+', 'S']
const productNames = ['MacBook Pro', 'iPhone 15', 'iPad Air', 'Apple Watch', 'AirPods Pro']
const categories = ['Laptop', 'Smartphone', 'Tablet', 'Smartwatch', 'Headphones']

const generateRandomRow = (index: number): TableRow => {
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
  const name = `${firstName} ${lastName}`
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@example.com`

  return {
    id: index,
    name,
    avatar: ['👨‍💻', '👩‍💼', '👨‍🔧', '👩‍🎨', '👨‍🔬'][index % 5],
    age: Math.floor(Math.random() * 40) + 20,
    email,
    phone: `555-0100-${String(index).padStart(4, '0')}`,
    address: `${100 + index} Main St, Anytown, USA`,
    city: `City ${index % 10}`,
    country: 'USA',
    zipCode: String(90000 + index),
    department: departments[Math.floor(Math.random() * departments.length)],
    position: positions[Math.floor(Math.random() * positions.length)],
    level: levels[Math.floor(Math.random() * levels.length)],
    salary: `$${(Math.floor(Math.random() * 10000) + 5000) * 10}`,
    bonus: `$${(Math.floor(Math.random() * 2000) + 500) * 10}`,
    hireDate: `202${Math.floor(Math.random() * 4)}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
    workYears: Math.floor(Math.random() * 15) + 1,
    education: educationLevels[Math.floor(Math.random() * educationLevels.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    rating: ratings[Math.floor(Math.random() * ratings.length)],
    emergencyContact: `Jane Doe 555-0200-${String(index).padStart(4, '0')}`,
    actions: 'Edit/Delete',
    order: `ORD-${String(index).padStart(3, '0')}`,
    customer: name,
    amount: `$${(Math.random() * 5000 + 500).toFixed(2)}`,
    date: `2024-01-${String(index % 30).padStart(2, '0')}`,
    product: productNames[index % productNames.length],
    price: `$${(Math.random() * 1000 + 500).toFixed(2)}`,
    category: categories[index % categories.length],
    stock: Math.floor(Math.random() * 100),
    description: `Description for item ${index}`
  }
}

export const generateMockData = (count: number): TableRow[] => {
  return Array.from({ length: count }, (_, i) => generateRandomRow(i + 1))
}

export const basicTableData: TableRow[] = generateMockData(5)
export const borderTableData: TableRow[] = generateMockData(10)
export const emptyAndLoadingData: TableRow[] = generateMockData(2)
export const multiFixedData: TableRow[] = generateMockData(3)
export const paginationTableData: TableRow[] = generateMockData(5)
export const scrollTableData: TableRow[] = generateMockData(10)
export const selectionSampleData: TableRow[] = generateMockData(5)
export const selectionLargeData: TableRow[] = generateMockData(25)
export const sequenceSampleData: TableRow[] = generateMockData(5)
export const sequenceLargeData: TableRow[] = generateMockData(25)
export const toolbarTableData: TableRow[] = generateMockData(5)

export const generateVScrollData = (dataSize: number, columnCount: number): TableRow[] => {
  const data = generateMockData(dataSize)
  if (columnCount > 10) {
    return data.map(row => ({
      ...row,
      company: `Company ${row.id % 20}`,
      experience: `${row.workYears} years`,
      skills: `Skill ${(row.id % 5) + 1}`,
      manager: `Manager ${Math.floor(row.id / 10) + 1}`,
      team: `Team ${(row.id % 8) + 1}`,
      project: `Project ${(row.id % 15) + 1}`,
      workLocation: 'Office',
      contractType: 'Full-time'
    }))
  }
  return data
}
