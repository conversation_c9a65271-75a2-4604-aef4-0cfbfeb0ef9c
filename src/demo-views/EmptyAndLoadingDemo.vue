<script setup lang="ts">
import { ref, computed } from 'vue'
import VueTable from '../components/Table/Table.vue'
import type { TableConfig } from '../types'
import { emptyAndLoadingData } from './mock_data'

// 当前演示类型
const currentDemo = ref<'empty' | 'loading'>('empty')

// 共同的列配置
const columns = [
  { key: 'name', title: '名称', width: 200 },
  { key: 'description', title: '描述', width: 300 },
  { key: 'status', title: '状态', width: 100 }
]

// 示例数据
const sampleData = emptyAndLoadingData

// 使用计算属性来确保响应性
const currentConfig = computed<TableConfig>(() => {
  if (currentDemo.value === 'empty') {
    return {
      columns,
      data: [],
      loading: false,
      border: true,
      hover: 'row',
      size: 'medium'
    }
  } else {
    return {
      columns,
      data: sampleData,
      loading: true,
      border: true,
      hover: 'row',
      size: 'medium'
    }
  }
})
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h2>空数据与加载状态演示</h2>
      <p class="demo-description">表格的空数据状态和加载状态展示</p>
    </div>

    <div class="demo-selector">
      <h3>选择演示类型：</h3>
      <div class="demo-buttons">
        <button
          :class="['demo-btn', { active: currentDemo === 'empty' }]"
          @click="currentDemo = 'empty'"
        >
          空数据状态
        </button>
        <button
          :class="['demo-btn', { active: currentDemo === 'loading' }]"
          @click="currentDemo = 'loading'"
        >
          加载状态
        </button>
      </div>
    </div>

    <div class="demo-content">
      <VueTable :config="currentConfig" />
    </div>

    <div class="demo-features">
      <h3>功能特性：</h3>
      <ul>
        <li>✅ 空数据提示</li>
        <li>✅ 加载状态指示器</li>
        <li>✅ 友好的用户体验</li>
        <li>✅ 状态切换演示</li>
        <li>✅ 自定义空状态内容</li>
        <li>✅ Loading 遮罩层</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.demo-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-header h2 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.demo-description {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.demo-selector {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-selector h3 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1.1rem;
}

.demo-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  color: #6b7280;
}

.demo-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.demo-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.demo-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features h3 {
  color: #1f2937;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.demo-features ul {
  margin: 0;
  padding-left: 1.5rem;
}

.demo-features li {
  margin-bottom: 0.5rem;
  color: #059669;
  font-weight: 500;
}

@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }

  .demo-content,
  .demo-features,
  .demo-selector {
    padding: 1rem;
  }
}
</style>
