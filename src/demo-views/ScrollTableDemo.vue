<script setup lang="ts">
import VueTable from '../components/Table/Table.vue'
import type { TableConfig } from '../types'
import { scrollTableData } from './mock_data'

// 横向滚动测试配置
const scrollTableConfig: TableConfig = {
  columns: [
    { key: 'id', title: 'ID', width: 80, sortable: true, fixed: 'left' },
    { key: 'name', title: '姓名', width: 120, sortable: true, fixed: 'left' },
    { key: 'age', title: '年龄', width: 100, align: 'center', sortable: true },
    { key: 'email', title: '邮箱地址', width: 200, sortable: true },
    { key: 'phone', title: '联系电话', width: 150, sortable: true },
    { key: 'department', title: '所属部门', width: 150, sortable: true },
    { key: 'position', title: '职位名称', width: 150, sortable: true },
    { key: 'level', title: '职级', width: 100, align: 'center', sortable: true },
    { key: 'salary', title: '月薪', width: 120, align: 'right', sortable: true },
    { key: 'bonus', title: '年终奖', width: 120, align: 'right', sortable: true },
    { key: 'hireDate', title: '入职日期', width: 150, sortable: true },
    { key: 'workYears', title: '工作年限', width: 100, align: 'center', sortable: true },
    { key: 'education', title: '学历', width: 100, align: 'center', sortable: true },
    { key: 'address', title: '家庭住址', width: 200, sortable: true },
    { key: 'emergencyContact', title: '紧急联系人', width: 150, sortable: true },
    { key: 'status', title: '状态', width: 100, align: 'center', fixed: 'right' },
    { key: 'actions', title: '操作', width: 150, align: 'center', fixed: 'right' }
  ],
  data: scrollTableData,
  border: true,
  stripe: { enabled: true, type: "default" },
  hover: 'row',
  size: 'medium',
  stickyHeader: true,
  height: '400px'
}
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h2>粘性标题行 & 滚动演示</h2>
      <p class="demo-description">支持水平滚动的宽表格，标题行保持粘性定位</p>
    </div>

    <div class="demo-content">
      <VueTable :config="scrollTableConfig" />
    </div>

    <div class="demo-features">
      <h3>功能特性：</h3>
      <ul>
        <li>✅ 横向滚动支持</li>
        <li>✅ 粘性表头</li>
        <li>✅ 左右固定列</li>
        <li>✅ 大数据量展示</li>
        <li>✅ 固定表格高度</li>
        <li>✅ 纵向滚动</li>
        <li>✅ 复杂数据结构</li>
        <li>✅ 多列对齐方式</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.demo-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-header h2 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.demo-description {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.demo-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features h3 {
  color: #1f2937;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.demo-features ul {
  margin: 0;
  padding-left: 1.5rem;
}

.demo-features li {
  margin-bottom: 0.5rem;
  color: #059669;
  font-weight: 500;
}

@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }

  .demo-content,
  .demo-features {
    padding: 1rem;
  }
}
</style>
