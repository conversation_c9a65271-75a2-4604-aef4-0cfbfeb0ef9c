<template>
  <div class="virtual-scroll-demo">
    <h2 class="text-2xl font-bold mb-6">虚拟滚动演示</h2>

    <!-- 控制面板 -->
    <div class="control-panel mb-6 p-4 bg-gray-50 rounded-lg">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">数据行数</label>
          <select
            v-model="dataSize"
            class="w-full p-2 border rounded"
            @change="generateData"
          >
            <option :value="10">10行（虚拟滚动关闭）</option>
            <option :value="50">50行（智能启用）</option>
            <option :value="500">500行（大数据集）</option>
            <option :value="5000">5000行（超大数据集）</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium mb-1">列数</label>
          <select
            v-model="columnCount"
            class="w-full p-2 border rounded"
            @change="generateColumns"
          >
            <option :value="10">10列（横向虚拟关闭）</option>
            <option :value="25">25列（智能启用）</option>
            <option :value="50">50列（多列展示）</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium mb-1">表格高度</label>
          <select
            v-model="tableHeight"
            class="w-full p-2 border rounded"
          >
            <option value="300px">300px</option>
            <option value="500px">500px</option>
            <option value="800px">800px</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium mb-1">虚拟滚动</label>
          <div class="flex items-center space-x-2">
            <input
              id="virtual-toggle"
              v-model="virtualEnabled"
              type="checkbox"
              class="w-4 h-4"
            />
            <label
              for="virtual-toggle"
              class="text-sm"
              >启用虚拟滚动</label
            >
          </div>
        </div>
      </div>

      <!-- 状态信息 -->
      <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div class="bg-white p-2 rounded border">
          <div class="font-medium text-gray-600">总数据量</div>
          <div class="text-lg font-bold text-blue-600">{{ tableConfig.data.length }}行</div>
        </div>
        <div class="bg-white p-2 rounded border">
          <div class="font-medium text-gray-600">总列数</div>
          <div class="text-lg font-bold text-green-600">{{ tableConfig.columns.length }}列</div>
        </div>
        <div class="bg-white p-2 rounded border">
          <div class="font-medium text-gray-600">纵向虚拟</div>
          <div
            class="text-lg font-bold"
            :class="virtualStatus.vertical ? 'text-green-600' : 'text-gray-400'"
          >
            {{ virtualStatus.vertical ? '已启用' : '未启用' }}
          </div>
        </div>
        <div class="bg-white p-2 rounded border">
          <div class="font-medium text-gray-600">横向虚拟</div>
          <div
            class="text-lg font-bold"
            :class="virtualStatus.horizontal ? 'text-green-600' : 'text-gray-400'"
          >
            {{ virtualStatus.horizontal ? '已启用' : '未启用' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 表格展示 -->
    <div class="table-container">
      <VueTable
        ref="tableRef"
        :config="tableConfig"
        class="w-full border border-gray-200 rounded-lg overflow-hidden"
      />
    </div>

    <!-- 性能信息面板 -->
    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-3 text-blue-800">实时性能监控</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">滚动FPS</div>
          <div class="text-lg font-bold text-green-600">
            {{ realTimeMetrics.scrollFPS }}
          </div>
        </div>
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">更新耗时</div>
          <div class="text-lg font-bold text-blue-600">
            {{ realTimeMetrics.averageUpdateTime }}ms
          </div>
        </div>
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">缓存命中率</div>
          <div class="text-lg font-bold text-purple-600">{{ realTimeMetrics.cacheHitRate }}%</div>
        </div>
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">可见项数量</div>
          <div class="text-lg font-bold text-orange-600">
            {{ realTimeMetrics.visibleItemCount }}
          </div>
        </div>
      </div>
      <!-- 传统性能指标 -->
      <div class="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">渲染性能</div>
          <div class="text-lg font-bold text-blue-600">{{ performanceInfo.renderTime }}ms</div>
        </div>
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">DOM节点数</div>
          <div class="text-lg font-bold text-purple-600">~{{ performanceInfo.estimatedNodes }}</div>
        </div>
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">缓冲效率</div>
          <div class="text-lg font-bold text-green-600">
            {{ realTimeMetrics.bufferEfficiency }}%
          </div>
        </div>
      </div>

      <!-- 横向虚拟滚动指标 -->
      <div
        v-if="realTimeMetrics.visibleColumnCount > 0"
        class="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4 text-sm"
      >
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">横向滚动FPS</div>
          <div class="text-lg font-bold text-blue-600">
            {{ realTimeMetrics.horizontalScrollFPS || 0 }}
          </div>
        </div>
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">可见列数</div>
          <div class="text-lg font-bold text-purple-600">
            {{ realTimeMetrics.visibleColumnCount || 0 }}
          </div>
        </div>
        <div class="bg-white p-3 rounded border">
          <div class="font-medium text-gray-600">列缓存命中率</div>
          <div class="text-lg font-bold text-green-600">
            {{ realTimeMetrics.columnCacheHitRate || 0 }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 说明信息 -->
    <div class="mt-6 p-4 bg-yellow-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2 text-yellow-800">虚拟滚动配置说明（参考 VXE Table）</h3>
      <ul class="text-sm text-yellow-700 space-y-1">
        <li>
          • <strong>智能启用</strong>：数据量超过 40行 时自动启用纵向虚拟滚动（类似 VXE 的 gt 参数）
        </li>
        <li>• <strong>固定行高</strong>：必须设置固定行高 48px 以保证滚动计算准确性</li>
        <li>
          • <strong>缓冲机制</strong>：bufferSize(10) + overscan(5) + preload(3) 保证滚动流畅性
        </li>
        <li>• <strong>自适应模式</strong>：adaptive=true 时根据数据量自动开关虚拟滚动</li>
        <li>• <strong>性能优化</strong>：reuseWrapper 复用 DOM 元素，减少创建销毁开销</li>
        <li>• <strong>兼容性</strong>：固定列、排序、过滤等功能与虚拟滚动完全兼容</li>
      </ul>
    </div>

    <!-- VXE Table 配置对比 -->
    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2 text-blue-800">与 VXE Table 配置对比</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div class="bg-white p-3 rounded border">
          <h4 class="font-semibold text-blue-700 mb-2">VXE Table 配置</h4>
          <pre class="text-xs bg-gray-100 p-2 rounded overflow-x-auto"><code>scroll-y="{gt: 100}"
height="500px"</code></pre>
        </div>
        <div class="bg-white p-3 rounded border">
          <h4 class="font-semibold text-green-700 mb-2">本项目配置</h4>
          <pre class="text-xs bg-gray-100 p-2 rounded overflow-x-auto"><code>virtual: {
  threshold: 40,
  adaptive: true,
  itemHeight: 48
}</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import VueTable from '../components/Table/Table.vue'
import type { TableConfig, TableColumn } from '../types'
import { generateVScrollData } from './mock_data'

// 响应式状态
const tableRef = ref()
const dataSize = ref(500)
const columnCount = ref(25)
const tableHeight = ref('500px')
const virtualEnabled = ref(true)
const performanceInfo = ref({
  renderTime: 0,
  estimatedNodes: 0,
  memoryOptimization: 0
})

const realTimeMetrics = ref({
  scrollFPS: 0,
  averageUpdateTime: 0,
  cacheHitRate: 0,
  visibleItemCount: 0,
  bufferEfficiency: 0,
  // 横向虚拟滚动指标
  horizontalScrollFPS: 0,
  visibleColumnCount: 0,
  columnCacheHitRate: 0
})

// 计算虚拟滚动状态
const virtualStatus = computed(() => ({
  vertical: virtualEnabled.value && dataSize.value > 40,
  horizontal: virtualEnabled.value && columnCount.value > 20
}))

// 生成测试数据
const generateData = () => {
  const startTime = performance.now()
  const data = generateVScrollData(dataSize.value, columnCount.value)
  const endTime = performance.now()
  performanceInfo.value.renderTime = Math.round(endTime - startTime)

  // 计算性能指标
  updatePerformanceInfo()

  return data
}

// 生成测试列
const generateColumns = () => {
  const baseColumns: TableColumn[] = [
    {
      key: 'id',
      title: 'ID',
      width: 80,
      sortable: true,
      fixed: 'left',
      align: 'center'
    },
    {
      key: 'name',
      title: '姓名',
      width: 120,
      sortable: true,
      fixed: 'left',
      resizable: true,
      render: ({ value }: { value: unknown }) =>
        h('span', { style: { fontWeight: 'bold', color: '#1890ff' } }, String(value))
    },
    {
      key: 'age',
      title: '年龄',
      width: 80,
      align: 'center',
      sortable: true,
      resizable: true,
      template: '🎂 {{value}}岁'
    },
    { key: 'email', title: '邮箱', width: 200, sortable: true, resizable: true },
    { key: 'phone', title: '电话', width: 130, sortable: true, resizable: true },
    { key: 'department', title: '部门', width: 100, sortable: true, resizable: true },
    { key: 'position', title: '职位', width: 120, sortable: true, resizable: true },
    { key: 'salary', title: '薪资', width: 120, align: 'right', sortable: true, resizable: true },
    { key: 'hireDate', title: '入职日期', width: 120, sortable: true, resizable: true },
    { key: 'status', title: '状态', width: 100, align: 'center', sortable: true, resizable: true }
  ]

  // 为多列模式添加更多列
  if (columnCount.value > 10) {
    const extraColumns: TableColumn[] = [
      { key: 'address', title: '地址', width: 150, sortable: true, resizable: true },
      { key: 'city', title: '城市', width: 100, sortable: true, resizable: true },
      { key: 'country', title: '国家', width: 100, sortable: true, resizable: true },
      { key: 'zipCode', title: '邮编', width: 100, sortable: true, resizable: true },
      { key: 'company', title: '公司', width: 120, sortable: true, resizable: true },
      { key: 'experience', title: '工作经验', width: 100, sortable: true, resizable: true },
      { key: 'skills', title: '技能', width: 120, sortable: true, resizable: true },
      { key: 'education', title: '学历', width: 100, sortable: true, resizable: true },
      { key: 'manager', title: '直属经理', width: 120, sortable: true, resizable: true },
      { key: 'team', title: '团队', width: 100, sortable: true, resizable: true },
      { key: 'project', title: '当前项目', width: 120, sortable: true, resizable: true },
      { key: 'rating', title: '评分', width: 80, align: 'center', sortable: true, resizable: true },
      { key: 'bonus', title: '奖金', width: 120, align: 'right', sortable: true, resizable: true },
      { key: 'workLocation', title: '工作地点', width: 120, sortable: true, resizable: true },
      { key: 'contractType', title: '合同类型', width: 120, sortable: true, resizable: true }
    ]

    // 根据需要的列数添加列
    const additionalCount = columnCount.value - baseColumns.length - 1 // -1 for actions column
    if (additionalCount > 0) {
      baseColumns.push(...extraColumns.slice(0, additionalCount))
    }
  }

  // 添加操作列（固定在右侧）
  baseColumns.push({
    key: 'actions',
    title: '操作',
    width: 120,
    align: 'center',
    fixed: 'right',
    resizable: true,
    render: () =>
      h('div', { class: 'space-x-2' }, [
        h('button', { class: 'text-blue-500 hover:text-blue-700', onclick: () => {} }, '编辑'),
        h('button', { class: 'text-red-500 hover:text-red-700', onclick: () => {} }, '删除')
      ])
  })

  return baseColumns.slice(0, columnCount.value)
}

// 更新性能信息
const updatePerformanceInfo = () => {
  const totalRows = dataSize.value
  const totalColumns = columnCount.value

  // 估算DOM节点数
  if (virtualStatus.value.vertical || virtualStatus.value.horizontal) {
    // 虚拟滚动模式：只渲染可见区域
    const visibleRows = Math.min(Math.ceil(parseInt(tableHeight.value) / 48) + 10, totalRows)
    const visibleColumns = Math.min(20, totalColumns) // 假设可见列数
    performanceInfo.value.estimatedNodes = visibleRows * visibleColumns

    // 计算内存优化百分比
    const totalNodes = totalRows * totalColumns
    performanceInfo.value.memoryOptimization = Math.round(
      ((totalNodes - performanceInfo.value.estimatedNodes) / totalNodes) * 100
    )
  } else {
    // 常规模式：渲染所有数据
    performanceInfo.value.estimatedNodes = totalRows * totalColumns
    performanceInfo.value.memoryOptimization = 0
  }
}

// 表格配置
const tableConfig = ref<TableConfig>({
  columns: generateColumns(),
  data: generateData(),
  border: true,
  stickyHeader: true,
  hover: 'row',
  size: 'medium',
  height: parseInt(tableHeight.value),
  virtual: {
    enabled: virtualEnabled.value,
    threshold: 40,
    bufferSize: 10,
    overscan: 5,
    adaptive: true,
    // 横向虚拟滚动配置
    horizontal: {
      enabled: virtualEnabled.value,
      threshold: 20,
      columnWidth: 'auto',
      bufferColumns: 5,
      overscanColumns: 3,
      enableFixedColumns: true,
      adaptiveColumnWidth: false
    }
  }
})

// 更新实时性能指标 - 优化内存使用
const updateRealTimeMetrics = () => {
  if (!tableRef.value) {
    return // 移除console.log以减少内存占用
  }

  try {
    // 获取统一的虚拟滚动指标
    const unifiedMetrics = tableRef.value.getVirtualMetrics?.()
    if (!unifiedMetrics) {
      return
    }

    // 直接更新对象属性而不是创建新对象，减少GC压力
    const current = realTimeMetrics.value
    current.scrollFPS = unifiedMetrics.scrollFPS || 0
    current.averageUpdateTime = unifiedMetrics.averageUpdateTime || 0
    current.cacheHitRate = unifiedMetrics.cacheHitRate || 0
    current.visibleItemCount = unifiedMetrics.visibleItemCount || 0
    current.bufferEfficiency = unifiedMetrics.bufferEfficiency || 0
    // 横向指标
    current.horizontalScrollFPS = unifiedMetrics.horizontalScrollFPS || 0
    current.visibleColumnCount = unifiedMetrics.visibleColumnCount || 0
    current.columnCacheHitRate = unifiedMetrics.columnCacheHitRate || 0
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error updating metrics:', error)
    }
    // 重置为默认值
    Object.assign(realTimeMetrics.value, {
      scrollFPS: 0,
      averageUpdateTime: 0,
      cacheHitRate: 0,
      visibleItemCount: 0,
      bufferEfficiency: 0,
      horizontalScrollFPS: 0,
      visibleColumnCount: 0,
      columnCacheHitRate: 0
    })
  }
}

// 定时更新性能指标 - 优化内存管理
let metricsTimer: number | undefined
let isMonitoring = false

const startMetricsMonitoring = () => {
  // 防止重复启动定时器
  if (isMonitoring || metricsTimer) {
    return
  }

  isMonitoring = true
  // 降低更新频率从500ms到1000ms，减少内存压力
  metricsTimer = window.setInterval(() => {
    // 只在虚拟滚动启用时更新指标
    if (virtualEnabled.value && (virtualStatus.value.vertical || virtualStatus.value.horizontal)) {
      updateRealTimeMetrics()
    }
  }, 1000) // 改为1秒更新一次
}

const stopMetricsMonitoring = () => {
  isMonitoring = false
  if (metricsTimer) {
    clearInterval(metricsTimer)
    metricsTimer = undefined
  }
}

// 组件挂载时启动监控
onMounted(() => {
  nextTick(() => {
    if (import.meta.env.DEV) {
      console.log('Starting metrics monitoring...')
    }
    startMetricsMonitoring()
    // 延迟执行初始更新，避免组件未完全就绪
    setTimeout(updateRealTimeMetrics, 100)
  })
})

// 组件卸载时清理
onUnmounted(() => {
  stopMetricsMonitoring()
})

// 监听数据变化
watch([dataSize], () => {
  tableConfig.value.data = generateData()
  updatePerformanceInfo()
})

watch([columnCount], () => {
  tableConfig.value.columns = generateColumns()
  updatePerformanceInfo()
})

watch(tableHeight, newHeight => {
  tableConfig.value.height = newHeight
  updatePerformanceInfo()
})

watch(virtualEnabled, newValue => {
  // 动态更新虚拟滚动配置，参考 vxetable 的配置模式
  if (tableConfig.value.virtual) {
    tableConfig.value.virtual.enabled = newValue
    // 根据数据量智能调整配置，类似 vxetable 的 gt 机制
    const rowCount = dataSize.value
    const colCount = columnCount.value

    // 纵向虚拟滚动：参考 vxetable 默认 gt: 100
    if (newValue && rowCount > 40) {
      tableConfig.value.virtual.threshold = 40
      tableConfig.value.virtual.itemHeight = 48
    }

    // 横向虚拟滚动配置
    if (tableConfig.value.virtual.horizontal) {
      tableConfig.value.virtual.horizontal.enabled = newValue
      if (newValue && colCount > 20) {
        tableConfig.value.virtual.horizontal.threshold = 20
      }
    }
  }
  updatePerformanceInfo()

  // 虚拟滚动状态变化时重新开始性能监控
  nextTick(() => {
    if (newValue) {
      if (import.meta.env.DEV) {
        console.log('Virtual scrolling enabled, restarting metrics monitoring')
      }
      // 停止现有监控后再启动新的，避免重复定时器
      stopMetricsMonitoring()
      startMetricsMonitoring()
    } else {
      // 虚拟滚动禁用时停止监控，释放资源
      stopMetricsMonitoring()
    }
  })
})

// 初始化
nextTick(() => {
  updatePerformanceInfo()
})
</script>

<style scoped>
.virtual-scroll-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

.control-panel {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.table-container {
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* 控制面板样式 */
.control-panel select {
  transition: all 0.15s ease-in-out;
}

.control-panel select:focus {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
  border-color: rgb(59 130 246);
}

.control-panel input[type='checkbox']:focus {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
}

/* 状态卡片悬停效果 */
.control-panel .bg-white {
  transition: all 0.2s ease-in-out;
}

.control-panel .bg-white:hover {
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* 性能信息面板动画 */
.bg-blue-50 .bg-white {
  transition: all 0.2s ease-in-out;
}

.bg-blue-50 .bg-white:hover {
  transform: scale(1.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .virtual-scroll-demo {
    padding: 1rem;
  }

  .control-panel {
    padding: 0.75rem;
  }

  .control-panel .grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .control-panel .mt-4 .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}
</style>
