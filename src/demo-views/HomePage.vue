<template>
  <div class="home-page">
    <div class="welcome-section">
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">欢迎使用 Vue Table Component</h1>
        <p class="text-xl text-gray-600 mb-8">高性能 Vue 3 表格组件库，专为 B2B 系统设计</p>
        <div class="flex justify-center space-x-4">
          <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            Vue 3
          </span>
          <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            TypeScript
          </span>
          <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
            Tailwind CSS
          </span>
          <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
            Vite
          </span>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <div class="feature-card">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
              <span class="text-white text-lg">🚀</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">高性能</h3>
          </div>
          <p class="text-gray-600">虚拟滚动、智能渲染，轻松处理大量数据</p>
        </div>

        <div class="feature-card">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
              <span class="text-white text-lg">🔧</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">灵活配置</h3>
          </div>
          <p class="text-gray-600">丰富的配置选项，满足各种业务场景需求</p>
        </div>

        <div class="feature-card">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
              <span class="text-white text-lg">📱</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">响应式</h3>
          </div>
          <p class="text-gray-600">完美适配桌面端和移动端，提供优秀的用户体验</p>
        </div>

        <div class="feature-card">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mr-3">
              <span class="text-white text-lg">🛡️</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">类型安全</h3>
          </div>
          <p class="text-gray-600">完整的 TypeScript 支持，开发更安全更高效</p>
        </div>

        <div class="feature-card">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center mr-3">
              <span class="text-white text-lg">🧪</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">测试覆盖</h3>
          </div>
          <p class="text-gray-600">136+ 测试用例，90% 测试覆盖率，稳定可靠</p>
        </div>

        <div class="feature-card">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-cyan-500 rounded-lg flex items-center justify-center mr-3">
              <span class="text-white text-lg">🎨</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">主题系统</h3>
          </div>
          <p class="text-gray-600">支持多主题切换，轻松适配不同的设计风格</p>
        </div>
      </div>

      <div class="quick-start">
        <h2 class="text-2xl font-bold text-gray-900 text-center mb-6">快速开始</h2>
        <div class="bg-gray-50 rounded-lg p-6 max-w-2xl mx-auto">
          <div class="space-y-4">
            <div>
              <h3 class="font-semibold text-gray-800 mb-2">1. 安装</h3>
              <div class="bg-gray-800 text-green-400 p-3 rounded font-mono text-sm">
                npm install @happy-table/vue3
              </div>
            </div>
            <div>
              <h3 class="font-semibold text-gray-800 mb-2">2. 引入</h3>
              <div class="bg-gray-800 text-blue-300 p-3 rounded font-mono text-sm">
                import VueTable from '@happy-table/vue3'<br />
                import '@happy-table/vue3/style.css'
              </div>
            </div>
            <div>
              <h3 class="font-semibold text-gray-800 mb-2">3. 使用</h3>
              <div class="bg-gray-800 text-yellow-300 p-3 rounded font-mono text-sm">
                &lt;VueTable :config="tableConfig" /&gt;
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页不需要额外逻辑
</script>

<style scoped>
.home-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.quick-start {
  margin-top: 3rem;
}

@media (max-width: 768px) {
  .home-page {
    padding: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feature-card {
    padding: 1rem;
  }
}
</style>
