<script setup lang="ts">
import { h } from 'vue'
import VueTable from '../components/Table/Table.vue'
import type { TableConfig } from '../types'
import { multiFixedData } from './mock_data'

// 多固定列测试配置
const multiFixedTableConfig: TableConfig = {
  columns: [
    { key: 'id', title: 'ID', width: 60, sortable: true, fixed: 'left' },
    { key: 'avatar', title: '头像', width: 80 },
    {
      key: 'name',
      title: '姓名',
      width: 100,
      sortable: true,
      fixed: 'left',
      render: ({ value }) => h('span', { style: { color: 'red' } }, String(value))
    },
    { key: 'age', title: '年龄', width: 80, align: 'center', sortable: true, fixed: 'left' },
    { key: 'email', title: '邮箱地址', width: 200, sortable: true, template: '📍 {{value}}' },
    { key: 'phone', title: '联系电话', width: 150, sortable: true, template: '📞 {{value}}' },
    { key: 'department', title: '所属部门', width: 150, sortable: true },
    { key: 'position', title: '职位名称', width: 150, sortable: true },
    { key: 'level', title: '职级', width: 80, align: 'center', sortable: true },
    { key: 'salary', title: '月薪', width: 100, align: 'right', sortable: true },
    { key: 'bonus', title: '年终奖', width: 100, align: 'right', sortable: true },
    { key: 'hireDate', title: '入职日期', width: 120, sortable: true },
    { key: 'workYears', title: '工作年限', width: 100, align: 'center', sortable: true },
    { key: 'education', title: '学历', width: 80, align: 'center', sortable: true },
    { key: 'rating', title: '评级', width: 80, align: 'center', fixed: 'right' },
    { key: 'address', title: '家庭住址', width: 200, sortable: true },
    { key: 'status', title: '状态', width: 80, align: 'center', fixed: 'right' },
    { key: 'emergencyContact', title: '紧急联系人', width: 150, sortable: true },
    { key: 'actions', title: '操作', width: 120, align: 'center', fixed: 'right' }
  ],
  data: multiFixedData,
  stickyHeader: true,
  // stripe: { enabled: true, type: 'default' },
  border: false,
  hover: 'row',
  height: '420px',
  size: 'medium'
}
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h2>多固定列演示</h2>
      <p class="demo-description">展示左右多列固定的复杂表格布局</p>
    </div>

    <div class="demo-content">
      <VueTable :config="multiFixedTableConfig" />
    </div>

    <div class="demo-features">
      <h3>功能特性：</h3>
      <ul>
        <li>✅ 左侧多列固定（ID、头像、姓名）</li>
        <li>✅ 右侧多列固定（评级、状态、操作）</li>
        <li>✅ 中间列可横向滚动</li>
        <li>✅ 自定义渲染和模板</li>
        <li>✅ 表情符号展示</li>
        <li>✅ 复杂数据结构</li>
        <li>✅ 多种对齐方式</li>
        <li>✅ 固定列阴影效果</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.demo-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-header h2 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.demo-description {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.demo-content {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-features h3 {
  color: #1f2937;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.demo-features ul {
  margin: 0;
  padding-left: 1.5rem;
}

.demo-features li {
  margin-bottom: 0.5rem;
  color: #059669;
  font-weight: 500;
}

@media (max-width: 768px) {
  .demo-page {
    padding: 1rem;
  }

  .demo-content,
  .demo-features {
    padding: 1rem;
  }
}
</style>
