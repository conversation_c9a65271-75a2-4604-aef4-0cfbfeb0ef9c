// Core table configuration interface
import type {
  TableColumn,
  TableRow,
  PaginationConfig,
  EditingConfig,
  KeyboardConfig,
  ResponsiveConfig,
  VirtualConfig,
  SortConfig
} from './table'
import type { ToolbarConfig } from './toolbar'
import type { ThemeConfig } from './theme'
import type { StripeConfig } from './stripe'
import type { CustomSortFunction } from '@/utils/sorting'

// Hover mode type
export type HoverMode = 'row' | 'cell' | 'none'

// Main table configuration interface
export interface TableConfig {
  columns: TableColumn[] // 列配置
  data: TableRow[] // 数据源
  toolbar?: ToolbarConfig // 工具栏配置
  theme?: ThemeConfig // 主题配置
  virtual?: VirtualConfig // 虚拟滚动配置
  pagination?: PaginationConfig // 分页配置
  editing?: EditingConfig // 编辑配置
  keyboard?: KeyboardConfig // 键盘配置
  responsive?: ResponsiveConfig // 响应式配置

  // Sorting configuration
  multiSort?: boolean // 是否支持多列排序
  defaultSort?: SortConfig[] // 默认排序配置
  customSortFunctions?: Record<string, CustomSortFunction> // 自定义排序函数

  // Filtering configuration
  caseSensitive?: boolean // 是否区分大小写
  enableRegex?: boolean // 是否启用正则表达式搜索
  enableHighlight?: boolean // 是否启用搜索高亮
  searchDebounce?: number // 搜索防抖延迟（毫秒）

  loading?: boolean // 加载状态
  width?: number | string // 表格外部容器宽度，默认100%（相对于父容器）
  height?: number | string // 表格高度
  maxHeight?: number | string // 最大高度
  border?: boolean | 'default' | 'full' | 'outer' | 'inner' | 'none' // 边框配置：boolean | string，参考 vxetable 的 border 配置项
  stripe?: StripeConfig // 斑马行配置
  hover?: HoverMode // 悬停效果模式：'row' | 'cell' | 'none'
  size?: 'small' | 'medium' | 'large' // 表格尺寸
  stickyHeader?: boolean // 是否启用粘性标题行
}

// Re-export all types
export type {
  // Table types
  TableColumn,
  TableRow,
  SortConfig,
  PaginationConfig,
  PaginationState,
  SelectionConfig,
  EditingConfig,
  KeyboardConfig,
  ResponsiveConfig,
  VirtualConfig,
  VirtualState,
  TableState,
  CellPosition,
  TableCellRenderer,
  TableCellValidator,
  // Filter types - explicitly exported to avoid duplicates
  FilterConfig
} from './table'

// Re-export filter types from utils to avoid conflicts
export type {
  FilterOperator,
  FilterCondition,
  FilterGroup,
  SearchConfig,
  FilterResult,
  HighlightInfo
} from '@/utils/filtering'

export * from './toolbar'
export * from './theme'
export * from './border'
export * from './stripe'
export * from './events'
export * from './slots'
export * from './fixedColumns'
export * from './dragDrop'
export type * from './utils'
