/**
 * 斑马行样式类型定义
 */

/** 斑马行变体类型 */
export type StripeVariant = 
  | 'default'    // 默认斑马行
  | 'subtle'     // 淡化斑马行  
  | 'none'       // 无斑马行

/** 斑马行组件类型 */
export type StripeComponent = 
  | 'container'  // 容器级别
  | 'row'        // 行级别
  | 'cell'       // 单元格级别

/** 斑马行配置接口 */
export interface StripeConfig {
  /** 斑马行类型 */
  type: StripeVariant
  /** 是否启用 */
  enabled: boolean
  /** 斑马行强度 (0-1，仅用于 subtle 模式) */
  intensity?: number
}

/** 斑马行CSS类集合 */
export interface StripeClasses {
  container: string[]
  row: string[]
  cell: string[]
}

/** 斑马行CSS样式集合 */  
export interface StripeStyles {
  container: Record<string, string>
  row: Record<string, string>
  cell: Record<string, string>
}