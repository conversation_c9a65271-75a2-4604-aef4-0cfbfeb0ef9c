/**
 * 列头拖拽排序相关类型定义
 */
import type { TableColumn } from './table'
import type { ComputedRef } from 'vue'

// 拖拽状态接口
export interface DragDropState {
  isDragging: boolean
  draggedColumn: TableColumn | null
  dropTarget: TableColumn | null
  dragStartIndex: number
  dropPosition: 'before' | 'after' | null
  dragPreview: HTMLElement | null
  validDropZones: Set<string>
  dragStartPosition: { x: number; y: number }
}

// 拖拽配置接口
export interface DragDropConfig {
  enabled: boolean
  constrainToGroup: boolean // 限制在同一组内（左固定/普通/右固定）
  showDropIndicator: boolean // 显示插入位置指示器
  showDragPreview: boolean // 显示拖拽预览
  animationDuration: number // 动画持续时间（毫秒）
  dragThreshold: number // 拖拽检测阈值（像素）
  allowSpecialColumnDrag: boolean // 是否允许拖拽特殊列（seq、selection）
  disabledColumnTypes: string[] // 禁用拖拽的列类型
}

// 拖拽事件类型
export interface DragEvent {
  type: 'reorder'
  sourceColumn?: TableColumn
  targetColumn?: TableColumn
  sourceIndex?: number
  targetIndex?: number
  dropPosition?: 'before' | 'after'
  newOrder?: string[]
}

// 拖拽取消原因
export type DragCancelReason = 'escape' | 'invalid' | 'outside' | 'error' | 'user'

// 拖拽验证结果
export interface DragValidationResult {
  isValid: boolean
  reason?: string
  canDrop: boolean
  dropPosition?: 'before' | 'after'
}

// 列组类型
export type ColumnGroup = 'left' | 'normal' | 'right'

// 拖拽钩子选项
export interface UseColumnDragDropOptions {
  columns: TableColumn[]
  onColumnReorder?: (newOrder: string[], dragEvent: DragEvent) => void
  onDragStart?: (column: TableColumn) => void
  onDragEnd?: (column: TableColumn, success: boolean) => void
  onDragCancel?: (reason: DragCancelReason, column: TableColumn | null) => void
  config?: Partial<DragDropConfig>
}

// 拖拽钩子返回值
export interface UseColumnDragDropReturn {
  // 状态
  dragState: DragDropState
  isDragging: ComputedRef<boolean>
  
  // 方法
  startDragDetection: (event: MouseEvent, column: TableColumn) => void
  startDrag: (column: TableColumn, event: MouseEvent) => void
  handleDragOver: (event: MouseEvent, targetColumn: TableColumn) => void
  handleDrop: (event: MouseEvent, targetColumn: TableColumn) => void
  cancelDrag: (reason: DragCancelReason) => void
  
  // 验证
  validateDrop: (draggedColumn: TableColumn, targetColumn: TableColumn) => DragValidationResult
  getColumnGroup: (column: TableColumn) => ColumnGroup
  
  // 工具函数
  reorderColumns: (draggedKey: string, targetKey: string, position: 'before' | 'after') => string[]
  
  // 清理
  cleanup: () => void
}

// 默认配置
export const DEFAULT_DRAG_DROP_CONFIG: DragDropConfig = {
  enabled: true,
  constrainToGroup: true,
  showDropIndicator: true,
  showDragPreview: true,
  animationDuration: 200,
  dragThreshold: 5,
  allowSpecialColumnDrag: false,
  disabledColumnTypes: ['seq', 'selection']
}