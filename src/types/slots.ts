// Slot system type definitions
import type { VNode } from 'vue'
import type { TableRow, TableColumn, PaginationConfig } from './table'
import type { ToolbarConfig } from './toolbar'

// Table slot props interfaces
export interface HeaderSlotProps {
  column: TableColumn
  index: number
}

export interface CellSlotProps {
  value: unknown
  row: TableRow
  column: TableColumn
  rowIndex: number
  columnIndex: number
}

export interface EmptySlotProps {
  loading: boolean
  error: Error | null
}

export interface LoadingSlotProps {
  text?: string
}

export interface PaginationSlotProps {
  config: PaginationConfig
  current: number
  total: number
  pageSize: number
}

// Toolbar slot props interfaces
export interface ToolbarTitleSlotProps {
  selectedCount: number
  totalCount: number
  config: ToolbarConfig
}

export interface ToolbarActionsSlotProps {
  selectedRows: TableRow[]
  selectedCount: number
}

export interface ToolbarSearchSlotProps {
  searchText: string
  onSearch: (text: string) => void
  placeholder?: string
}

export interface ToolbarFilterSlotProps {
  filters: Record<string, unknown>
  onFilter: (filters: Record<string, unknown>) => void
  onClear: () => void
}

export interface ToolbarExportSlotProps {
  onExport: (format: string) => void
  formats: string[]
  loading: boolean
}

export interface ToolbarColumnsSlotProps {
  columns: TableColumn[]
  visibleColumns: string[]
  onToggle: (columnKey: string, visible: boolean) => void
  onReorder: (columnKeys: string[]) => void
}

// Table slots interface
export interface TableSlots {
  // Table content slots
  header?: (props: HeaderSlotProps) => VNode | VNode[]
  cell?: (props: CellSlotProps) => VNode | VNode[]
  empty?: (props: EmptySlotProps) => VNode | VNode[]
  loading?: (props: LoadingSlotProps) => VNode | VNode[]
  pagination?: (props: PaginationSlotProps) => VNode | VNode[]

  // Row expansion slots
  'expand-row'?: (props: { row: TableRow; index: number; expanded: boolean }) => VNode | VNode[]
  'expand-icon'?: (props: { expanded: boolean; record: TableRow }) => VNode | VNode[]

  // Selection slots
  'selection-cell'?: (props: {
    selected: boolean
    disabled: boolean
    row: TableRow
    onChange: (selected: boolean) => void
  }) => VNode | VNode[]

  // Toolbar slots
  'toolbar-title'?: (props: ToolbarTitleSlotProps) => VNode | VNode[]
  'toolbar-actions'?: (props: ToolbarActionsSlotProps) => VNode | VNode[]
  'toolbar-features'?: () => VNode | VNode[]
  'toolbar-search'?: (props: ToolbarSearchSlotProps) => VNode | VNode[]
  'toolbar-filter'?: (props: ToolbarFilterSlotProps) => VNode | VNode[]
  'toolbar-export'?: (props: ToolbarExportSlotProps) => VNode | VNode[]
  'toolbar-columns'?: (props: ToolbarColumnsSlotProps) => VNode | VNode[]

  // Custom slots for specific columns
  [key: `column-${string}`]: (props: CellSlotProps) => VNode | VNode[]
  [key: `header-${string}`]: (props: HeaderSlotProps) => VNode | VNode[]
}

// Slot renderer type
export type SlotRenderer<T = unknown> = (props: T) => VNode | VNode[] | string | number

// Slot configuration interface
export interface SlotConfig {
  name: string
  props?: Record<string, unknown>
  fallback?: () => VNode | VNode[]
}
