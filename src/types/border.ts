/**
 * 边框配置类型定义
 */

export interface BorderConfig {
  /** 边框类型 */
  type: 'default' | 'full' | 'outer' | 'inner' | 'none'
  /** 边框宽度 */
  width?: string | number
  /** 边框样式 */
  style?: 'solid' | 'dashed' | 'dotted' | 'double'
  /** 边框颜色 */
  color?: string
  /** 边框圆角 */
  radius?: string | number
}

export interface BorderClasses {
  /** 容器边框类 */
  container: string[]
  /** 表头边框类 */
  header: string[]
  /** 行边框类 */
  row: string[]
  /** 单元格边框类 */
  cell: string[]
  /** 固定列边框类 */
  fixed?: {
    left: string[]
    right: string[]
  }
}

export interface BorderStyles {
  /** 容器样式 */
  container: Record<string, string>
  /** 表头样式 */
  header: Record<string, string>
  /** 单元格样式 */
  cell: Record<string, string>
  /** 固定列样式 */
  fixed?: {
    left: Record<string, string>
    right: Record<string, string>
  }
}

/** 边框变体类型 */
export type BorderVariant = 'default' | 'full' | 'outer' | 'inner' | 'none'

/** 边框组件类型 */
export type BorderComponent = 'container' | 'header' | 'row' | 'cell'

/** 固定列位置 */
export type FixedPosition = 'left' | 'right'