/**
 * 固定列功能的 TypeScript 类型定义
 * 基于技术设计文档的 Intersection Observer 方案
 */

import type { TableColumn } from './table'

/**
 * 哨兵元素类型
 */
export interface SentinelElement {
  element: HTMLElement
  column: TableColumn
  side: 'left' | 'right'
  observer?: IntersectionObserver
}

/**
 * 哨兵元素集合
 */
export interface SentinelElements {
  leftSentinel?: SentinelElement
  rightSentinel?: SentinelElement
}

/**
 * 滚动状态
 */
export interface FixedColumnScrollState {
  /** 是否向左滚动（左侧内容被隐藏） */
  isScrolledLeft: boolean
  /** 是否向右滚动（右侧内容被隐藏） */
  isScrolledRight: boolean
  /** 是否在最左端 */
  isAtLeftEnd: boolean
  /** 是否在最右端 */
  isAtRightEnd: boolean
  /** 当前水平滚动位置 */
  scrollLeft: number
  /** 容器宽度 */
  containerWidth: number
  /** 表格总宽度 */
  tableWidth: number
}

/**
 * 阴影状态
 */
export interface ShadowState {
  /** 左侧临界列是否显示阴影 */
  showLeftShadow: boolean
  /** 右侧临界列是否显示阴影 */
  showRightShadow: boolean
}

/**
 * 固定列边界信息
 */
export interface FixedColumnBoundary {
  /** 最后一个左固定列的索引 */
  lastLeftFixedIndex: number
  /** 第一个右固定列的索引 */
  firstRightFixedIndex: number
  /** 最后一个左固定列 */
  lastLeftFixedColumn?: TableColumn
  /** 第一个右固定列 */
  firstRightFixedColumn?: TableColumn
}

/**
 * 固定列样式结果
 */
export interface FixedColumnStyleResult {
  classes: string[]
  styles: Record<string, string>
}

/**
 * Intersection Observer 配置
 */
export interface ObserverConfig {
  /** 观察容器 */
  root: HTMLElement
  /** 阈值 */
  threshold: number
  /** 根边距 */
  rootMargin?: string
}

/**
 * 观察者清理函数
 */
export type ObserverCleanup = () => void

/**
 * 哨兵回调函数
 */
export interface SentinelCallbacks {
  /** 左侧哨兵状态变化回调 */
  onLeftSentinelChange?: (isVisible: boolean) => void
  /** 右侧哨兵状态变化回调 */
  onRightSentinelChange?: (isVisible: boolean) => void
}

/**
 * useFixedColumns 配置
 */
export interface UseFixedColumnsOptions {
  /** 表格容器引用 */
  containerRef: import('vue').Ref<HTMLElement | null>
  /** 列配置 */
  columns: import('vue').Ref<TableColumn[]>
  /** 是否启用固定列功能 */
  enabled?: boolean
  /** 观察者配置 */
  observerConfig?: Partial<ObserverConfig>
  /** 哨兵回调 */
  callbacks?: SentinelCallbacks
}

/**
 * useFixedColumns 返回值
 */
export interface UseFixedColumnsReturn {
  /** 滚动状态 */
  scrollState: import('vue').ComputedRef<FixedColumnScrollState>
  /** 阴影状态 */
  shadowState: import('vue').ComputedRef<ShadowState>
  /** 固定列边界信息 */
  boundaryInfo: import('vue').ComputedRef<FixedColumnBoundary>
  /** 哨兵元素 */
  sentinels: import('vue').Ref<SentinelElements>
  
  /** 获取固定列样式 */
  getFixedColumnStyle: (column: TableColumn) => FixedColumnStyleResult
  /** 初始化固定列位置 */
  initializeFixedPositions: () => void
  /** 创建哨兵元素 */
  createSentinels: () => void
  /** 设置观察者 */
  setupObservers: () => ObserverCleanup
  /** 清理资源 */
  cleanup: () => void
}