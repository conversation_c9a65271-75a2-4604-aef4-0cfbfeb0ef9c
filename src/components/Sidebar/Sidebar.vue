<template>
  <!-- Shadcn-inspired Sidebar -->
  <aside class="w-64 bg-white border-r border-gray-200 flex flex-col h-full">
    <!-- Sidebar Header -->
    <div class="flex flex-col gap-2 p-4 border-b border-gray-200">
      <div class="flex items-center gap-2 px-2">
        <div class="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
          <span class="text-sm font-semibold">V</span>
        </div>
        <div class="grid flex-1 text-left text-sm leading-tight">
          <span class="truncate font-semibold">Vue Table</span>
          <span class="truncate text-xs text-muted-foreground">Component Library</span>
        </div>
      </div>
    </div>

    <!-- Sidebar Content -->
    <div class="flex-1 overflow-auto">
      <div class="grid gap-2 p-4">
        <!-- Navigation Group -->
        <div class="grid gap-1">
          <div class="px-2 py-1">
            <h4 class="text-xs font-medium text-muted-foreground uppercase tracking-wider">演示页面</h4>
          </div>
          <nav class="grid gap-1">
            <button
              v-for="route in routes"
              :key="route.path"
              :class="[
                'flex items-center gap-2 rounded-md px-2 py-1.5 text-sm font-medium transition-colors',
                'hover:bg-accent hover:text-accent-foreground',
                'focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring',
                currentRoute === route.path
                  ? 'bg-accent text-accent-foreground'
                  : 'text-muted-foreground'
              ]"
              @click="navigateTo(route.path)"
            >
              <span class="text-base">{{ route.icon }}</span>
              <span class="truncate">{{ route.name }}</span>
            </button>
          </nav>
        </div>
      </div>
    </div>

    <!-- Sidebar Footer -->
    <div class="p-4 border-t border-gray-200">
      <div class="rounded-lg border bg-card p-3">
        <div class="flex flex-col gap-2">
          <h4 class="text-sm font-medium">技术栈</h4>
          <div class="flex flex-wrap gap-1">
            <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">Vue 3</span>
            <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">TypeScript</span>
            <span class="inline-flex items-center rounded-md bg-cyan-50 px-2 py-1 text-xs font-medium text-cyan-700 ring-1 ring-inset ring-cyan-700/10">Tailwind</span>
            <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-700/10">Vite</span>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
interface Route {
  path: string
  name: string
  icon: string
}

interface SidebarProps {
  routes: Route[]
  currentRoute: string
}

defineProps<SidebarProps>()

const emit = defineEmits<{
  navigate: [path: string]
}>()

const navigateTo = (path: string) => {
  emit('navigate', path)
}
</script>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  aside {
    transform: translateX(-100%);
    position: absolute;
    z-index: 50;
    height: 100vh;
  }
}

/* 滚动条样式 */
aside::-webkit-scrollbar {
  width: 4px;
}

aside::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

aside::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

aside::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>