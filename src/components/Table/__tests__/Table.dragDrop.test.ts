import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import Table from '../Table.vue'
import type { TableConfig, TableColumn, DragEvent } from '@/types'

describe('Table Component - Drag and Drop Integration', () => {
  let wrapper: VueWrapper<any>
  let mockTableConfig: TableConfig
  let mockColumns: TableColumn[]

  beforeEach(() => {
    mockColumns = [
      { key: 'seq', title: '#', type: 'seq', fixed: 'left', width: 60, draggable: false },
      { key: 'id', title: 'ID', width: 80, sortable: true, draggable: true },
      { key: 'name', title: 'Name', width: 120, sortable: true, draggable: true },
      { key: 'age', title: 'Age', width: 80, sortable: true, draggable: true },
      { key: 'email', title: 'Email', width: 150, draggable: true },
      { key: 'actions', title: 'Actions', fixed: 'right', width: 100, draggable: false }
    ]

    mockTableConfig = {
      columns: mockColumns,
      data: [
        { id: 1, name: '<PERSON>', age: 30, email: '<EMAIL>' },
        { id: 2, name: '<PERSON>', age: 25, email: '<EMAIL>' },
        { id: 3, name: 'Charlie', age: 35, email: '<EMAIL>' }
      ],
      border: 'outer',
      hover: 'row'
    }

    // Mock DOM methods for drag operations
    Object.defineProperty(HTMLElement.prototype, 'getBoundingClientRect', {
      value: vi.fn().mockReturnValue({
        x: 0, y: 0, width: 100, height: 40,
        top: 0, left: 0, bottom: 40, right: 100
      }),
      writable: true
    })

    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      get: vi.fn().mockReturnValue(100),
      configurable: true
    })

    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
      get: vi.fn().mockReturnValue(40),
      configurable: true
    })

    vi.spyOn(document, 'createElement').mockImplementation(() => ({
      style: {},
      classList: { add: vi.fn(), remove: vi.fn(), contains: vi.fn() },
      appendChild: vi.fn(),
      removeChild: vi.fn(),
      remove: vi.fn(),
      cloneNode: vi.fn().mockReturnValue({
        style: {},
        classList: { add: vi.fn(), remove: vi.fn() }
      }),
      querySelector: vi.fn(),
      querySelectorAll: vi.fn().mockReturnValue([])
    } as any))

    vi.spyOn(document.body, 'appendChild').mockImplementation(() => undefined as any)
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => undefined as any)
    vi.spyOn(window, 'requestAnimationFrame').mockImplementation((callback) => {
      callback(0)
      return 0
    })
  })

  describe('Column Reordering State', () => {
    it('should initialize with empty column order', () => {
      wrapper = mount(Table, {
        props: { config: mockTableConfig }
      })

      const vm = wrapper.vm as any
      expect(vm.columnOrder).toEqual([])
      expect(vm.hasCustomColumnOrder).toBe(false)
    })

    it('should maintain original column order without custom ordering', () => {
      wrapper = mount(Table, {
        props: { config: mockTableConfig }
      })

      const vm = wrapper.vm as any
      const processedColumns = vm.processedColumns

      expect(processedColumns[0].key).toBe('seq')
      expect(processedColumns[1].key).toBe('id')
      expect(processedColumns[2].key).toBe('name')
      expect(processedColumns[3].key).toBe('age')
      expect(processedColumns[4].key).toBe('email')
      expect(processedColumns[5].key).toBe('actions')
    })
  })

  describe('Column Reorder Handler', () => {
    it('should update column order state when reorder occurs', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      const dragEvent: DragEvent = {
        type: 'reorder',
        sourceColumn: mockColumns[1], // id
        targetColumn: mockColumns[2], // name
        sourceIndex: 1,
        targetIndex: 2,
        dropPosition: 'before',
        newOrder
      }

      vm.handleColumnReorder(newOrder, dragEvent)

      expect(vm.columnOrder).toEqual(newOrder)
      expect(vm.hasCustomColumnOrder).toBe(true)
    })

    it('should emit columnReorder event with correct parameters', () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      const dragEvent: DragEvent = {
        type: 'reorder',
        sourceColumn: mockColumns[1],
        targetColumn: mockColumns[2],
        sourceIndex: 1,
        targetIndex: 2,
        dropPosition: 'before',
        newOrder
      }

      vm.handleColumnReorder(newOrder, dragEvent)

      expect(wrapper.emitted('columnReorder')).toBeTruthy()
      const emittedEvents = wrapper.emitted('columnReorder') as any[]
      expect(emittedEvents[0]).toEqual([newOrder, dragEvent])
    })

    it('should update column _dragOrder properties', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      const dragEvent: DragEvent = {
        type: 'reorder',
        sourceColumn: mockColumns[1],
        targetColumn: mockColumns[2],
        sourceIndex: 1,
        targetIndex: 2,
        dropPosition: 'before',
        newOrder
      }

      vm.handleColumnReorder(newOrder, dragEvent)

      const processedColumns = vm.processedColumns
      expect(processedColumns.find((c: TableColumn) => c.key === 'seq')?._dragOrder).toBe(0)
      expect(processedColumns.find((c: TableColumn) => c.key === 'name')?._dragOrder).toBe(1)
      expect(processedColumns.find((c: TableColumn) => c.key === 'id')?._dragOrder).toBe(2)
      expect(processedColumns.find((c: TableColumn) => c.key === 'age')?._dragOrder).toBe(3)
      expect(processedColumns.find((c: TableColumn) => c.key === 'email')?._dragOrder).toBe(4)
      expect(processedColumns.find((c: TableColumn) => c.key === 'actions')?._dragOrder).toBe(5)
    })
  })

  describe('Column Order Processing', () => {
    it('should reorder columns based on custom column order', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Simulate a column reorder
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      vm.columnOrder = newOrder
      
      await wrapper.vm.$nextTick()
      
      const processedColumns = vm.processedColumns
      expect(processedColumns[1].key).toBe('name') // name moved to position 1
      expect(processedColumns[2].key).toBe('id')   // id moved to position 2
    })

    it('should maintain fixed column grouping during reorder', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Reorder only normal columns, keep fixed columns in place
      const newOrder = ['seq', 'email', 'name', 'age', 'id', 'actions']
      vm.columnOrder = newOrder
      
      await wrapper.vm.$nextTick()
      
      const processedColumns = vm.processedColumns
      
      // Fixed columns should remain at the edges
      expect(processedColumns[0].key).toBe('seq') // left fixed
      expect(processedColumns[processedColumns.length - 1].key).toBe('actions') // right fixed
      
      // Normal columns should be reordered
      const normalColumns = processedColumns.filter((c: TableColumn) => !c.fixed)
      expect(normalColumns[0].key).toBe('email')
      expect(normalColumns[1].key).toBe('name')
      expect(normalColumns[2].key).toBe('age')
      expect(normalColumns[3].key).toBe('id')
    })
  })

  describe('Integration with Existing Features', () => {
    it('should maintain sorting functionality after column reorder', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Reorder columns first
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      vm.handleColumnReorder(newOrder, {
        type: 'reorder',
        sourceColumn: mockColumns[1],
        targetColumn: mockColumns[2],
        sourceIndex: 1,
        targetIndex: 2,
        dropPosition: 'before',
        newOrder
      })

      await wrapper.vm.$nextTick()

      // Verify sort functionality still works
      expect(vm.sortConfigs).toBeDefined()
      expect(vm.handleSort).toBeDefined()
      
      const processedColumns = vm.processedColumns
      const nameColumn = processedColumns.find((c: TableColumn) => c.key === 'name')
      expect(nameColumn?.sortable).toBe(true)
    })

    it('should maintain fixed column positioning after reorder', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Simulate column reorder
      const newOrder = ['seq', 'age', 'name', 'id', 'email', 'actions']
      vm.handleColumnReorder(newOrder, {
        type: 'reorder',
        sourceColumn: mockColumns[3], // age
        targetColumn: mockColumns[1], // id
        sourceIndex: 3,
        targetIndex: 1,
        dropPosition: 'before',
        newOrder
      })

      // Should call initializeFixedPositions
      expect(vm.initializeFixedPositions).toBeDefined()
    })

    it('should maintain virtual scrolling compatibility', async () => {
      const configWithVirtual = {
        ...mockTableConfig,
        virtual: {
          enabled: true,
          threshold: 10,
          itemHeight: 40
        }
      }

      wrapper = mount(Table, {
        props: {
          config: configWithVirtual,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Column reordering should work with virtual scrolling
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      vm.handleColumnReorder(newOrder, {
        type: 'reorder',
        newOrder
      })

      expect(vm.isVirtualEnabled).toBeDefined()
      expect(vm.visibleColumns).toBeDefined()
    })
  })

  describe('Event Handling', () => {
    it('should handle drag start events from header', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      const column = mockColumns[1] // id column

      vm.handleDragStart(column)

      // Should be handled (logged in current implementation)
      expect(vm.handleDragStart).toBeDefined()
    })

    it('should handle drag end events from header', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      const column = mockColumns[1] // id column

      vm.handleDragEnd(column, true)

      // Should be handled (logged in current implementation)
      expect(vm.handleDragEnd).toBeDefined()
    })

    it('should handle drag cancel events from header', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      const column = mockColumns[1] // id column

      vm.handleDragCancel('escape', column)

      // Should be handled (logged in current implementation)
      expect(vm.handleDragCancel).toBeDefined()
    })
  })

  describe('Performance and Cleanup', () => {
    it('should use requestAnimationFrame for fixed column updates', async () => {
      const rafSpy = vi.spyOn(window, 'requestAnimationFrame')

      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      const dragEvent: DragEvent = {
        type: 'reorder',
        newOrder
      }

      vm.handleColumnReorder(newOrder, dragEvent)

      expect(rafSpy).toHaveBeenCalled()

      rafSpy.mockRestore()
    })

    it('should handle errors in fixed column position updates gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Mock initializeFixedPositions to throw error
      vm.initializeFixedPositions = vi.fn().mockImplementation(() => {
        throw new Error('Mock error')
      })

      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      const dragEvent: DragEvent = {
        type: 'reorder',
        newOrder
      }

      // Should not throw error but log warning
      expect(() => {
        vm.handleColumnReorder(newOrder, dragEvent)
      }).not.toThrow()

      // Allow RAF to execute
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to update fixed column positions after reorder:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe('Data Consistency', () => {
    it('should maintain data integrity after column reorder', async () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Get original data
      const originalData = vm.finalDisplayData
      expect(originalData).toHaveLength(3)
      expect(originalData[0].name).toBe('Alice')

      // Reorder columns
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      vm.handleColumnReorder(newOrder, {
        type: 'reorder',
        newOrder
      })

      await wrapper.vm.$nextTick()

      // Data should remain unchanged
      const reorderedData = vm.finalDisplayData
      expect(reorderedData).toHaveLength(3)
      expect(reorderedData[0].name).toBe('Alice')
      expect(reorderedData[0].id).toBe(1)
    })

    it('should work with pagination enabled', async () => {
      const configWithPagination = {
        ...mockTableConfig,
        pagination: {
          enabled: true,
          pageSize: 2,
          currentPage: 1,
          total: 3
        }
      }

      wrapper = mount(Table, {
        props: {
          config: configWithPagination,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Reorder columns with pagination
      const newOrder = ['seq', 'name', 'id', 'age', 'email', 'actions']
      vm.handleColumnReorder(newOrder, {
        type: 'reorder',
        newOrder
      })

      await wrapper.vm.$nextTick()

      // Pagination should still work
      expect(vm.finalDisplayData).toHaveLength(2) // pageSize = 2
      expect(vm.paginationState.currentPage).toBe(1)
    })
  })

  describe('Drag Drop Configuration', () => {
    it('should pass drag drop config to TableHeader', () => {
      const dragDropConfig = {
        enabled: true,
        animationDuration: 500,
        dragThreshold: 10,
        constrainToGroup: true,
        allowSpecialColumnDrag: false,
        disabledColumnTypes: ['seq', 'selection']
      }

      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig
        }
      })

      const tableHeader = wrapper.findComponent({ name: 'TableHeader' })
      expect(tableHeader.props('dragDropConfig')).toEqual(dragDropConfig)
    })

    it('should work with default drag drop config', () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig
          // No dragDropConfig provided
        }
      })

      const tableHeader = wrapper.findComponent({ name: 'TableHeader' })
      expect(tableHeader.props('dragDropConfig')).toEqual({ enabled: true })
    })

    it('should disable drag when dragDropConfig.enabled is false', () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: false }
        }
      })

      const tableHeader = wrapper.findComponent({ name: 'TableHeader' })
      expect(tableHeader.props('dragDropConfig')).toEqual({ enabled: false })
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty column order gracefully', () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Set empty order
      vm.handleColumnReorder([], {
        type: 'reorder',
        newOrder: []
      })

      expect(vm.columnOrder).toEqual([])
      expect(vm.hasCustomColumnOrder).toBe(false)
    })

    it('should handle columns without keys gracefully', () => {
      const invalidColumns = [
        { key: '', title: 'Invalid' }, // Empty key
        { title: 'No Key' as any } // Missing key
      ]

      expect(() => {
        wrapper = mount(Table, {
          props: {
            config: {
              ...mockTableConfig,
              columns: invalidColumns
            },
            dragDropConfig: { enabled: true }
          }
        })
      }).not.toThrow()

      // Table should show error state instead of crashing
      const errorBoundary = wrapper.find('.table-error-boundary')
      expect(errorBoundary.exists()).toBe(true)
    })

    it('should handle reorder with missing columns in new order', () => {
      wrapper = mount(Table, {
        props: {
          config: mockTableConfig,
          dragDropConfig: { enabled: true }
        }
      })

      const vm = wrapper.vm as any
      
      // Incomplete order (missing some columns)
      const incompleteOrder = ['seq', 'name', 'id'] // Missing age, email, actions
      vm.handleColumnReorder(incompleteOrder, {
        type: 'reorder',
        newOrder: incompleteOrder
      })

      expect(vm.columnOrder).toEqual(incompleteOrder)
      
      // Should handle gracefully and show all columns
      const processedColumns = vm.processedColumns
      expect(processedColumns).toHaveLength(6) // All original columns should be present
    })
  })
})