import { describe, it, expect, beforeEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import TablePagination from '../TablePagination.vue'

describe('TablePagination', () => {
  let wrapper: VueWrapper<any>

  const defaultProps = {
    currentPage: 1,
    pageSize: 10,
    total: 100,
    totalPages: 10,
    hasNextPage: true,
    hasPrevPage: false,
    startIndex: 0,
    endIndex: 10,
    pageRange: [1, 2, 3, 4, 5, -1, 10],
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: true,
    pageSizeOptions: [10, 20, 50, 100]
  }

  beforeEach(() => {
    wrapper = mount(TablePagination, {
      props: defaultProps
    })
  })

  afterEach(() => {
    wrapper.unmount()
  })

  describe('rendering', () => {
    it('should render pagination component', () => {
      expect(wrapper.find('.table-pagination').exists()).toBe(true)
    })

    it('should display total information', () => {
      const totalElement = wrapper.find('.pagination-total')
      expect(totalElement.exists()).toBe(true)
      expect(totalElement.text()).toContain('共 100 条记录')
      expect(totalElement.text()).toContain('显示第 1-10 条')
    })

    it('should render page size changer', () => {
      const sizeChanger = wrapper.find('.pagination-size-changer')
      expect(sizeChanger.exists()).toBe(true)

      const select = sizeChanger.find('select')
      expect(select.exists()).toBe(true)

      const options = select.findAll('option')
      expect(options).toHaveLength(4)
      expect(options[0].text()).toBe('10 条')
      expect(options[1].text()).toBe('20 条')
    })

    it('should render pagination controls', () => {
      const controls = wrapper.find('.pagination-controls')
      expect(controls.exists()).toBe(true)

      expect(controls.find('.pagination-first').exists()).toBe(true)
      expect(controls.find('.pagination-prev').exists()).toBe(true)
      expect(controls.find('.pagination-next').exists()).toBe(true)
      expect(controls.find('.pagination-last').exists()).toBe(true)
    })

    it('should render page numbers', () => {
      const pages = wrapper.find('.pagination-pages')
      expect(pages.exists()).toBe(true)

      const pageButtons = pages.findAll('.pagination-page')
      const ellipsis = pages.findAll('.pagination-ellipsis')

      expect(pageButtons).toHaveLength(6) // 1,2,3,4,5,10
      expect(ellipsis).toHaveLength(1) // One ellipsis
    })

    it('should render quick jumper', () => {
      const jumper = wrapper.find('.pagination-jumper')
      expect(jumper.exists()).toBe(true)

      const input = jumper.find('input')
      expect(input.exists()).toBe(true)
      expect(input.attributes('type')).toBe('number')
      expect(input.attributes('min')).toBe('1')
      expect(input.attributes('max')).toBe('10')
    })

    it('should highlight current page', () => {
      const activeButton = wrapper.find('.pagination-page-active')
      expect(activeButton.exists()).toBe(true)
      expect(activeButton.text()).toBe('1')
    })

    it('should disable navigation buttons at boundaries', () => {
      const firstBtn = wrapper.find('.pagination-first')
      const prevBtn = wrapper.find('.pagination-prev')

      expect(firstBtn.attributes('disabled')).toBeDefined()
      expect(prevBtn.attributes('disabled')).toBeDefined()
    })
  })

  describe('conditional rendering', () => {
    it('should hide total when showTotal is false', async () => {
      await wrapper.setProps({ showTotal: false })
      expect(wrapper.find('.pagination-total').exists()).toBe(false)
    })

    it('should hide size changer when showSizeChanger is false', async () => {
      await wrapper.setProps({ showSizeChanger: false })
      expect(wrapper.find('.pagination-size-changer').exists()).toBe(false)
    })

    it('should hide quick jumper when showQuickJumper is false', async () => {
      await wrapper.setProps({ showQuickJumper: false })
      expect(wrapper.find('.pagination-jumper').exists()).toBe(false)
    })

    it('should hide controls when totalPages is 1', async () => {
      await wrapper.setProps({
        totalPages: 1,
        pageRange: [1],
        hasNextPage: false
      })
      expect(wrapper.find('.pagination-controls').exists()).toBe(false)
    })

    it('should hide size changer when only one option', async () => {
      await wrapper.setProps({ pageSizeOptions: [10] })
      expect(wrapper.find('.pagination-size-changer').exists()).toBe(false)
    })
  })

  describe('user interactions', () => {
    it('should emit pageChange when page button is clicked', async () => {
      const pageButton = wrapper.findAll('.pagination-page')[1] // Page 2
      await pageButton.trigger('click')

      expect(wrapper.emitted('pageChange')).toBeTruthy()
      expect(wrapper.emitted('pageChange')?.[0]).toEqual([2])
    })

    it('should emit pageChange when navigation buttons are clicked', async () => {
      // Test with a page that has both prev and next available
      await wrapper.setProps({
        currentPage: 5,
        hasPrevPage: true,
        hasNextPage: true
      })

      const nextBtn = wrapper.find('.pagination-next')
      await nextBtn.trigger('click')

      expect(wrapper.emitted('pageChange')).toBeTruthy()
      expect(wrapper.emitted('pageChange')?.[0]).toEqual([6])
    })

    it('should emit pageSizeChange when page size is changed', async () => {
      const select = wrapper.find('.size-changer-select')
      await select.setValue('20')

      expect(wrapper.emitted('pageSizeChange')).toBeTruthy()
      expect(wrapper.emitted('pageSizeChange')?.[0]).toEqual([20])

      expect(wrapper.emitted('showSizeChange')).toBeTruthy()
      expect(wrapper.emitted('showSizeChange')?.[0]).toEqual([1, 20])
    })

    it('should handle quick jumper input', async () => {
      const input = wrapper.find('.jumper-input')

      await input.setValue('5')
      await input.trigger('keydown.enter')

      expect(wrapper.emitted('pageChange')).toBeTruthy()
      expect(wrapper.emitted('pageChange')?.[0]).toEqual([5])
    })

    it('should handle quick jumper blur', async () => {
      const input = wrapper.find('.jumper-input')

      await input.setValue('3')
      await input.trigger('blur')

      expect(wrapper.emitted('pageChange')).toBeTruthy()
      expect(wrapper.emitted('pageChange')?.[0]).toEqual([3])
    })

    it('should not emit events when disabled', async () => {
      await wrapper.setProps({ disabled: true })

      const pageButton = wrapper.findAll('.pagination-page')[1]
      await pageButton.trigger('click')

      expect(wrapper.emitted('pageChange')).toBeFalsy()
    })

    it('should not emit pageChange for current page', async () => {
      const currentPageButton = wrapper.find('.pagination-page-active')
      await currentPageButton.trigger('click')

      expect(wrapper.emitted('pageChange')).toBeFalsy()
    })
  })

  describe('input validation', () => {
    it('should validate quick jumper input range', async () => {
      const input = wrapper.find('.jumper-input')

      // Test invalid values
      await input.setValue('0')
      await input.trigger('enter')
      expect(wrapper.emitted('pageChange')).toBeFalsy()

      await input.setValue('11')
      await input.trigger('enter')
      expect(wrapper.emitted('pageChange')).toBeFalsy()

      await input.setValue('abc')
      await input.trigger('enter')
      expect(wrapper.emitted('pageChange')).toBeFalsy()
    })

    it('should clear jumper input after successful submission', async () => {
      const input = wrapper.find('.jumper-input')

      await input.setValue('5')
      await input.trigger('keydown.enter')
      await wrapper.vm.$nextTick()

      expect((input.element as HTMLInputElement).value).toBe('')
    })

    it('should clear jumper input when page changes externally', async () => {
      const input = wrapper.find('.jumper-input')
      await input.setValue('5')

      await wrapper.setProps({ currentPage: 3 })

      expect((input.element as HTMLInputElement).value).toBe('')
    })
  })

  describe('styling and classes', () => {
    it('should apply size classes', async () => {
      await wrapper.setProps({ size: 'small' })
      expect(wrapper.find('.pagination-small').exists()).toBe(true)

      await wrapper.setProps({ size: 'large' })
      expect(wrapper.find('.pagination-large').exists()).toBe(true)
    })

    it('should apply simple mode class', async () => {
      await wrapper.setProps({ simple: true })
      expect(wrapper.find('.pagination-simple').exists()).toBe(true)
    })

    it('should apply disabled class', async () => {
      await wrapper.setProps({ disabled: true })
      expect(wrapper.find('.pagination-disabled').exists()).toBe(true)
    })
  })

  describe('slots', () => {
    it('should render custom total slot', () => {
      const wrapperWithSlot = mount(TablePagination, {
        props: defaultProps,
        slots: {
          total: '<div class="custom-total">Custom total info</div>'
        }
      })

      expect(wrapperWithSlot.find('.custom-total').exists()).toBe(true)
      expect(wrapperWithSlot.find('.custom-total').text()).toBe('Custom total info')

      wrapperWithSlot.unmount()
    })

    it('should render custom navigation icons', () => {
      const wrapperWithSlots = mount(TablePagination, {
        props: defaultProps,
        slots: {
          'first-icon': '<span class="custom-first">First</span>',
          'prev-icon': '<span class="custom-prev">Prev</span>',
          'next-icon': '<span class="custom-next">Next</span>',
          'last-icon': '<span class="custom-last">Last</span>'
        }
      })

      expect(wrapperWithSlots.find('.custom-first').exists()).toBe(true)
      expect(wrapperWithSlots.find('.custom-prev').exists()).toBe(true)
      expect(wrapperWithSlots.find('.custom-next').exists()).toBe(true)
      expect(wrapperWithSlots.find('.custom-last').exists()).toBe(true)

      wrapperWithSlots.unmount()
    })

    it('should render custom ellipsis slot', () => {
      const wrapperWithSlot = mount(TablePagination, {
        props: defaultProps,
        slots: {
          ellipsis: '<span class="custom-ellipsis">•••</span>'
        }
      })

      expect(wrapperWithSlot.find('.custom-ellipsis').exists()).toBe(true)
      expect(wrapperWithSlot.find('.custom-ellipsis').text()).toBe('•••')

      wrapperWithSlot.unmount()
    })
  })

  describe('accessibility', () => {
    it('should have proper button titles', () => {
      const firstBtn = wrapper.find('.pagination-first')
      const prevBtn = wrapper.find('.pagination-prev')
      const nextBtn = wrapper.find('.pagination-next')
      const lastBtn = wrapper.find('.pagination-last')

      expect(firstBtn.attributes('title')).toBe('首页')
      expect(prevBtn.attributes('title')).toBe('上一页')
      expect(nextBtn.attributes('title')).toBe('下一页')
      expect(lastBtn.attributes('title')).toBe('末页')
    })

    it('should have proper page button titles', () => {
      const pageButtons = wrapper.findAll('.pagination-page')
      expect(pageButtons[0].attributes('title')).toBe('第 1 页')
      expect(pageButtons[1].attributes('title')).toBe('第 2 页')
    })

    it('should have proper input attributes', () => {
      const input = wrapper.find('.jumper-input')
      expect(input.attributes('min')).toBe('1')
      expect(input.attributes('max')).toBe('10')
      expect(input.attributes('placeholder')).toBe('1-10')
    })
  })

  describe('edge cases', () => {
    it('should handle single page scenario', async () => {
      await wrapper.setProps({
        currentPage: 1,
        totalPages: 1,
        pageRange: [1],
        hasNextPage: false,
        hasPrevPage: false
      })

      expect(wrapper.find('.pagination-controls').exists()).toBe(false)
    })

    it('should handle zero total scenario', async () => {
      await wrapper.setProps({
        total: 0,
        totalPages: 1,
        startIndex: 0,
        endIndex: 0,
        pageRange: [1]
      })

      const totalElement = wrapper.find('.pagination-total')
      expect(totalElement.text()).toContain('共 0 条记录')
      expect(totalElement.text()).not.toContain('显示第')
    })

    it('should handle large page numbers', async () => {
      await wrapper.setProps({
        currentPage: 999,
        totalPages: 1000,
        pageRange: [1, -1, 998, 999, 1000]
      })

      const activeButton = wrapper.find('.pagination-page-active')
      expect(activeButton.text()).toBe('999')
    })
  })
})
