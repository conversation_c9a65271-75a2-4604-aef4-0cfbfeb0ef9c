import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import Table from '../Table.vue'
import TableHeader from '../TableHeader.vue'
import TableCell from '../TableCell.vue'
import type { TableConfig, TableRow } from '@/types'

describe('Selection Column Basic Tests', () => {
  let sampleData: TableRow[]

  beforeEach(() => {
    sampleData = [
      { id: 1, name: '<PERSON>', age: 25 },
      { id: 2, name: '<PERSON>', age: 30 },
      { id: 3, name: '<PERSON>', age: 35 }
    ]
  })

  describe('Table Configuration Validation', () => {
    it('should allow empty title for selection columns', () => {
      const config: TableConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            selectionConfig: {
              enabled: true,
              mode: 'multiple'
            }
          },
          { key: 'name', title: 'Name' }
        ],
        data: sampleData
      }

      expect(() => mount(Table, { props: { config } })).not.toThrow()
    })

    it('should process selection column configuration correctly', async () => {
      const config: TableConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            selectionConfig: {
              enabled: true,
              mode: 'multiple'
            }
          }
        ],
        data: sampleData
      }

      const wrapper = mount(Table, { props: { config } })
      await nextTick()

      // Should not throw validation errors
      expect(wrapper.vm).toBeTruthy()
    })
  })

  describe('TableHeader Selection Column', () => {
    it('should render select-all checkbox in multiple mode', async () => {
      const wrapper = mount(TableHeader, {
        props: {
          columns: [
            {
              key: 'selection',
              title: '',
              type: 'selection'
            }
          ],
          selectionConfig: {
            enabled: true,
            mode: 'multiple',
            showSelectAll: true
          },
          data: sampleData,
          selectedRows: new Set<string | number>()
        }
      })

      await nextTick()

      const selectAllCheckbox = wrapper.find('.selection-checkbox')
      expect(selectAllCheckbox.exists()).toBe(true)
      expect((selectAllCheckbox.element as HTMLInputElement).type).toBe('checkbox')
    })

    it('should render single selection header correctly', async () => {
      const wrapper = mount(TableHeader, {
        props: {
          columns: [
            {
              key: 'selection',
              title: '选择',
              type: 'selection'
            }
          ],
          selectionConfig: {
            enabled: true,
            mode: 'single'
          },
          data: sampleData,
          selectedRows: new Set<string | number>()
        }
      })

      await nextTick()

      const selectionTitle = wrapper.find('.selection-title')
      expect(selectionTitle.exists()).toBe(true)
      expect(selectionTitle.text()).toBe('选择')

      const selectAllCheckbox = wrapper.find('.selection-checkbox')
      expect(selectAllCheckbox.exists()).toBe(false)
    })

    it('should emit selectAll event when clicked', async () => {
      const wrapper = mount(TableHeader, {
        props: {
          columns: [
            {
              key: 'selection',
              title: '',
              type: 'selection'
            }
          ],
          selectionConfig: {
            enabled: true,
            mode: 'multiple',
            showSelectAll: true
          },
          data: sampleData,
          selectedRows: new Set<string | number>()
        }
      })

      await nextTick()

      const selectAllCheckbox = wrapper.find('.selection-checkbox')
      await selectAllCheckbox.setValue(true)

      const emittedEvents = wrapper.emitted('selectAll')
      expect(emittedEvents).toBeTruthy()
      expect(emittedEvents?.[0]).toEqual([true, expect.any(Event)])
    })
  })

  describe('TableCell Selection Column', () => {
    it('should render checkbox for multiple selection', async () => {
      const mockRow: TableRow = { id: 1, name: 'John Doe' }

      const wrapper = mount(TableCell, {
        props: {
          column: {
            key: 'selection',
            title: '',
            type: 'selection'
          },
          row: mockRow,
          value: null,
          index: 0,
          selectionConfig: {
            enabled: true,
            mode: 'multiple'
          }
        }
      })

      await nextTick()

      const checkbox = wrapper.find('input[type="checkbox"]')
      expect(checkbox.exists()).toBe(true)
    })

    it('should render radio button for single selection', async () => {
      const mockRow: TableRow = { id: 1, name: 'John Doe' }

      const wrapper = mount(TableCell, {
        props: {
          column: {
            key: 'selection',
            title: '',
            type: 'selection'
          },
          row: mockRow,
          value: null,
          index: 0,
          selectionConfig: {
            enabled: true,
            mode: 'single'
          }
        }
      })

      await nextTick()

      const radioButton = wrapper.find('input[type="radio"]')
      expect(radioButton.exists()).toBe(true)
      expect((radioButton.element as HTMLInputElement).name).toBe('table-selection-selection')
    })

    it('should emit selectionChange event when clicked', async () => {
      const mockRow: TableRow = { id: 1, name: 'John Doe' }

      const wrapper = mount(TableCell, {
        props: {
          column: {
            key: 'selection',
            title: '',
            type: 'selection'
          },
          row: mockRow,
          value: null,
          index: 0,
          selectionConfig: {
            enabled: true,
            mode: 'multiple'
          }
        }
      })

      await nextTick()

      const checkbox = wrapper.find('input[type="checkbox"]')
      await checkbox.setValue(true)

      const emittedEvents = wrapper.emitted('selection-change')
      expect(emittedEvents).toBeTruthy()
      expect(emittedEvents?.[0]).toEqual([mockRow, true, expect.any(Event)])
    })

    it('should be disabled when row is disabled', async () => {
      const mockRow: TableRow = { id: 1, name: 'John Doe' }

      const wrapper = mount(TableCell, {
        props: {
          column: {
            key: 'selection',
            title: '',
            type: 'selection'
          },
          row: mockRow,
          value: null,
          index: 0,
          disabled: true,
          selectionConfig: {
            enabled: true,
            mode: 'multiple'
          }
        }
      })

      await nextTick()

      const checkbox = wrapper.find('input[type="checkbox"]')
      expect((checkbox.element as HTMLInputElement).disabled).toBe(true)
    })
  })

  describe('Selection Configuration Processing', () => {
    it('should apply correct default properties to selection column', async () => {
      const config: TableConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            selectionConfig: {
              enabled: true,
              mode: 'multiple'
            }
            // No width, fixed, etc. - should use defaults
          },
          { key: 'name', title: 'Name' }
        ],
        data: sampleData
      }

      const wrapper = mount(Table, { props: { config } })
      await nextTick()

      // The component should process the selection column and apply defaults
      expect(wrapper.vm).toBeTruthy()
      expect(wrapper.props('config')).toEqual(config)
    })
  })

  describe('Selection Events', () => {
    it('should emit rowSelect event when selection changes', async () => {
      const config: TableConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            selectionConfig: {
              enabled: true,
              mode: 'multiple'
            }
          },
          { key: 'name', title: 'Name' }
        ],
        data: sampleData
      }

      const mockRowSelect = vi.fn()
      const wrapper = mount(Table, {
        props: { config },
        attrs: {
          onRowSelect: mockRowSelect
        }
      })

      await nextTick()

      // Simulate a selection change by calling the handler directly
      const tableVm = wrapper.vm as any
      if (tableVm.handleSelectionChange) {
        tableVm.handleSelectionChange(sampleData[0], true, new Event('change'))
        // Verify that the event was emitted
        expect(mockRowSelect).toHaveBeenCalled()
      }
    })
  })
})