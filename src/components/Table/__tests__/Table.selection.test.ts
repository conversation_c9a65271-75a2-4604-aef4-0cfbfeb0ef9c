import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import Table from '../Table.vue'
import TableHeader from '../TableHeader.vue'
import TableCell from '../TableCell.vue'
import type { TableConfig, TableRow } from '@/types'

describe('Table Selection Functionality', () => {
  let wrapper: VueWrapper<any>
  let sampleData: TableRow[]

  beforeEach(() => {
    sampleData = [
      { id: 1, name: '<PERSON>', age: 25, department: 'Engineering', status: 'active' },
      { id: 2, name: '<PERSON>', age: 30, department: 'Marketing', status: 'active' },
      { id: 3, name: '<PERSON>', age: 35, department: 'Sales', status: 'inactive' },
      { id: 4, name: '<PERSON>', age: 28, department: 'HR', status: 'active' },
      { id: 5, name: '<PERSON>', age: 32, department: 'Finance', status: 'active' }
    ]
  })

  describe('Multiple Selection Mode', () => {
    let multipleConfig: TableConfig

    beforeEach(() => {
      multipleConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            width: 60,
            selectionConfig: {
              enabled: true,
              mode: 'multiple',
              showSelectAll: true
            }
          },
          { key: 'name', title: 'Name' },
          { key: 'age', title: 'Age' },
          { key: 'department', title: 'Department' }
        ],
        data: sampleData
      }
    })

    it('should render selection column with checkboxes', async () => {
      wrapper = mount(Table, {
        props: { config: multipleConfig }
      })

      await nextTick()

      // Check if selection column header exists
      const headerCells = wrapper.findAll('.header-cell')
      expect(headerCells.length).toBeGreaterThan(0)

      // Check if selection column cells exist - use correct div-based selector
      const tableBodyCheckboxes = wrapper.findAll('.table-body input[type="checkbox"]')
      expect(tableBodyCheckboxes.length).toBe(sampleData.length)

      // Check if all checkboxes exist (including header select-all)
      const allCheckboxes = wrapper.findAll('input[type="checkbox"]')
      expect(allCheckboxes.length).toBeGreaterThan(sampleData.length) // Including header select-all
    })

    it('should handle individual row selection', async () => {
      const mockRowSelect = vi.fn()
      wrapper = mount(Table, {
        props: { config: multipleConfig },
        attrs: {
          onRowSelect: mockRowSelect
        }
      })

      await nextTick()

      // Find first row checkbox and click it - use correct selector
      const firstRowCheckbox = wrapper.find('.table-body input[type="checkbox"]')
      await firstRowCheckbox.setValue(true)

      await nextTick()

      // Expect processed data with internal properties, not original sampleData
      const expectedRow = expect.objectContaining({
        ...sampleData[0],
        _id: sampleData[0].id,
        _selected: true,
        _editing: false,
        _disabled: false
      })

      expect(mockRowSelect).toHaveBeenCalledWith([expectedRow])
    })

    it('should handle select-all functionality', async () => {
      const mockRowSelect = vi.fn()
      wrapper = mount(Table, {
        props: { config: multipleConfig },
        attrs: {
          onRowSelect: mockRowSelect
        }
      })

      await nextTick()

      // Find header select-all checkbox and click it - use correct selector
      const selectAllCheckbox = wrapper.find('.table-header input[type="checkbox"]')
      expect(selectAllCheckbox.exists()).toBe(true)

      await selectAllCheckbox.setValue(true)
      await nextTick()

      // Expect processed data array with internal properties
      const expectedRows = sampleData.map(row => expect.objectContaining({
        ...row,
        _id: row.id,
        _selected: true,
        _editing: false,
        _disabled: false
      }))

      expect(mockRowSelect).toHaveBeenCalledWith(expectedRows)
    })

    it('should show indeterminate state when some rows are selected', async () => {
      wrapper = mount(Table, {
        props: { config: multipleConfig }
      })

      await nextTick()

      // Select first row - use correct selector
      const firstRowCheckbox = wrapper.find('.table-body input[type="checkbox"]')
      await firstRowCheckbox.setValue(true)

      await nextTick()

      // Check if header checkbox shows indeterminate state - use correct selector
      const selectAllCheckbox = wrapper.find('.table-header input[type="checkbox"]')
      expect((selectAllCheckbox.element as HTMLInputElement).indeterminate).toBe(true)
    })
  })

  describe('Single Selection Mode', () => {
    let singleConfig: TableConfig

    beforeEach(() => {
      singleConfig = {
        columns: [
          {
            key: 'selection',
            title: '选择',
            type: 'selection',
            width: 80,
            selectionConfig: {
              enabled: true,
              mode: 'single'
            }
          },
          { key: 'name', title: 'Name' },
          { key: 'department', title: 'Department' }
        ],
        data: sampleData
      }
    })

    it('should render selection column with radio buttons', async () => {
      wrapper = mount(Table, {
        props: { config: singleConfig }
      })

      await nextTick()

      // Check if radio buttons exist - use correct selector
      const radioButtons = wrapper.findAll('.table-body input[type="radio"]')
      expect(radioButtons.length).toBe(sampleData.length)
    })

    it('should handle single row selection', async () => {
      const mockRowSelect = vi.fn()
      wrapper = mount(Table, {
        props: { config: singleConfig },
        attrs: {
          onRowSelect: mockRowSelect
        }
      })

      await nextTick()

      // Select first row - use correct selector
      const firstRowRadio = wrapper.find('.table-body input[type="radio"]')
      await firstRowRadio.setValue(true)

      await nextTick()

      // Expect processed data with internal properties
      const expectedRow = expect.objectContaining({
        ...sampleData[0],
        _id: sampleData[0].id,
        _selected: true,
        _editing: false,
        _disabled: false
      })

      expect(mockRowSelect).toHaveBeenCalledWith([expectedRow])
    })

    it('should not show select-all checkbox in single mode', async () => {
      wrapper = mount(Table, {
        props: { config: singleConfig }
      })

      await nextTick()

      // Check that header doesn't have checkbox - use correct selector
      const headerCheckbox = wrapper.find('.table-header input[type="checkbox"]')
      expect(headerCheckbox.exists()).toBe(false)
    })
  })

  describe('Disabled Row Selection', () => {
    let disabledConfig: TableConfig

    beforeEach(() => {
      disabledConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            width: 60,
            selectionConfig: {
              enabled: true,
              mode: 'multiple',
              showSelectAll: true,
              getCheckboxProps: (row: TableRow) => ({
                disabled: row.status === 'inactive'
              })
            }
          },
          { key: 'name', title: 'Name' },
          { key: 'status', title: 'Status' },
          { key: 'department', title: 'Department' }
        ],
        data: sampleData
      }
    })

    it('should disable checkboxes for inactive rows', async () => {
      wrapper = mount(Table, {
        props: { config: disabledConfig }
      })

      await nextTick()

      // Use correct selector for div-based table
      const checkboxes = wrapper.findAll('.table-body input[type="checkbox"]')

      // Check that inactive row has disabled checkbox
      const inactiveRowIndex = sampleData.findIndex(row => row.status === 'inactive')
      expect((checkboxes[inactiveRowIndex].element as HTMLInputElement).disabled).toBe(true)

      // Check that active rows have enabled checkboxes
      const activeRowIndices = sampleData
        .map((row, index) => ({ row, index }))
        .filter(({ row }) => row.status === 'active')
        .map(({ index }) => index)

      activeRowIndices.forEach(index => {
        expect((checkboxes[index].element as HTMLInputElement).disabled).toBe(false)
      })
    })

    it('should exclude disabled rows from select-all', async () => {
      const mockRowSelect = vi.fn()
      wrapper = mount(Table, {
        props: { config: disabledConfig },
        attrs: {
          onRowSelect: mockRowSelect
        }
      })

      await nextTick()

      // Click select-all - use correct selector
      const selectAllCheckbox = wrapper.find('.table-header input[type="checkbox"]')
      await selectAllCheckbox.setValue(true)

      await nextTick()

      // Should only select active rows - expect processed data with internal properties
      const expectedSelectedRows = sampleData
        .filter(row => row.status === 'active')
        .map(row => expect.objectContaining({
          ...row,
          _id: row.id,
          _selected: true,
          _editing: false,
          _disabled: false
        }))

      expect(mockRowSelect).toHaveBeenCalledWith(expectedSelectedRows)
    })
  })

  describe('Selection Column Configuration', () => {
    it('should apply correct width to selection column', async () => {
      const config: TableConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            width: 80,
            selectionConfig: {
              enabled: true,
              mode: 'multiple'
            }
          },
          { key: 'name', title: 'Name' }
        ],
        data: sampleData
      }

      wrapper = mount(Table, {
        props: { config }
      })

      await nextTick()

      const selectionHeaderCell = wrapper.find('.header-cell.cell-selection')
      expect(selectionHeaderCell.exists()).toBe(true)
    })

    it('should fix selection column to left by default', async () => {
      const config: TableConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            selectionConfig: {
              enabled: true,
              mode: 'multiple'
            }
          },
          { key: 'name', title: 'Name' }
        ],
        data: sampleData
      }

      wrapper = mount(Table, {
        props: { config }
      })

      await nextTick()

      const selectionHeaderCell = wrapper.find('.header-cell.cell-selection')
      expect(selectionHeaderCell.classes()).toContain('cell-fixed-left')
    })
  })

  describe('Selection Events', () => {
    it('should emit rowSelect event with correct data', async () => {
      const config: TableConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            selectionConfig: {
              enabled: true,
              mode: 'multiple'
            }
          },
          { key: 'name', title: 'Name' }
        ],
        data: sampleData
      }

      wrapper = mount(Table, {
        props: { config }
      })

      await nextTick()

      // Select first row - use correct selector
      const firstRowCheckbox = wrapper.find('.table-body input[type="checkbox"]')
      await firstRowCheckbox.setValue(true)

      await nextTick()

      // Check emitted events - expect processed data with internal properties
      const emittedEvents = wrapper.emitted('rowSelect')
      expect(emittedEvents).toBeTruthy()
      
      const expectedRow = expect.objectContaining({
        ...sampleData[0],
        _id: sampleData[0].id,
        _selected: true,
        _editing: false,
        _disabled: false
      })
      
      expect(emittedEvents?.[0]).toEqual([[expectedRow]])
    })

    it('should emit correct data for deselection', async () => {
      const config: TableConfig = {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection',
            selectionConfig: {
              enabled: true,
              mode: 'multiple'
            }
          },
          { key: 'name', title: 'Name' }
        ],
        data: sampleData
      }

      wrapper = mount(Table, {
        props: { config }
      })

      await nextTick()

      // Select then deselect first row - use correct selector
      const firstRowCheckbox = wrapper.find('.table-body input[type="checkbox"]')
      await firstRowCheckbox.setValue(true)
      await nextTick()
      await firstRowCheckbox.setValue(false)
      await nextTick()

      // Check final emitted event
      const emittedEvents = wrapper.emitted('rowSelect')
      expect(emittedEvents).toBeTruthy()
      expect(emittedEvents?.[emittedEvents?.length - 1]).toEqual([[]])
    })
  })
})

describe('TableHeader Selection Tests', () => {
  let wrapper: VueWrapper<any>
  let sampleData: TableRow[]

  beforeEach(() => {
    sampleData = [
      { id: 1, name: 'John Doe', age: 25 },
      { id: 2, name: 'Jane Smith', age: 30 },
      { id: 3, name: 'Bob Johnson', age: 35 }
    ]
  })

  it('should render select-all checkbox in multiple mode', async () => {
    wrapper = mount(TableHeader, {
      props: {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection'
          }
        ],
        selectionConfig: {
          enabled: true,
          mode: 'multiple',
          showSelectAll: true
        },
        data: sampleData,
        selectedRows: new Set<string | number>()
      }
    })

    await nextTick()

    const selectAllCheckbox = wrapper.find('.selection-checkbox')
    expect(selectAllCheckbox.exists()).toBe(true)
    expect((selectAllCheckbox.element as HTMLInputElement).type).toBe('checkbox')
  })

  it('should not render select-all checkbox in single mode', async () => {
    wrapper = mount(TableHeader, {
      props: {
        columns: [
          {
            key: 'selection',
            title: '选择',
            type: 'selection'
          }
        ],
        selectionConfig: {
          enabled: true,
          mode: 'single'
        },
        data: sampleData,
        selectedRows: new Set<string | number>()
      }
    })

    await nextTick()

    const selectionTitle = wrapper.find('.selection-title')
    expect(selectionTitle.exists()).toBe(true)
    expect(selectionTitle.text()).toBe('选择')
  })

  it('should emit selectAll event when clicked', async () => {
    wrapper = mount(TableHeader, {
      props: {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection'
          }
        ],
        selectionConfig: {
          enabled: true,
          mode: 'multiple',
          showSelectAll: true
        },
        data: sampleData,
        selectedRows: new Set<string | number>()
      }
    })

    await nextTick()

    const selectAllCheckbox = wrapper.find('.selection-checkbox')
    await selectAllCheckbox.setValue(true)

    const emittedEvents = wrapper.emitted('selectAll')
    expect(emittedEvents).toBeTruthy()
    expect(emittedEvents?.[0]).toEqual([true, expect.any(Event)])
  })

  it('should show correct checked state based on selection', async () => {
    wrapper = mount(TableHeader, {
      props: {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection'
          }
        ],
        selectionConfig: {
          enabled: true,
          mode: 'multiple',
          showSelectAll: true
        },
        data: sampleData,
        selectedRows: new Set<string | number>([1, 2, 3])
      }
    })

    await nextTick()

    const selectAllCheckbox = wrapper.find('.selection-checkbox')
    expect((selectAllCheckbox.element as HTMLInputElement).checked).toBe(true)
  })

  it('should show indeterminate state when partially selected', async () => {
    wrapper = mount(TableHeader, {
      props: {
        columns: [
          {
            key: 'selection',
            title: '',
            type: 'selection'
          }
        ],
        selectionConfig: {
          enabled: true,
          mode: 'multiple',
          showSelectAll: true
        },
        data: sampleData,
        selectedRows: new Set<string | number>([1, 2])
      }
    })

    await nextTick()

    const selectAllCheckbox = wrapper.find('.selection-checkbox')
    expect((selectAllCheckbox.element as HTMLInputElement).indeterminate).toBe(true)
  })
})

describe('TableCell Selection Tests', () => {
  let wrapper: VueWrapper<any>
  let mockRow: TableRow

  beforeEach(() => {
    mockRow = {
      id: 1,
      name: 'John Doe',
      age: 25
    }
  })

  it('should render checkbox for selection column in multiple mode', async () => {
    wrapper = mount(TableCell, {
      props: {
        column: {
          key: 'selection',
          title: '',
          type: 'selection'
        },
        row: mockRow,
        value: null,
        index: 0,
        selectionConfig: {
          enabled: true,
          mode: 'multiple'
        }
      }
    })

    await nextTick()

    const checkbox = wrapper.find('input[type="checkbox"]')
    expect(checkbox.exists()).toBe(true)
  })

  it('should render radio button for selection column in single mode', async () => {
    wrapper = mount(TableCell, {
      props: {
        column: {
          key: 'selection',
          title: '',
          type: 'selection'
        },
        row: mockRow,
        value: null,
        index: 0,
        selectionConfig: {
          enabled: true,
          mode: 'single'
        }
      }
    })

    await nextTick()

    const radioButton = wrapper.find('input[type="radio"]')
    expect(radioButton.exists()).toBe(true)
  })

  it('should emit selectionChange event when clicked', async () => {
    wrapper = mount(TableCell, {
      props: {
        column: {
          key: 'selection',
          title: '',
          type: 'selection'
        },
        row: mockRow,
        value: null,
        index: 0,
        selectionConfig: {
          enabled: true,
          mode: 'multiple'
        }
      }
    })

    await nextTick()

    const checkbox = wrapper.find('input[type="checkbox"]')
    await checkbox.setValue(true)

    const emittedEvents = wrapper.emitted('selection-change')
    expect(emittedEvents).toBeTruthy()
    expect(emittedEvents?.[0]).toEqual([mockRow, true, expect.any(Event)])
  })

  it('should be disabled when row is disabled', async () => {
    wrapper = mount(TableCell, {
      props: {
        column: {
          key: 'selection',
          title: '',
          type: 'selection'
        },
        row: mockRow,
        value: null,
        index: 0,
        disabled: true,
        selectionConfig: {
          enabled: true,
          mode: 'multiple'
        }
      }
    })

    await nextTick()

    const checkbox = wrapper.find('input[type="checkbox"]')
    expect((checkbox.element as HTMLInputElement).disabled).toBe(true)
  })

  it('should be disabled when getCheckboxProps returns disabled', async () => {
    const disabledRow = { ...mockRow, status: 'inactive' }

    wrapper = mount(TableCell, {
      props: {
        column: {
          key: 'selection',
          title: '',
          type: 'selection'
        },
        row: disabledRow,
        value: null,
        index: 0,
        selectionConfig: {
          enabled: true,
          mode: 'multiple',
          getCheckboxProps: (row: TableRow) => ({
            disabled: row.status === 'inactive'
          })
        }
      }
    })

    await nextTick()

    const checkbox = wrapper.find('input[type="checkbox"]')
    expect((checkbox.element as HTMLInputElement).disabled).toBe(true)
  })
})