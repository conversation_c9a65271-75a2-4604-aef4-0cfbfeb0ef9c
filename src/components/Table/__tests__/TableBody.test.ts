import { describe, it, expect, beforeEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import TableBody from '../TableBody.vue'
import type { TableColumn, TableRow } from '@/types'

describe('TableBody Component', () => {
  let wrapper: VueWrapper<any>
  let mockColumns: TableColumn[]
  let mockData: TableRow[]

  beforeEach(() => {
    mockColumns = [
      { key: 'id', title: 'ID', width: 80 },
      { key: 'name', title: '姓名', width: 120 },
      { key: 'age', title: '年龄', width: 80, align: 'center' },
      { key: 'email', title: '邮箱', width: 200 }
    ]

    mockData = [
      { id: 1, name: '张三', age: 25, email: '<EMAIL>' },
      { id: 2, name: '李四', age: 30, email: '<EMAIL>' },
      { id: 3, name: '王五', age: 28, email: '<EMAIL>' }
    ]
  })

  describe('Basic Rendering', () => {
    it('should render table body with data rows', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns
        }
      })

      const body = wrapper.find('.table-body')
      expect(body.exists()).toBe(true)

      const rows = wrapper.findAll('.table-row')
      expect(rows).toHaveLength(3)
    })

    it('should apply body classes correctly', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          stripe: { enabled: true, type: "default" },
          hoverMode: 'row'
        }
      })

      const body = wrapper.find('.table-body')
      expect(body.classes()).toContain('body-hover-row')
    })
  })

  describe('Empty State', () => {
    it('should show empty state when no data provided', () => {
      wrapper = mount(TableBody, {
        props: {
          data: [],
          columns: mockColumns
        }
      })

      const emptyState = wrapper.find('.table-empty')
      expect(emptyState.exists()).toBe(true)

      const emptyText = wrapper.find('.empty-text')
      expect(emptyText.text()).toBe('暂无数据')
    })

    it('should show custom empty text', () => {
      wrapper = mount(TableBody, {
        props: {
          data: [],
          columns: mockColumns,
          emptyText: '没有找到数据'
        }
      })

      const emptyText = wrapper.find('.empty-text')
      expect(emptyText.text()).toBe('没有找到数据')
    })

    it('should show empty description when provided', () => {
      wrapper = mount(TableBody, {
        props: {
          data: [],
          columns: mockColumns,
          emptyDescription: '请尝试调整筛选条件'
        }
      })

      const emptyDescription = wrapper.find('.empty-description')
      expect(emptyDescription.exists()).toBe(true)
      expect(emptyDescription.text()).toBe('请尝试调整筛选条件')
    })

    it('should render custom empty slot content', () => {
      wrapper = mount(TableBody, {
        props: {
          data: [],
          columns: mockColumns
        },
        slots: {
          empty: '<div class="custom-empty">自定义空状态</div>'
        }
      })

      const customEmpty = wrapper.find('.custom-empty')
      expect(customEmpty.exists()).toBe(true)
      expect(customEmpty.text()).toBe('自定义空状态')
    })
  })

  describe('Loading State', () => {
    it('should show loading state when loading is true', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          loading: true
        }
      })

      const loadingState = wrapper.find('.table-loading')
      expect(loadingState.exists()).toBe(true)

      const loadingText = wrapper.find('.loading-text')
      expect(loadingText.text()).toBe('加载中...')

      const loadingSpinner = wrapper.find('.loading-spinner')
      expect(loadingSpinner.exists()).toBe(true)
    })

    it('should show custom loading text', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          loading: true,
          loadingText: '数据加载中，请稍候...'
        }
      })

      const loadingText = wrapper.find('.loading-text')
      expect(loadingText.text()).toBe('数据加载中，请稍候...')
    })

    it('should render custom loading slot content', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          loading: true
        },
        slots: {
          loading: '<div class="custom-loading">自定义加载状态</div>'
        }
      })

      const customLoading = wrapper.find('.custom-loading')
      expect(customLoading.exists()).toBe(true)
      expect(customLoading.text()).toBe('自定义加载状态')
    })

    it('should apply loading body class when loading', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          loading: true
        }
      })

      const body = wrapper.find('.table-body')
      expect(body.classes()).toContain('body-loading')
    })
  })

  describe('Row Events', () => {
    it('should emit row-click event when row is clicked', async () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns
        }
      })

      const firstRow = wrapper.findAll('.table-row')[0]
      await firstRow.trigger('click')

      expect(wrapper.emitted('rowClick')).toBeTruthy()
      expect(wrapper.emitted('rowClick')?.[0]?.[0]).toEqual(mockData[0])
      expect(wrapper.emitted('rowClick')?.[0]?.[1]).toBe(0)
    })

    it('should emit row-dblclick event when row is double clicked', async () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns
        }
      })

      const firstRow = wrapper.findAll('.table-row')[0]
      await firstRow.trigger('dblclick')

      expect(wrapper.emitted('rowDblclick')).toBeTruthy()
      expect(wrapper.emitted('rowDblclick')?.[0]?.[0]).toEqual(mockData[0])
      expect(wrapper.emitted('rowDblclick')?.[0]?.[1]).toBe(0)
    })

    it('should emit row-mouseenter event when mouse enters row', async () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns
        }
      })

      const firstRow = wrapper.findAll('.table-row')[0]
      await firstRow.trigger('mouseenter')

      expect(wrapper.emitted('rowMouseenter')).toBeTruthy()
      expect(wrapper.emitted('rowMouseenter')?.[0]?.[0]).toEqual(mockData[0])
      expect(wrapper.emitted('rowMouseenter')?.[0]?.[1]).toBe(0)
    })

    it('should emit row-mouseleave event when mouse leaves row', async () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns
        }
      })

      const firstRow = wrapper.findAll('.table-row')[0]
      await firstRow.trigger('mouseleave')

      expect(wrapper.emitted('rowMouseleave')).toBeTruthy()
      expect(wrapper.emitted('rowMouseleave')?.[0]?.[0]).toEqual(mockData[0])
      expect(wrapper.emitted('rowMouseleave')?.[0]?.[1]).toBe(0)
    })

    it('should emit row-contextmenu event when row is right clicked', async () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns
        }
      })

      const firstRow = wrapper.findAll('.table-row')[0]
      await firstRow.trigger('contextmenu')

      expect(wrapper.emitted('rowContextmenu')).toBeTruthy()
      expect(wrapper.emitted('rowContextmenu')?.[0]?.[0]).toEqual(mockData[0])
      expect(wrapper.emitted('rowContextmenu')?.[0]?.[1]).toBe(0)
    })
  })

  describe('Row States', () => {
    it('should handle selected rows correctly', () => {
      const selectedRows = new Set([1, 3])

      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          selectedRows
        }
      })

      const rows = wrapper.findAll('.table-row')
      expect(rows[0].classes()).toContain('row-selected')
      expect(rows[1].classes()).not.toContain('row-selected')
      expect(rows[2].classes()).toContain('row-selected')
    })

    it('should handle disabled rows correctly', () => {
      const disabledRows = new Set([2])

      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          disabledRows
        }
      })

      const rows = wrapper.findAll('.table-row')
      expect(rows[0].classes()).not.toContain('row-disabled')
      expect(rows[1].classes()).toContain('row-disabled')
      expect(rows[2].classes()).not.toContain('row-disabled')
    })

    it('should handle editing rows correctly', () => {
      const editingRows = new Set([1])

      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          editingRows
        }
      })

      const rows = wrapper.findAll('.table-row')
      expect(rows[0].classes()).toContain('row-editing')
      expect(rows[1].classes()).not.toContain('row-editing')
      expect(rows[2].classes()).not.toContain('row-editing')
    })

    it('should handle stripe rows correctly', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          stripeConfig: { enabled: true, type: "default" },
          getRowStripeClasses: (index: number) => {
            // Simulate stripe logic - odd rows get stripe class
            return index % 2 === 1 ? ['row-stripe'] : []
          }
        }
      })

      const rows = wrapper.findAll('.table-row')
      expect(rows[0].classes()).not.toContain('row-stripe')
      expect(rows[1].classes()).toContain('row-stripe')
      expect(rows[2].classes()).not.toContain('row-stripe')
    })
  })

  describe('Row Height', () => {
    it('should apply custom row height', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          rowHeight: 60
        }
      })

      const firstRow = wrapper.findAll('.table-row')[0]
      expect(firstRow.attributes('style')).toContain('height: 60px')
    })

    it('should apply custom row height as string', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          rowHeight: '4rem'
        }
      })

      const firstRow = wrapper.findAll('.table-row')[0]
      expect(firstRow.attributes('style')).toContain('height: 4rem')
    })
  })

  describe('Max Height', () => {
    it('should apply max height and overflow styles', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns,
          maxHeight: 300
        }
      })

      const rowsContainer = wrapper.find('.table-rows')
      expect(rowsContainer.attributes('style')).toContain('max-height: 300px')
      expect(rowsContainer.attributes('style')).toContain('overflow-y: auto')
    })
  })

  describe('Row Key Generation', () => {
    it('should use _id as row key when available', () => {
      const dataWithIds = [
        { _id: 'row1', name: '张三' },
        { _id: 'row2', name: '李四' }
      ]

      wrapper = mount(TableBody, {
        props: {
          data: dataWithIds,
          columns: [{ key: 'name', title: '姓名' }]
        }
      })

      const vm = wrapper.vm as any
      expect(vm.getRowKey(dataWithIds[0], 0)).toBe('row1')
      expect(vm.getRowKey(dataWithIds[1], 1)).toBe('row2')
    })

    it('should use id as row key when _id is not available', () => {
      const dataWithIds = [
        { id: 1, name: '张三' },
        { id: 2, name: '李四' }
      ]

      wrapper = mount(TableBody, {
        props: {
          data: dataWithIds,
          columns: [{ key: 'name', title: '姓名' }]
        }
      })

      const vm = wrapper.vm as any
      expect(vm.getRowKey(dataWithIds[0], 0)).toBe(1)
      expect(vm.getRowKey(dataWithIds[1], 1)).toBe(2)
    })

    it('should use index as row key when neither _id nor id is available', () => {
      const dataWithoutIds = [{ name: '张三' }, { name: '李四' }]

      wrapper = mount(TableBody, {
        props: {
          data: dataWithoutIds,
          columns: [{ key: 'name', title: '姓名' }]
        }
      })

      const vm = wrapper.vm as any
      expect(vm.getRowKey(dataWithoutIds[0], 0)).toBe(0)
      expect(vm.getRowKey(dataWithoutIds[1], 1)).toBe(1)
    })
  })

  describe('Slots', () => {
    it('should pass through cell slots to TableRow', () => {
      wrapper = mount(TableBody, {
        props: {
          data: mockData,
          columns: mockColumns
        },
        slots: {
          'cell-name': '<div class="custom-name-cell">Custom Name</div>'
        }
      })

      const customCell = wrapper.find('.custom-name-cell')
      expect(customCell.exists()).toBe(true)
      expect(customCell.text()).toBe('Custom Name')
    })
  })
})
