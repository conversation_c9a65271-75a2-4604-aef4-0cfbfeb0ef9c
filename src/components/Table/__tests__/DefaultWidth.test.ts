import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import Table from '../Table.vue'
import type { TableConfig, TableRow } from '@/types'

describe('Table Default Width Configuration', () => {
  const data: TableRow[] = [{ id: 1, name: '<PERSON>', age: 25 }]

  it('应该为未设置宽度的普通列自动设置默认宽度', () => {
    const config: TableConfig = {
      columns: [
        { key: 'name', title: 'Name' }, // 4字符 = 约124px
        { key: 'age', title: 'Age' }, // 3字符 = 约108px
        { key: 'department', title: 'Department' } // 10字符 = 约220px（但会被限制在200px）
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    // After column reordering, non-fixed columns maintain their calculated widths
    expect(processedColumns[0].width).toBe(124) // 'Name': 4 * 16 + 60 = 124
    expect(processedColumns[1].width).toBe(108) // 'Age': 3 * 16 + 60 = 108
    expect(processedColumns[2].width).toBe(200) // 'Department': 限制为最大200px
  })

  it('应该为序号列设置默认60px宽度', () => {
    const config: TableConfig = {
      columns: [
        { key: 'seq', title: 'Seq', type: 'seq' },
        { key: 'name', title: 'Name' }
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    expect(processedColumns[0].width).toBe(60) // 序号列默认宽度
    expect(processedColumns[0].minWidth).toBe(60) // 序号列最小宽度
    expect(processedColumns[1].width).toBe(124) // 普通列自动计算宽度
  })

  it('应该保持用户明确设置的宽度', () => {
    const config: TableConfig = {
      columns: [
        { key: 'seq', title: 'Seq', type: 'seq', width: 80 }, // 明确设置为80px
        { key: 'name', title: 'Name', width: 150 } // 明确设置为150px
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    expect(processedColumns[0].width).toBe(80) // 用户设置的宽度被保留
    expect(processedColumns[1].width).toBe(150) // 用户设置的宽度被保留
  })

  it('应该为所有列设置默认最小宽度', () => {
    const config: TableConfig = {
      columns: [
        { key: 'seq', title: 'Seq', type: 'seq' },
        { key: 'name', title: 'Name' },
        { key: 'a', title: 'A' } // 单字符标题
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    expect(processedColumns[0].minWidth).toBe(60) // 序号列最小宽度
    expect(processedColumns[1].minWidth).toBe(100) // 普通列默认最小宽度
    expect(processedColumns[2].minWidth).toBe(100) // 普通列默认最小宽度
    expect(processedColumns[2].width).toBe(100) // 单字符标题使用最小宽度
  })

  it('应该根据标题长度智能计算宽度', () => {
    const config: TableConfig = {
      columns: [
        { key: 'short', title: 'A' }, // 1字符 = 76px，使用最小宽度100px
        { key: 'medium', title: 'Medium' }, // 6字符 = 156px
        { key: 'long', title: 'Very Long Column Title' } // 21字符 = 396px，限制为最大200px
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    expect(processedColumns[0].width).toBe(100) // 使用最小宽度
    expect(processedColumns[1].width).toBe(156) // 计算得出的宽度
    expect(processedColumns[2].width).toBe(200) // 限制在最大宽度
  })
})
