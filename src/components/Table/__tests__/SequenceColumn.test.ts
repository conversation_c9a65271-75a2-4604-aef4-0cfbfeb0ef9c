import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import Table from '../Table.vue'
import type { TableConfig, TableColumn, TableRow } from '@/types'

describe('Table Sequence Column', () => {
  let config: TableConfig
  let data: TableRow[]

  beforeEach(() => {
    data = [
      { id: 1, name: '<PERSON>', age: 25 },
      { id: 2, name: '<PERSON>', age: 30 },
      { id: 3, name: '<PERSON>', age: 35 }
    ]

    config = {
      columns: [
        {
          key: 'seq',
          title: 'Seq',
          type: 'seq',
          width: 60
        },
        { key: 'name', title: 'Name' },
        { key: 'age', title: 'Age' }
      ],
      data,
      loading: false
    }
  })

  it('应该渲染序号列', async () => {
    const wrapper = mount(Table, {
      props: { config }
    })

    await wrapper.vm.$nextTick()

    // 检查序号列是否存在
    const seqCells = wrapper.findAll('[data-testid="header-cell-seq"]')
    expect(seqCells).toHaveLength(1)
    expect(seqCells[0].text()).toBe('Seq')
  })

  it('应该按顺序显示序号', async () => {
    const wrapper = mount(Table, {
      props: { config }
    })

    await wrapper.vm.$nextTick()

    // 检查序号列的内容
    const seqCells = wrapper.findAll('.table-cell.cell-seq')
    expect(seqCells).toHaveLength(3)
    expect(seqCells[0].text()).toBe('1')
    expect(seqCells[1].text()).toBe('2')
    expect(seqCells[2].text()).toBe('3')
  })

  it('应该支持自定义起始序号', async () => {
    const configWithStartIndex = {
      ...config,
      columns: [
        {
          key: 'seq',
          title: 'Seq',
          type: 'seq' as const,
          width: 60,
          seqConfig: {
            startIndex: 10
          }
        },
        ...config.columns.slice(1)
      ]
    }

    const wrapper = mount(Table, {
      props: { config: configWithStartIndex }
    })

    await wrapper.vm.$nextTick()

    const seqCells = wrapper.findAll('.table-cell.cell-seq')
    expect(seqCells[0].text()).toBe('10')
    expect(seqCells[1].text()).toBe('11')
    expect(seqCells[2].text()).toBe('12')
  })

  it('应该支持自定义格式化函数', async () => {
    const configWithFormatter = {
      ...config,
      columns: [
        {
          key: 'seq',
          title: 'Seq',
          type: 'seq' as const,
          width: 60,
          seqConfig: {
            format: (index: number) => `No.${index}`
          }
        },
        ...config.columns.slice(1)
      ]
    }

    const wrapper = mount(Table, {
      props: { config: configWithFormatter }
    })

    await wrapper.vm.$nextTick()

    const seqCells = wrapper.findAll('.table-cell.cell-seq')
    expect(seqCells[0].text()).toBe('No.1')
    expect(seqCells[1].text()).toBe('No.2')
    expect(seqCells[2].text()).toBe('No.3')
  })

  it('序号列应该居中对齐并且不可编辑', async () => {
    const wrapper = mount(Table, {
      props: { config }
    })

    await wrapper.vm.$nextTick()

    const seqCells = wrapper.findAll('.table-cell.cell-seq')
    expect(seqCells).toHaveLength(3)

    // 检查居中对齐
    seqCells.forEach(cell => {
      expect(cell.classes()).toContain('cell-seq')
      expect(cell.classes()).not.toContain('cell-editable')
    })
  })

  it('应该支持分页时的序号计算', async () => {
    const paginatedConfig = {
      ...config,
      pagination: {
        enabled: true,
        pageSize: 2,
        currentPage: 2,
        total: data.length
      }
    }

    const wrapper = mount(Table, {
      props: { config: paginatedConfig }
    })

    await wrapper.vm.$nextTick()

    // 在第二页，第一行应该显示序号 3（pageSize * (currentPage - 1) + 1 = 2 * (2-1) + 1 = 3）
    const seqCells = wrapper.findAll('.table-cell.cell-seq')
    if (seqCells.length > 0) {
      // 由于分页可能会改变显示的数据，我们检查序号是否正确计算
      const firstSeqNumber = parseInt(seqCells[0].text())
      expect(firstSeqNumber).toBeGreaterThan(1) // 第二页的序号应该大于1
    }
  })

  it('应该使用默认的序号列配置', async () => {
    const simpleSeqColumn: TableColumn = {
      key: 'seq',
      title: 'Seq',
      type: 'seq'
    }

    const simpleConfig: TableConfig = {
      columns: [simpleSeqColumn, { key: 'name', title: 'Name' }],
      data,
      loading: false
    }

    const wrapper = mount(Table, {
      props: { config: simpleConfig }
    })

    await wrapper.vm.$nextTick()

    const headerCell = wrapper.find('[data-testid="header-cell-seq"]')
    expect(headerCell.exists()).toBe(true)

    // 检查默认宽度和对齐方式
    const processedColumns = wrapper.vm.processedColumns()
    const seqColumn = processedColumns.find((col: any) => col.key === 'seq')

    expect(seqColumn?.width).toBe(60) // 默认宽度
    expect(seqColumn?.align).toBe('center') // 默认居中
    expect(seqColumn?.editable).toBe(false) // 不可编辑
    expect(seqColumn?.sortable).toBe(false) // 不可排序
    expect(seqColumn?.filterable).toBe(false) // 不可过滤
    expect(seqColumn?.fixed).toBe('left') // 默认固定在左侧
  })
})
