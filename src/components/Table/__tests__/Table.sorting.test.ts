// Integration tests for Table component sorting functionality
import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import Table from '../Table.vue'
import type { TableConfig } from '@/types'

describe('Table Component - Sorting Integration', () => {
  let mockConfig: TableConfig

  beforeEach(() => {
    mockConfig = {
      columns: [
        { key: 'id', title: 'ID', width: 80, sortable: true },
        { key: 'name', title: '姓名', width: 120, sortable: true },
        { key: 'age', title: '年龄', width: 80, align: 'center', sortable: true },
        { key: 'email', title: '邮箱', width: 200, sortable: false }
      ],
      data: [
        { id: 3, name: '王五', age: 28, email: '<EMAIL>' },
        { id: 1, name: '张三', age: 25, email: '<EMAIL>' },
        { id: 2, name: '李四', age: 30, email: '<EMAIL>' }
      ],
      border: true,
      stripe: { enabled: true, type: "default" },
      hover: 'row',
      size: 'medium'
    }
  })

  describe('Basic Sorting', () => {
    it('should sort data when header is clicked', async () => {
      const wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      await nextTick()

      // Find the name column header and click it
      const nameHeader = wrapper.find('[data-testid="header-cell-name"]')
      if (nameHeader.exists()) {
        await nameHeader.trigger('click')
        await nextTick()

        // Check if sortChange event was emitted
        const sortChangeEvents = wrapper.emitted('sortChange')
        expect(sortChangeEvents).toBeTruthy()

        if (sortChangeEvents) {
          const lastEvent = sortChangeEvents[sortChangeEvents.length - 1]
          expect(lastEvent[0]).toEqual([{ column: 'name', direction: 'asc', priority: 0 }])
        }
      }
    })

    it('should cycle through sort directions on multiple clicks', async () => {
      const wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      await nextTick()

      const nameHeader = wrapper.find('[data-testid="header-cell-name"]')
      if (nameHeader.exists()) {
        // First click - ascending
        await nameHeader.trigger('click')
        await nextTick()

        let sortChangeEvents = wrapper.emitted('sortChange')
        if (sortChangeEvents) {
          expect(sortChangeEvents[sortChangeEvents.length - 1][0]).toEqual([
            { column: 'name', direction: 'asc', priority: 0 }
          ])
        }

        // Second click - descending
        await nameHeader.trigger('click')
        await nextTick()

        sortChangeEvents = wrapper.emitted('sortChange')
        if (sortChangeEvents) {
          expect(sortChangeEvents[sortChangeEvents.length - 1][0]).toEqual([
            { column: 'name', direction: 'desc', priority: 0 }
          ])
        }

        // Third click - clear sort
        await nameHeader.trigger('click')
        await nextTick()

        sortChangeEvents = wrapper.emitted('sortChange')
        if (sortChangeEvents) {
          expect(sortChangeEvents[sortChangeEvents.length - 1][0]).toEqual([])
        }
      }
    })

    it('should not sort non-sortable columns', async () => {
      const wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      await nextTick()

      const emailHeader = wrapper.find('[data-testid="header-cell-email"]')
      if (emailHeader.exists()) {
        await emailHeader.trigger('click')
        await nextTick()

        // Should not emit sortChange event for non-sortable column
        const sortChangeEvents = wrapper.emitted('sortChange')
        expect(sortChangeEvents).toBeFalsy()
      }
    })
  })

  describe('Multi-column Sorting', () => {
    it('should support multi-column sorting with Ctrl+click', async () => {
      const configWithMultiSort = {
        ...mockConfig,
        multiSort: true
      }

      const wrapper = mount(Table, {
        props: { config: configWithMultiSort }
      })

      await nextTick()

      const nameHeader = wrapper.find('[data-testid="header-cell-name"]')
      const ageHeader = wrapper.find('[data-testid="header-cell-age"]')

      if (nameHeader.exists() && ageHeader.exists()) {
        // First column sort
        await nameHeader.trigger('click')
        await nextTick()

        // Second column sort with Ctrl key
        await ageHeader.trigger('click', { ctrlKey: true })
        await nextTick()

        const sortChangeEvents = wrapper.emitted('sortChange')
        if (sortChangeEvents) {
          const lastEvent = sortChangeEvents[sortChangeEvents.length - 1][0] as any[]
          expect(lastEvent).toHaveLength(2)
          expect(lastEvent[0].column).toBe('name')
          expect(lastEvent[1].column).toBe('age')
        }
      }
    })
  })

  describe('Custom Sort Functions', () => {
    it('should use custom sort functions when provided', async () => {
      const configWithCustomSort = {
        ...mockConfig,
        customSortFunctions: {
          name: (a: any, b: any) => {
            // Sort by string length instead of alphabetically
            return (a || '').length - (b || '').length
          }
        }
      }

      const wrapper = mount(Table, {
        props: { config: configWithCustomSort }
      })

      await nextTick()

      const nameHeader = wrapper.find('[data-testid="header-cell-name"]')
      if (nameHeader.exists()) {
        await nameHeader.trigger('click')
        await nextTick()

        // Should emit sortChange event with custom sorting
        const sortChangeEvents = wrapper.emitted('sortChange')
        expect(sortChangeEvents).toBeTruthy()
      }
    })
  })

  describe('Default Sort Configuration', () => {
    it('should apply default sort configuration on initialization', async () => {
      const configWithDefaultSort = {
        ...mockConfig,
        defaultSort: [{ column: 'age', direction: 'desc' as const, priority: 0 }]
      }

      const wrapper = mount(Table, {
        props: { config: configWithDefaultSort }
      })

      await nextTick()

      // Should emit sortChange event with default sort
      const sortChangeEvents = wrapper.emitted('sortChange')
      if (sortChangeEvents) {
        expect(sortChangeEvents[0][0]).toEqual([{ column: 'age', direction: 'desc', priority: 0 }])
      }
    })
  })

  describe('Sort Indicators', () => {
    it('should show sort indicators in table headers', async () => {
      const wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      await nextTick()

      // Check if sortable columns have sort indicators
      const sortableHeaders = wrapper.findAll('.cell-sortable')
      expect(sortableHeaders.length).toBeGreaterThan(0)

      // Check if sort indicators exist
      const sortIndicators = wrapper.findAll('.sort-indicator')
      expect(sortIndicators.length).toBeGreaterThan(0)
    })

    it('should show active sort indicator when column is sorted', async () => {
      const wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      await nextTick()

      const nameHeader = wrapper.find('[data-testid="header-cell-name"]')
      if (nameHeader.exists()) {
        await nameHeader.trigger('click')
        await nextTick()

        // Check if active sort indicator is shown
        const activeSortIndicator = wrapper.find('.indicator-active')
        expect(activeSortIndicator.exists()).toBe(true)
      }
    })
  })

  describe('Exposed Methods', () => {
    it('should expose sorting methods', async () => {
      const wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      await nextTick()

      const tableInstance = wrapper.vm as any

      // Check if sorting methods are exposed
      expect(typeof tableInstance.setSortConfigs).toBe('function')
      expect(typeof tableInstance.clearSort).toBe('function')
      expect(typeof tableInstance.getSortState).toBe('function')
    })

    it('should allow programmatic sort configuration', async () => {
      const wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      await nextTick()

      const tableInstance = wrapper.vm as any

      // Set sort configuration programmatically
      tableInstance.setSortConfigs([{ column: 'name', direction: 'asc', priority: 0 }])

      await nextTick()

      // Should emit sortChange event
      const sortChangeEvents = wrapper.emitted('sortChange')
      expect(sortChangeEvents).toBeTruthy()
    })
  })
})
