// Integration tests for Table component filtering functionality
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import Table from '../Table.vue'
import type { TableConfig } from '@/types'

// Test data
const testConfig: TableConfig = {
  columns: [
    { key: 'name', title: 'Name', filterable: true },
    { key: 'age', title: 'Age', filterable: true },
    { key: 'email', title: 'Email', filterable: true },
    { key: 'status', title: 'Status', filterable: true }
  ],
  data: [
    {
      _id: 1,
      name: '<PERSON>',
      age: 30,
      email: '<EMAIL>',
      status: 'active'
    },
    {
      _id: 2,
      name: '<PERSON>',
      age: 25,
      email: '<EMAIL>',
      status: 'inactive'
    },
    {
      _id: 3,
      name: '<PERSON>',
      age: 35,
      email: '<EMAIL>',
      status: 'active'
    }
  ],
  enableHighlight: true,
  caseSensitive: false,
  searchDebounce: 0 // Disable debounce for testing
}

describe('Table Filtering Integration', () => {
  it('should integrate filtering functionality with table component', async () => {
    const wrapper = mount(Table, {
      props: { config: testConfig }
    })

    await nextTick()

    // Get the component instance
    const tableInstance = wrapper.vm as any

    // Test search functionality - need to wait for debounce
    tableInstance.setSearchText('john')
    await new Promise(resolve => setTimeout(resolve, 50)) // Wait for debounce

    expect(tableInstance.isSearching()).toBe(true)
    expect(tableInstance.getFilteredData()).toHaveLength(2) // John Doe and Bob Johnson

    // Test filter conditions
    const filterCondition = tableInstance.createSimpleFilter('status', 'equals', 'active')
    tableInstance.addFilterCondition(filterCondition)
    await nextTick()

    expect(tableInstance.isFiltered()).toBe(true)
    // John and Bob are active and contain 'john'
    expect(tableInstance.getFilteredData()).toHaveLength(2)

    // Test clearing filters
    tableInstance.clearAllFilters()
    await nextTick()

    expect(tableInstance.isFiltered()).toBe(false)
    expect(tableInstance.getFilteredData()).toHaveLength(2) // Still filtered by search

    // Test clearing search
    tableInstance.clearSearch()
    await nextTick()

    expect(tableInstance.isSearching()).toBe(false)
    expect(tableInstance.getFilteredData()).toHaveLength(3) // All data
  })

  it('should emit filter change events', async () => {
    const wrapper = mount(Table, {
      props: { config: testConfig }
    })

    await nextTick()

    const tableInstance = wrapper.vm as any

    // Add a filter condition
    const filterCondition = tableInstance.createSimpleFilter('status', 'equals', 'active')
    tableInstance.addFilterCondition(filterCondition)
    await nextTick()

    // Check if filterChange event was emitted
    expect(wrapper.emitted('filterChange')).toBeTruthy()
    expect(wrapper.emitted('filterChange')?.[0]?.[0]).toEqual(
      expect.objectContaining({
        conditions: [filterCondition]
      })
    )
  })

  it('should handle column value extraction', async () => {
    const wrapper = mount(Table, {
      props: { config: testConfig }
    })

    await nextTick()

    const tableInstance = wrapper.vm as any

    // Get unique values for status column
    const statusValues = tableInstance.getColumnValues('status')
    expect(statusValues).toEqual(['active', 'inactive'])

    // Get unique values for age column
    const ageValues = tableInstance.getColumnValues('age')
    expect(ageValues).toEqual([25, 30, 35])
  })

  it('should handle filter condition creation', async () => {
    const wrapper = mount(Table, {
      props: { config: testConfig }
    })

    await nextTick()

    const tableInstance = wrapper.vm as any

    // Test filter condition creation
    const condition = tableInstance.createSimpleFilter('status', 'equals', 'active')
    expect(condition).toEqual({
      column: 'status',
      operator: 'equals',
      value: 'active',
      caseSensitive: false
    })

    // Test adding the condition
    tableInstance.addFilterCondition(condition)
    await nextTick()

    expect(tableInstance.isFiltered()).toBe(true)
    expect(tableInstance.getFilteredData()).toHaveLength(2) // John and Bob are active
  })

  it('should handle text highlighting', async () => {
    const wrapper = mount(Table, {
      props: { config: testConfig }
    })

    await nextTick()

    const tableInstance = wrapper.vm as any

    // Set search text
    tableInstance.setSearchText('doe')
    await new Promise(resolve => setTimeout(resolve, 50)) // Wait for debounce

    // Test highlighting (basic functionality test)
    const highlighted = tableInstance.highlightText('John Doe', 'name', '1')
    expect(typeof highlighted).toBe('string')
  })

  it('should handle complex filtering scenarios', async () => {
    const wrapper = mount(Table, {
      props: { config: testConfig }
    })

    await nextTick()

    const tableInstance = wrapper.vm as any

    // Add multiple filter conditions
    tableInstance.addFilterCondition(tableInstance.createSimpleFilter('status', 'equals', 'active'))
    tableInstance.addFilterCondition(tableInstance.createSimpleFilter('age', 'greaterThan', 28))

    await nextTick()

    // Should find John (30, active) and Bob (35, active)
    expect(tableInstance.getFilteredData()).toHaveLength(2)

    // Add search on top of filters
    tableInstance.setSearchText('john')
    await new Promise(resolve => setTimeout(resolve, 50)) // Wait for debounce

    // Should find John and Bob (both active, age > 28, and contain 'john')
    expect(tableInstance.getFilteredData()).toHaveLength(2)
  })
})
