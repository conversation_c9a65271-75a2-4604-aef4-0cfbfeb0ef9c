import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import Table from '../Table.vue'
import type { TableConfig, TableRow } from '@/types'

describe('Table Width MinWidth Validation', () => {
  const data: TableRow[] = [{ id: 1, name: '<PERSON>', age: 25 }]

  // Mock console.warn to test warning messages
  const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {})

  beforeEach(() => {
    mockConsoleWarn.mockClear()
  })

  afterAll(() => {
    mockConsoleWarn.mockRestore()
  })

  it('应该自动调整 width 小于 minWidth 的配置', () => {
    const config: TableConfig = {
      columns: [
        { key: 'name', title: 'Name', width: 80, minWidth: 100 }, // width < minWidth
        { key: 'age', title: 'Age', width: 50, minWidth: 120 }, // width < minWidth
        { key: 'id', title: 'ID', width: 150, minWidth: 100 } // width >= minWidth, 正常
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    // 验证 width 保持用户设置，minWidth 被调整
    expect(processedColumns[0].width).toBe(80) // 保持用户设置的 width
    expect(processedColumns[1].width).toBe(50) // 保持用户设置的 width
    expect(processedColumns[2].width).toBe(150) // 保持原值

    // 验证警告消息
    expect(mockConsoleWarn).toHaveBeenCalledTimes(2)
    expect(mockConsoleWarn).toHaveBeenCalledWith(
      '列 "name": width (80px) 小于 minWidth (100px), 已将 minWidth 调整为 80px'
    )
    expect(mockConsoleWarn).toHaveBeenCalledWith(
      '列 "age": width (50px) 小于 minWidth (120px), 已将 minWidth 调整为 50px'
    )
  })

  it('应该处理字符串类型的 width', () => {
    const config: TableConfig = {
      columns: [
        { key: 'name', title: 'Name', width: '80px', minWidth: 100 }, // 字符串类型 width < minWidth
        { key: 'age', title: 'Age', width: '150px', minWidth: 100 } // 字符串类型 width >= minWidth
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    // 验证字符串类型的 width 也能正确验证和调整
    expect(processedColumns[0].width).toBe('80px') // 保持用户设置的字符串 width
    expect(processedColumns[1].width).toBe('150px') // 保持原值

    // 验证警告消息
    expect(mockConsoleWarn).toHaveBeenCalledTimes(1)
    expect(mockConsoleWarn).toHaveBeenCalledWith(
      '列 "name": width (80px) 小于 minWidth (100px), 已将 minWidth 调整为 80px'
    )
  })

  it('应该正确处理序号列的 width 和 minWidth', () => {
    const config: TableConfig = {
      columns: [
        { key: 'seq', title: 'Seq', type: 'seq', width: 40 }, // width < 序号列默认 minWidth(60)
        { key: 'name', title: 'Name' }
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    // 验证序号列的 width 保持用户设置，minWidth 被调整
    expect(processedColumns[0].width).toBe(40) // 保持用户设置的 width
    expect(processedColumns[0].minWidth).toBe(40) // minWidth 被调整为 width

    // 验证警告消息
    expect(mockConsoleWarn).toHaveBeenCalledTimes(1)
    expect(mockConsoleWarn).toHaveBeenCalledWith(
      '列 "seq": width (40px) 小于 minWidth (60px), 已将 minWidth 调整为 40px'
    )
  })

  it('应该保持有效的 width >= minWidth 配置不变', () => {
    const config: TableConfig = {
      columns: [
        { key: 'name', title: 'Name', width: 150, minWidth: 100 }, // 有效配置
        { key: 'age', title: 'Age', width: 200, minWidth: 100 }, // 有效配置
        { key: 'id', title: 'ID', width: 120 } // 使用默认 minWidth: 100
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    // 验证有效配置保持不变
    expect(processedColumns[0].width).toBe(150)
    expect(processedColumns[1].width).toBe(200)
    expect(processedColumns[2].width).toBe(120)

    // 验证没有警告消息
    expect(mockConsoleWarn).not.toHaveBeenCalled()
  })

  it('应该正确处理自动计算宽度与 minWidth 的关系', () => {
    const config: TableConfig = {
      columns: [
        { key: 'name', title: 'Name' }, // 没有设置 width，会自动计算: "Name" = 4 * 16 + 60 = 124，大于默认 minWidth 100
        { key: 'age', title: 'Age', minWidth: 150 } // 只有 minWidth，自动计算: "Age" = 3 * 16 + 60 = 108，小于 minWidth 150
      ],
      data
    }

    const wrapper = mount(Table, { props: { config } })
    const processedColumns = wrapper.vm.processedColumns()

    // 验证自动计算的宽度
    expect(processedColumns[0].width).toBe(124) // 根据标题长度计算: "Name" = 4 * 16 + 60 = 124
    expect(processedColumns[1].width).toBe(150) // 自动计算为 108，但被调整为 minWidth 150

    // 验证 minWidth
    expect(processedColumns[0].minWidth).toBe(100) // 默认 minWidth
    expect(processedColumns[1].minWidth).toBe(150) // 用户设置的 minWidth

    // 验证第二列的警告消息（自动计算的宽度小于用户设置的 minWidth）
    expect(mockConsoleWarn).toHaveBeenCalledTimes(1)
    expect(mockConsoleWarn).toHaveBeenCalledWith(
      '列 "age": 计算宽度 (108px) 小于 minWidth (150px), 已将 width 调整为 150px'
    )
  })
})