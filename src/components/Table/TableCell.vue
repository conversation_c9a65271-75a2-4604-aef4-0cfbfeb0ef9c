<template>
  <div
    class="table-cell"
    :class="cellClasses"
    :style="cellStyle"
    :data-column-key="column.key"
    @click="handleClick"
    @dblclick="handleDoubleClick"
  >
    <!-- Editing mode -->
    <div
      v-if="editing"
      class="cell-editor"
    >
      <input
        v-if="getEditorType() === 'input'"
        ref="editorRef"
        v-model="editValue"
        class="cell-input"
        :type="getInputType()"
        @blur="handleEditorBlur"
        @keydown="handleEditorKeydown"
      />
      <textarea
        v-else-if="getEditorType() === 'textarea'"
        ref="editorRef"
        v-model="editValue"
        class="cell-textarea"
        @blur="handleEditorBlur"
        @keydown="handleEditorKeydown"
      />
      <select
        v-else-if="getEditorType() === 'select'"
        ref="editorRef"
        v-model="editValue"
        class="cell-select"
        @blur="handleEditorBlur"
        @keydown="handleEditorKeydown"
      >
        <option
          v-for="option in getSelectOptions()"
          :key="String(option.value)"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </option>
      </select>
    </div>

    <!-- Display mode -->
    <div
      v-else
      class="cell-content"
    >
      <slot
        :value="value"
        :row="row"
        :column="column"
        :index="index"
      >
        <!-- 选择列复选框/单选框 -->
        <input
          v-if="column.type === 'selection'"
          :type="selectionInputType"
          :name="selectionInputName"
          :checked="isRowSelected"
          :disabled="isSelectionDisabled"
          class="selection-input"
          @change="handleSelectionChange"
        />
        <!-- Custom render function, template, or default display -->
        <component
          :is="renderComponent"
          v-else-if="column.render"
        />
        <span
          v-else-if="column.template"
          class="cell-text"
          >{{ templateValue }}</span
        >
        <span
          v-else
          class="cell-text"
          >{{ displayValue }}</span
        >
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, watch } from 'vue'
import type {
  TableColumn,
  TableRow as TableRowType,
  SelectionConfig,
  BorderConfig,
  StripeConfig
} from '@/types'
import { renderTemplateSafe } from '@/utils/templateSanitizer'

// Props definition
interface Props {
  column: TableColumn
  row: TableRowType
  value: unknown
  index: number
  selected?: boolean
  disabled?: boolean
  editing?: boolean
  hovered?: boolean
  paginationState?: { currentPage: number; pageSize: number; total: number } // 分页信息用于序号计算
  selectionConfig?: SelectionConfig // 选择列配置
  selectedRows?: Set<string | number> // 选中行的 Set，用于响应式更新
  borderConfig?: BorderConfig
  cellBorderClasses?: string[]
  getFixedColumnBorder?: (column: TableColumn) => {
    classes: string[]
    styles: Record<string, string>
  }
  getFixedCellStripe?: (
    column: TableColumn,
    rowIndex: number,
    selected?: boolean,
    hover?: boolean,
    editing?: boolean
  ) => { classes: string[]; styles: Record<string, string> }
  stripeConfig?: StripeConfig
  getCurrentRowBackground?: (
    index: number,
    selected?: boolean,
    hover?: boolean,
    editing?: boolean
  ) => string
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  disabled: false,
  editing: false,
  hovered: false,
  paginationState: undefined,
  selectionConfig: undefined,
  selectedRows: () => new Set(),
  borderConfig: () => ({ type: 'default' }),
  cellBorderClasses: () => [],
  getFixedColumnBorder: () => (_column: TableColumn) => ({ classes: [], styles: {} }),
  getFixedCellStripe:
    () =>
    (
      _column: TableColumn,
      _rowIndex: number,
      _selected?: boolean,
      _hover?: boolean,
      _editing?: boolean
    ) => ({ classes: [], styles: {} }),
  stripeConfig: () => ({ type: 'default', enabled: false }),
  getCurrentRowBackground:
    () => (_index: number, _selected?: boolean, _hover?: boolean, _editing?: boolean) =>
      'var(--table-bg)'
})

// Emits definition
interface Emits {
  'cell-click': [column: TableColumn, value: unknown, event: MouseEvent]
  'cell-dblclick': [column: TableColumn, value: unknown, event: MouseEvent]
  'cell-edit': [column: TableColumn, value: unknown]
  'cell-change': [column: TableColumn, oldValue: unknown, newValue: unknown]
  'selection-change': [row: TableRowType, selected: boolean, event: Event]
}

const emit = defineEmits<Emits>()

// Template refs
const editorRef = ref<HTMLElement>()

// Internal state
const editValue = ref<string | number>(String(props.value || ''))

// Computed properties - 恢复原有逻辑保证固定列正常工作
const cellClasses = computed(() => {
  const classes = ['cell']

  // 添加统一边框类
  if (props.borderConfig) {
    classes.push(`table-border-cell-${props.borderConfig.type}`)
  }

  if (props.column.type === 'seq') classes.push('cell-seq')
  if (props.column.type === 'selection') classes.push('cell-selection')
  if (props.column.align) classes.push(`cell-align-${props.column.align}`)

  // 固定列基础样式类
  if (props.column.fixed === 'left') classes.push('cell-fixed-left')
  if (props.column.fixed === 'right') classes.push('cell-fixed-right')

  // 使用统一固定列边框管理 - 保持原有逻辑
  if (props.column.fixed && props.getFixedColumnBorder) {
    const fixedBorder = props.getFixedColumnBorder(props.column)
    if (fixedBorder?.classes) {
      classes.push(...fixedBorder.classes)
    }
  }

  // 添加固定列斑马纹CSS类
  if (props.column.fixed && props.getFixedCellStripe) {
    const fixedStripe = props.getFixedCellStripe(
      props.column,
      props.index,
      props.selected,
      props.hovered,
      props.editing
    )
    if (fixedStripe?.classes) {
      classes.push(...fixedStripe.classes)
    }
  }

  if (props.column._isLastLeftFixed) classes.push('cell-last-left-fixed')
  if (props.column._isFirstRightFixed) classes.push('cell-first-right-fixed')
  if (props.selected) classes.push('cell-selected')
  if (props.disabled) classes.push('cell-disabled')
  if (props.editing) classes.push('cell-editing')
  if (props.column.editable && props.column.type !== 'seq' && props.column.type !== 'selection')
    classes.push('cell-editable')

  // 添加交互式类以启用hover效果（来自table-cell-unified.css）
  if (!props.disabled) classes.push('cell-interactive')

  return classes
})

const cellStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.column.width) {
    style['width'] =
      typeof props.column.width === 'number' ? `${props.column.width}px` : props.column.width
    style['minWidth'] = style['width']
    style['maxWidth'] = style['width']
  }

  if (props.column.minWidth) {
    style['minWidth'] = `${props.column.minWidth}px`
  }

  if (props.column.maxWidth) {
    style['maxWidth'] = `${props.column.maxWidth}px`
  }

  if (props.column.align) {
    style['textAlign'] = props.column.align
  }

  // 固定列基础定位样式
  if (props.column.fixed) {
    style['position'] = 'sticky'
    style['zIndex'] = '10'

    if (props.column.fixed === 'left') {
      style['left'] = props.column._fixedLeftPosition
        ? `${props.column._fixedLeftPosition}px`
        : '0px'
    } else if (props.column.fixed === 'right') {
      style['right'] = props.column._fixedRightPosition
        ? `${props.column._fixedRightPosition}px`
        : '0px'
    }
  }

  // 使用统一固定列边框管理 - 保持原有逻辑
  if (props.column.fixed && props.getFixedColumnBorder) {
    const fixedBorder = props.getFixedColumnBorder(props.column)
    Object.assign(style, fixedBorder.styles)
  }

  // 固定列斑马纹背景通过CSS类处理，不使用内联样式
  // 这样可以保证hover效果和CSS优先级系统正常工作

  return style
})

const displayValue = computed(() => {
  // 序号列特殊处理
  if (props.column.type === 'seq') {
    return getSeqNumber()
  }

  // 选择列特殊处理 - 参考 vxetable 的 type="checkbox" 模式
  if (props.column.type === 'selection') {
    return '' // 选择列不显示文本，只显示复选框
  }

  if (props.value === null || props.value === undefined) {
    return ''
  }

  if (typeof props.value === 'boolean') {
    return props.value ? '是' : '否'
  }

  if (typeof props.value === 'object') {
    return JSON.stringify(props.value)
  }

  return String(props.value)
})

// 序号列计算逻辑 - 参考 vxetable 的实现
const getSeqNumber = (): string | number => {
  const config = props.column.seqConfig || {}
  const startIndex = config.startIndex ?? 1

  // 计算基础序号（考虑分页）
  let seqNumber = props.index + startIndex

  // 如果有分页信息，需要计算跨页的序号
  if (props.paginationState) {
    const { currentPage, pageSize } = props.paginationState
    seqNumber = (currentPage - 1) * pageSize + props.index + startIndex
  }

  // 如果有自定义格式化函数，则使用自定义格式化
  if (config.format) {
    return config.format(seqNumber, props.row, props.paginationState)
  }

  return seqNumber
}

// 选择列相关计算属性 - 参考 vxetable 的选择列实现
const getRowKey = (row: TableRowType, index: number): string | number => {
  return row._id ?? row['id'] ?? index
}

const selectionInputType = computed(() => {
  if (!props.selectionConfig) return 'checkbox'
  return props.selectionConfig.mode === 'single' ? 'radio' : 'checkbox'
})

const selectionInputName = computed(() => {
  if (selectionInputType.value === 'radio') {
    return `table-selection-${props.column.key}` // radio 需要相同的 name 属性
  }
  return undefined
})

const isRowSelected = computed(() => {
  // Check if we have selectedRows Set prop
  if (props.selectedRows) {
    const rowKey = getRowKey(props.row, props.index)

    return props.selectedRows.has(rowKey)
  }

  // Fallback to the _selected property on the row
  return props.row._selected ?? false
})

const isSelectionDisabled = computed(() => {
  if (props.disabled) return true
  if (!props.selectionConfig?.getCheckboxProps) return false

  const checkboxProps = props.selectionConfig.getCheckboxProps(props.row)
  return checkboxProps.disabled ?? false
})

const templateValue = computed(() => {
  if (!props.column.template) return ''

  try {
    return renderTemplateSafe(props.column.template, {
      value: props.value,
      row: props.row,
      column: props.column,
      index: props.index
    })
  } catch (error) {
    console.warn('模板渲染失败:', error)
    return String(props.value || '')
  }
})

const renderComponent = computed(() => {
  if (!props.column.render) return null

  // 返回一个函数式组件，直接渲染 VNode
  return () => {
    return props.column.render!({
      value: props.value,
      row: props.row,
      column: props.column,
      index: props.index
    })
  }
})

// Methods
const getEditorType = (): string => {
  // This could be extended to support different editor types based on column configuration
  if (typeof props.value === 'boolean') return 'select'
  if (typeof props.value === 'number') return 'input'
  if (typeof props.value === 'string' && props.value.length > 50) return 'textarea'
  return 'input'
}

const getInputType = (): string => {
  if (typeof props.value === 'number') return 'number'
  if (typeof props.value === 'string') {
    // Simple email detection
    if (props.value.includes('@')) return 'email'
    // Simple URL detection
    if (props.value.startsWith('http')) return 'url'
  }
  return 'text'
}

const getSelectOptions = () => {
  if (typeof props.value === 'boolean') {
    return [
      { label: '是', value: true, disabled: false },
      { label: '否', value: false, disabled: false }
    ]
  }
  return []
}

const handleClick = (event: MouseEvent): void => {
  if (props.disabled) return
  emit('cell-click', props.column, props.value, event)
}

const handleDoubleClick = (event: MouseEvent): void => {
  if (props.disabled) return
  if (props.column.editable) {
    emit('cell-edit', props.column, props.value)
  }
  emit('cell-dblclick', props.column, props.value, event)
}

const handleSelectionChange = (event: Event): void => {
  if (props.disabled || isSelectionDisabled.value) return

  const target = event.target as HTMLInputElement
  const selected = target.checked

  emit('selection-change', props.row, selected, event)
}

const handleEditorBlur = (): void => {
  if (editValue.value !== props.value) {
    emit('cell-change', props.column, props.value, editValue.value)
  }
}

const handleEditorKeydown = (event: KeyboardEvent): void => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    if (editorRef.value) {
      editorRef.value.blur()
    }
  } else if (event.key === 'Escape') {
    event.preventDefault()
    editValue.value = String(props.value || '') // Reset to original value
    if (editorRef.value) {
      editorRef.value.blur()
    }
  }
}

// Watchers
watch(
  () => props.value,
  newValue => {
    editValue.value = String(newValue || '')
  }
)

watch(
  () => props.editing,
  async isEditing => {
    if (isEditing) {
      await nextTick()
      if (editorRef.value) {
        editorRef.value.focus()
        if (editorRef.value && 'select' in editorRef.value) {
          ;(editorRef.value as HTMLInputElement).select()
        }
      }
    }
  }
)
</script>

<style scoped>
@reference "../../styles/base.css";

.table-cell {
  @apply px-4 py-3 text-table-text;
  @apply last:border-r-0 flex-shrink-0 transition-colors;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.cell-align-center {
  @apply text-center justify-center;
}

.cell-align-right {
  @apply text-right justify-end;
}

.cell-selected {
  @apply bg-table-row-selected-bg;
}

.cell-disabled {
  @apply opacity-50 cursor-not-allowed;
}

.cell-editing {
  @apply bg-table-primary/10 ring-1 ring-table-primary/30;
}

.cell-editable {
  @apply cursor-pointer;
}

/* 编辑单元格悬停效果（只在cell模式下生效） */
.table-row.row-cell-hover .cell-editable:hover:not(.cell-disabled) {
  @apply bg-table-row-hover-bg;
}

/* 序号列样式 */
.table-cell.cell-seq {
  @apply flex items-center justify-center text-center;
  min-width: 60px;
  background-color: var(--table-header-bg) !important;
}

.table-cell.cell-seq .cell-content {
  @apply flex items-center justify-center w-full h-full;
}

.table-cell.cell-seq .cell-text {
  @apply text-sm font-mono font-medium text-table-text-secondary;
}

/* 选择列样式 */
.table-cell.cell-selection {
  @apply flex items-center justify-center text-center;
  min-width: 60px;
  background-color: var(--table-header-bg) !important;
}

.table-cell.cell-selection .cell-content {
  @apply flex items-center justify-center w-full h-full;
}

/* 固定列样式 */
.cell-fixed-left,
.cell-fixed-right {
  position: sticky;
  z-index: 10;
  background-color: var(--table-bg); /* 确保固定列有背景色，防止内容透视 */
}

/* 固定列悬停样式 - 继承行级悬停效果 */
.table-row.row-hover:hover .cell-fixed-left,
.table-row.row-hover:hover .cell-fixed-right {
  background-color: var(--table-row-hover-bg) !important;
}

/* 单元格悬停模式的固定列样式 */
.table-row.row-cell-hover .cell-fixed-left:hover,
.table-row.row-cell-hover .cell-fixed-right:hover {
  background-color: var(--table-row-hover-bg) !important;
}

/* 单元格悬停模式的普通单元格样式 */
.table-row.row-cell-hover .table-cell:hover:not(.cell-selection):not(.cell-seq):not(.cell-disabled) {
  background-color: var(--table-row-hover-bg) !important;
}

/* Cell content styles */
.cell-content {
  @apply w-full;
}

.cell-text {
  @apply truncate block;
}

/* Editor styles */
.cell-editor {
  @apply w-full;
}

.cell-input,
.cell-textarea,
.cell-select {
  @apply w-full px-2 py-1 text-sm border border-table-border rounded bg-table-bg text-table-text;
}

.cell-textarea {
  @apply resize-none min-h-[2rem];
}

.cell-select {
  @apply cursor-pointer;
}

/* Selection input styles */
.selection-input {
  @apply w-4 h-4 rounded transition-all duration-200 cursor-pointer;
  @apply checked:bg-table-primary checked:border-table-primary;
  @apply focus:ring-2 focus:ring-table-primary/20 focus:ring-offset-1;
  @apply hover:border-table-primary/60;
  border: 2px solid var(--table-border-color);
  accent-color: var(--table-primary);
}

/* Focus styles */
.table-cell:focus,
.table-cell:focus-visible {
  @apply outline-none ring-2 ring-table-primary ring-offset-1 ring-offset-table-bg;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cell-input,
  .cell-textarea,
  .cell-select {
    @apply text-xs px-1 py-0.5;
  }
}
</style>
