<template>
  <div
    ref="tableContainer"
    class="vue-table-container"
    :data-theme="currentTheme"
    :class="containerClasses"
    :style="containerStyle"
    @keydown="handleKeydown"
  >
    <!-- Error boundary display -->
    <div
      v-if="error"
      class="table-error-boundary"
    >
      <div class="error-content">
        <h3 class="error-title">表格渲染错误</h3>
        <p class="error-message">
          {{ error.message }}
        </p>
        <button
          class="error-retry-btn"
          @click="handleRetry"
        >
          重试
        </button>
      </div>
    </div>

    <!-- Loading state -->
    <div
      v-else-if="isLoading"
      class="table-loading"
    >
      <div class="loading-spinner" />
      <p class="loading-text">加载中...</p>
    </div>

    <!-- Main table content -->
    <div
      v-else
      class="table-content"
    >
      <!-- Toolbar (placeholder for future implementation) -->
      <div
        v-if="toolbarConfig"
        class="table-toolbar-placeholder"
      >
        <div class="toolbar-title">
          {{ getToolbarTitle() }}
        </div>
      </div>

      <!-- Table wrapper -->
      <div
        ref="tableWrapper"
        class="table-wrapper"
        :style="tableWrapperStyle"
      >
        <!-- Table header -->
        <TableHeader
          :columns="isHorizontalVirtualEnabled ? visibleColumns : processedColumns"
          :sort-config="sortConfigs"
          :resizable="true"
          :sticky="props.config.stickyHeader ?? false"
          :selection-config="selectionConfig"
          :data="processedData"
          :selected-rows="tableState.selectedRows"
          :border-config="borderConfig"
          :header-border-classes="headerBorderClasses"
          :get-fixed-column-border="getFixedColumnBorder"
          :drag-drop-config="dragDropConfig"
          @sort="handleSortChange"
          @resize="handleColumnResize"
          @header-click="handleHeaderClick"
          @select-all="handleSelectAll"
          @column-reorder="handleColumnReorder"
          @drag-start="handleDragStart"
          @drag-end="handleDragEnd"
          @drag-cancel="handleDragCancel"
        >
          <!-- Pass through header slots -->
          <template
            v-for="column in processedColumns"
            :key="`header-${column.key}`"
            #[`header-${column.key}`]="slotProps"
          >
            <slot
              :name="`header-${column.key}`"
              v-bind="slotProps"
            />
          </template>
        </TableHeader>

        <!-- Virtual scrolling container -->
        <div
          v-if="isVirtualEnabled"
          class="virtual-scroll-container"
          :style="{ height: `${virtualState.totalHeight}px` }"
        >
          <div
            class="virtual-scroll-area"
            :style="virtualScrollAreaStyle"
          >
            <TableBody
              :data="finalDisplayData"
              :columns="isHorizontalVirtualEnabled ? visibleColumns : processedColumns"
              :loading="false"
              :stripe-config="stripeConfig"
              :get-row-stripe-classes="getRowStripeClasses"
              :get-current-row-background="getCurrentRowBackground"
              :hover-mode="hoverMode"
              :selected-rows="tableState.selectedRows"
              :editing-rows="new Set()"
              :disabled-rows="new Set()"
              :empty-text="'暂无数据'"
              :loading-text="'加载中...'"
              :pagination-state="paginationConfig?.enabled ? paginationState : undefined"
              :selection-config="selectionConfig"
              :border-config="borderConfig"
              :cell-border-classes="cellBorderClasses"
              :get-fixed-column-border="getFixedColumnBorder"
              @row-click="handleRowClick"
              @row-dblclick="handleRowDoubleClick"
              @row-mouseenter="handleRowMouseEnter"
              @row-mouseleave="handleRowMouseLeave"
              @row-contextmenu="handleRowContextMenu"
              @selection-change="handleSelectionChange"
            >
              <!-- Pass through cell slots -->
              <template
                v-for="column in processedColumns"
                :key="`cell-${column.key}`"
                #[`cell-${column.key}`]="cellProps"
              >
                <slot
                  :name="`cell-${column.key}`"
                  v-bind="cellProps"
                />
              </template>
            </TableBody>
          </div>
        </div>
        <!-- Regular table body without virtual scrolling -->
        <TableBody
          v-else
          :data="finalDisplayData"
          :columns="isHorizontalVirtualEnabled ? visibleColumns : processedColumns"
          :loading="false"
          :stripe-config="stripeConfig"
          :get-row-stripe-classes="getRowStripeClasses"
          :get-current-row-background="getCurrentRowBackground"
          :hover-mode="hoverMode"
          :selected-rows="tableState.selectedRows"
          :editing-rows="new Set()"
          :disabled-rows="new Set()"
          :empty-text="'暂无数据'"
          :loading-text="'加载中...'"
          :pagination-state="paginationConfig?.enabled ? paginationState : undefined"
          :selection-config="selectionConfig"
          :border-config="borderConfig"
          :cell-border-classes="cellBorderClasses"
          :get-fixed-column-border="getFixedColumnBorder"
          :get-fixed-cell-stripe="getFixedCellStripe"
          @row-click="handleRowClick"
          @row-dblclick="handleRowDoubleClick"
          @row-mouseenter="handleRowMouseEnter"
          @row-mouseleave="handleRowMouseLeave"
          @row-contextmenu="handleRowContextMenu"
          @selection-change="handleSelectionChange"
        >
          <!-- Pass through cell slots -->
          <template
            v-for="column in processedColumns"
            :key="`cell-${column.key}`"
            #[`cell-${column.key}`]="cellProps"
          >
            <slot
              :name="`cell-${column.key}`"
              v-bind="cellProps"
            />
          </template>
        </TableBody>
      </div>

      <!-- Pagination -->
      <TablePagination
        v-if="paginationConfig?.enabled"
        :current-page="paginationState.currentPage"
        :page-size="paginationState.pageSize"
        :total="paginationState.total"
        :total-pages="totalPages"
        :has-next-page="hasNextPage"
        :has-prev-page="hasPrevPage"
        :start-index="startIndex"
        :end-index="endIndex"
        :page-range="[...pageRange]"
        :show-size-changer="paginationConfig.showSizeChanger"
        :show-quick-jumper="paginationConfig.showQuickJumper"
        :show-total="paginationConfig.showTotal"
        :page-size-options="paginationConfig.pageSizeOptions"
        :pagination-border-classes="paginationBorderClasses"
        :input-border-classes="inputBorderClasses"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @show-size-change="handleShowSizeChange"
      >
        <!-- Pass through pagination slots -->
        <template #total="totalProps">
          <slot
            name="pagination-total"
            v-bind="totalProps"
          />
        </template>
        <template #first-icon>
          <slot name="pagination-first-icon" />
        </template>
        <template #prev-icon>
          <slot name="pagination-prev-icon" />
        </template>
        <template #next-icon>
          <slot name="pagination-next-icon" />
        </template>
        <template #last-icon>
          <slot name="pagination-last-icon" />
        </template>
        <template #ellipsis>
          <slot name="pagination-ellipsis" />
        </template>
      </TablePagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import TableHeader from './TableHeader.vue'
import TableBody from './TableBody.vue'
import TablePagination from './TablePagination.vue'
import { useSorting, useFiltering, usePagination } from '@/composables'
import { useVirtual } from '@/composables/virtual'
import { useBorderStyle } from '@/composables/useBorderStyle'
import { useStripeStyle } from '@/composables/useStripeStyle'
import { useFixedColumns } from '@/composables/useFixedColumns'
import type {
  TableConfig,
  TableColumn,
  TableRow,
  TableState,
  ToolbarConfig,
  PaginationConfig,
  PaginationState,
  SelectionConfig,
  ThemeConfig,
  SortConfig,
  FilterConfig,
  VirtualConfig,
  BorderConfig,
  StripeConfig,
  HoverMode,
  DragDropConfig,
  DragEvent,
  DragCancelReason
} from '@/types'

// Props definition
interface Props {
  config: TableConfig
  dragDropConfig?: Partial<DragDropConfig>
}

const props = defineProps<Props>()

// Emits definition
interface Emits {
  rowClick: [row: TableRow, index: number]
  rowSelect: [selectedRows: TableRow[]]
  cellEdit: [value: unknown, row: TableRow, column: TableColumn]
  sortChange: [sortConfig: SortConfig[]]
  filterChange: [filterConfig: FilterConfig]
  pageChange: [pageConfig: PaginationState]
  columnReorder: [newOrder: string[], dragEvent: DragEvent]
  error: [error: Error]
  loadingChange: [loading: boolean]
}

const emit = defineEmits<Emits>()

// Template refs
const tableContainer = ref<HTMLElement | null>(null)
const tableWrapper = ref<HTMLElement | null>(null)

// Internal state
const error = ref<Error | null>(null)
const loading = ref(false)

// Reactive column widths - this will store the current widths for each column
const columnWidths = ref<Record<string, number>>({})

// Column ordering state for drag-and-drop
const columnOrder = ref<string[]>([])
const hasCustomColumnOrder = computed(() => columnOrder.value.length > 0)

// Initialize table state
const tableState = reactive<TableState>({
  originalData: [],
  processedData: [],
  filteredData: [],
  selectedRows: new Set(),
  editingCell: null,
  focusedCell: null,
  sortConfig: [],
  filterConfig: {},
  paginationState: {
    currentPage: 1,
    pageSize: 10,
    total: 0
  },
  virtualState: {
    scrollTop: 0,
    visibleStart: 0,
    visibleEnd: 0,
    totalHeight: 0,
    containerHeight: 0
  },
  currentTheme: 'default',
  loading: false,
  error: null
})

// Methods (defined early to avoid hoisting issues)
const handleError = (err: Error): void => {
  error.value = err
  tableState.error = err
  emit('error', err)
  console.error('Table error:', err)
}

const validateAndProcessColumns = (columns: TableColumn[]): TableColumn[] => {
  if (!Array.isArray(columns) || columns.length === 0) {
    throw new Error('表格列配置不能为空')
  }

  const processedColumns = columns.map((column, index) => {
    if (!column.key) {
      throw new Error(`第 ${index + 1} 列缺少必需的 key 属性`)
    }
    if (!column.title && column.type !== 'selection' && column.type !== 'seq') {
      throw new Error(`列 "${column.key}" 缺少必需的 title 属性`)
    }

    const processedColumn = {
      align: 'left' as const,
      sortable: false,
      filterable: false,
      editable: false,
      type: 'default' as const,
      minWidth: 100, // 默认最小宽度 100px
      ...column
    }

    // 序号列的默认配置 - 参考 vxetable 的默认配置
    if (processedColumn.type === 'seq') {
      processedColumn.align = 'center' // 序号列默认居中
      processedColumn.sortable = false // 序号列不可排序
      processedColumn.filterable = false // 序号列不可筛选
      processedColumn.editable = false // 序号列不可编辑
      processedColumn.width = processedColumn.width || 60 // 序号列默认宽度 60px，参考 vxetable
      processedColumn.minWidth = 60 // 序号列最小宽度
      processedColumn.fixed = processedColumn.fixed || 'left' // 序号列默认固定在左侧
    }

    // 选择列的默认配置 - 参考 vxetable 的 type="checkbox" 模式
    if (processedColumn.type === 'selection') {
      processedColumn.align = 'center' // 选择列默认居中
      processedColumn.sortable = false // 选择列不可排序
      processedColumn.filterable = false // 选择列不可筛选
      processedColumn.editable = false // 选择列不可编辑
      processedColumn.width = processedColumn.width || 60 // 选择列默认宽度 60px，与序号列一致
      processedColumn.minWidth = 60 // 选择列最小宽度
      processedColumn.fixed = processedColumn.fixed || 'left' // 选择列默认固定在左侧

      // 设置默认的选择配置
      if (!processedColumn.selectionConfig) {
        processedColumn.selectionConfig = {
          enabled: true,
          mode: 'multiple',
          showSelectAll: true
        }
      }
    }

    // 记录是否显式设置了宽度
    const hasExplicitWidth = !!column.width

    // 如果没有设置宽度，根据列类型设置默认宽度
    if (!processedColumn.width) {
      // 根据标题长度估算默认宽度，最小宽度将在后续验证步骤中确保，最大200px
      const titleLength = processedColumn.title.length
      const estimatedWidth = Math.min(200, titleLength * 16 + 60)
      processedColumn.width = estimatedWidth
    }

    // 宽度和最小宽度的冲突处理
    if (processedColumn.width && processedColumn.minWidth) {
      const width =
        typeof processedColumn.width === 'number'
          ? processedColumn.width
          : parseInt(processedColumn.width.toString())

      if (width < processedColumn.minWidth) {
        if (hasExplicitWidth) {
          // 如果用户显式设置了宽度，调整 minWidth 以适应用户设置
          console.warn(
            `列 "${processedColumn.key}": width (${width}px) 小于 minWidth (${processedColumn.minWidth}px), 已将 minWidth 调整为 ${width}px`
          )
          processedColumn.minWidth = width
        } else {
          // 如果是自动计算的宽度，调整 width 以满足 minWidth
          console.warn(
            `列 "${processedColumn.key}": 计算宽度 (${width}px) 小于 minWidth (${processedColumn.minWidth}px), 已将 width 调整为 ${processedColumn.minWidth}px`
          )
          processedColumn.width = processedColumn.minWidth
        }
      }
    }

    return processedColumn
  })

  return processedColumns
}

const validateAndProcessData = (data: TableRow[]): TableRow[] => {
  if (!Array.isArray(data)) {
    throw new Error('表格数据必须是数组格式')
  }

  return data.map((row, index) => ({
    _id: row._id ?? row['id'] ?? index,
    _selected: false,
    _editing: false,
    _disabled: false,
    ...row
  }))
}

// Computed properties
const toolbarConfig = computed<ToolbarConfig | undefined>(() => props.config.toolbar)
const paginationConfig = computed<PaginationConfig | undefined>(() => props.config.pagination)
const selectionConfig = computed<SelectionConfig | undefined>(() => {
  const selectionColumn = processedColumns.value.find(col => col.type === 'selection')
  return selectionColumn?.selectionConfig
})
const virtualConfig = computed<VirtualConfig | undefined>(() => props.config.virtual)
const themeConfig = computed<ThemeConfig | undefined>(() => props.config.theme)
const dragDropConfig = computed<Partial<DragDropConfig> | undefined>(() => {
  return { ...props.config.dragDrop, ...props.dragDropConfig }
})

const currentTheme = computed(() => themeConfig.value?.name || 'default')

// 边框配置
const borderConfig = computed<BorderConfig>(() => {
  const border = props.config.border
  let type: BorderConfig['type'] = 'default' // 默认值

  if (border === false || border === 'default') {
    type = 'default'
  } else if (border === true || border === 'full') {
    type = 'full'
  } else if (border === 'outer') {
    type = 'outer'
  } else if (border === 'inner') {
    type = 'inner'
  } else if (border === 'none') {
    type = 'none'
  }

  return { type }
})

// 斑马行配置
const stripeConfig = computed<StripeConfig>(() => {
  return props.config.stripe || { type: 'none', enabled: false }
})

// 悬停配置
const hoverMode = computed<HoverMode>(() => {
  return props.config.hover || 'none'
})

// 使用边框样式管理（移除固定列逻辑）
const {
  containerBorderClasses,
  headerBorderClasses,
  cellBorderClasses,
  borderVariables,
  paginationBorderClasses,
  inputBorderClasses
} = useBorderStyle({ borderConfig })

// 使用斑马行样式管理
const { getRowStripeClasses, getCurrentRowBackground, getFixedCellStripe } = useStripeStyle({
  stripeConfig: stripeConfig.value
})

// Computed loading state that combines internal loading and prop loading
const isLoading = computed(() => loading.value || props.config.loading || false)

const containerClasses = computed(() => {
  const classes = ['table-container']

  // 使用统一边框管理
  classes.push(...containerBorderClasses.value)

  if (hoverMode.value !== 'none') classes.push(`table-hover-${hoverMode.value}`)
  if (props.config.size) classes.push(`table-size-${props.config.size}`)

  return classes
})

const containerStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.config.width) {
    style['width'] =
      typeof props.config.width === 'number' ? `${props.config.width}px` : props.config.width
  } else {
    // 默认宽度为 100%（相对于外层容器）
    style['width'] = '100%'
  }

  // 添加边框变量
  Object.assign(style, borderVariables.value)

  return style
})

const processedColumns = computed<TableColumn[]>(() => {
  if (error.value) return []
  try {
    const columns = validateAndProcessColumns(props.config.columns)
    // Apply reactive column widths
    const columnsWithWidths = columns.map(column => ({
      ...column,
      width: columnWidths.value[column.key] ?? column.width
    }))

    // 应用自定义列顺序（如果存在）
    let orderedColumns = columnsWithWidths
    if (hasCustomColumnOrder.value) {
      // 按照自定义顺序重新排列列
      const orderMap = new Map(columnOrder.value.map((key, index) => [key, index]))
      orderedColumns = [...columnsWithWidths].sort((a, b) => {
        const aIndex = orderMap.get(a.key) ?? Infinity
        const bIndex = orderMap.get(b.key) ?? Infinity
        return aIndex - bIndex
      })
    }

    // 重新排列列顺序：左固定列 + 非固定列 + 右固定列
    const leftFixedColumns = orderedColumns.filter(col => col.fixed === 'left')
    const normalColumns = orderedColumns.filter(col => !col.fixed)
    const rightFixedColumns = orderedColumns.filter(col => col.fixed === 'right')

    // 按照新的顺序组合列
    const reorderedColumns = [...leftFixedColumns, ...normalColumns, ...rightFixedColumns]

    // 固定列位置将由 useFixedColumns 自动计算
    return reorderedColumns
  } catch (err) {
    handleError(err as Error)
    return []
  }
})

// 使用固定列管理（在 processedColumns 定义后）
const { 
  getFixedColumnStyle, 
  initializeFixedPositions,
  cleanup: cleanupFixedColumns 
} = useFixedColumns({
  containerRef: tableWrapper,
  columns: processedColumns,
  enabled: true
})

// 创建兼容的 getFixedColumnBorder 函数（保持API兼容性）
const getFixedColumnBorder = (column: TableColumn) => {
  return getFixedColumnStyle(column)
}

const processedData = computed<TableRow[]>(() => {
  if (error.value) return []
  try {
    return validateAndProcessData(props.config.data)
  } catch (err) {
    handleError(err as Error)
    return []
  }
})

// Initialize sorting functionality
const {
  sortConfigs,
  // isSorted,
  sortedData,
  handleSort,
  handleHeaderClick: handleSortHeaderClick,
  setSortConfigs,
  clearSort,
  getSortState
} = useSorting(processedData, processedColumns, {
  multiSort: props.config.multiSort ?? false,
  defaultSortConfigs: props.config.defaultSort || [],
  customSortFunctions: props.config.customSortFunctions || {},
  onSortChange: configs => {
    // Update table state
    tableState.sortConfig = configs
    emit('sortChange', configs)
  }
})

// Initialize filtering functionality
const {
  searchConfig,
  filterConditions,
  filteredData,
  isFiltered,
  isSearching,
  searchText,
  setSearchText,
  addFilterCondition,
  removeFilterCondition,
  clearAllFilters,
  clearSearch,
  getColumnValues,
  highlightText,
  createSimpleFilter
} = useFiltering(sortedData, processedColumns, {
  caseSensitive: props.config.caseSensitive ?? false,
  enableRegex: props.config.enableRegex ?? true,
  enableHighlight: props.config.enableHighlight ?? true,
  debounceMs: props.config.searchDebounce ?? 300,
  onFilterChange: config => {
    // Update table state
    tableState.filterConfig = config
    emit('filterChange', config)
  }
})

// Initialize pagination functionality
const {
  paginationState,
  currentPage,
  pageSize,
  total,
  totalPages,
  hasNextPage,
  hasPrevPage,
  startIndex,
  endIndex,
  pageRange,
  setPage,
  setPageSize,
  setTotal,
  nextPage,
  prevPage,
  firstPage,
  lastPage,
  jumpToPage,
  reset: resetPagination,
  updatePagination,
  getPageData,
  isValidPage,
  getPageInfo
} = usePagination(0, {
  defaultPageSize: paginationConfig.value?.pageSize || 10,
  defaultCurrentPage: paginationConfig.value?.currentPage || 1,
  showSizeChanger: paginationConfig.value?.showSizeChanger ?? true,
  showQuickJumper: paginationConfig.value?.showQuickJumper ?? true,
  showTotal: paginationConfig.value?.showTotal ?? true,
  pageSizeOptions: paginationConfig.value?.pageSizeOptions || [10, 20, 50, 100],
  onPageChange: (page, pageSize) => {
    // Update table state
    tableState.paginationState.currentPage = page
    tableState.paginationState.pageSize = pageSize
    emit('pageChange', { currentPage: page, pageSize, total: paginationState.total })
  },
  onPageSizeChange: (page, pageSize) => {
    // Update table state
    tableState.paginationState.currentPage = page
    tableState.paginationState.pageSize = pageSize
    emit('pageChange', { currentPage: page, pageSize, total: paginationState.total })
  }
})

const displayData = computed<TableRow[]>(() => {
  // Start with filtered data (which includes sorting)
  let data = filteredData.value

  // Apply pagination if enabled
  if (paginationConfig.value?.enabled) {
    data = getPageData(data)
  }

  console.log('DisplayData computed:', {
    filteredDataLength: filteredData.value.length,
    paginationEnabled: paginationConfig.value?.enabled,
    paginationConfig: paginationConfig.value,
    pageSize: paginationState.pageSize,
    currentPage: paginationState.currentPage,
    resultLength: data.length,
    firstId: data[0]?.id,
    lastId: data[data.length - 1]?.id
  })

  return data
})

// Virtual scrolling setup - 参考 vxetable 的实现方式
const {
  virtualState,
  isVirtualEnabled,
  visibleData,
  containerStyle: virtualContainerStyle,
  scrollAreaStyle: virtualScrollAreaStyle,
  scrollToIndex,
  scrollToTop,
  updateVisibleRange,
  getItemHeight,
  getMetrics,
  // 横向虚拟滚动
  horizontalVirtualState,
  isHorizontalVirtualEnabled,
  visibleColumns,
  // leftFixedColumns,
  // rightFixedColumns,
  // scrollableColumns,
  // horizontalContainerStyle,
  // horizontalScrollAreaStyle,
  scrollToColumn,
  scrollToLeft,
  updateHorizontalVisibleRange,
  getColumnWidth,
  getTotalWidth,
  getHorizontalMetrics
} = useVirtual({
  containerRef: tableWrapper,
  data: displayData,
  columns: processedColumns,
  config: virtualConfig
})

// 实际渲染的数据：如果启用虚拟滚动则使用 visibleData，否则使用 displayData
const finalDisplayData = computed<TableRow[]>(() => {
  const result = isVirtualEnabled.value ? visibleData.value : displayData.value
  return result
})

const tableWrapperStyle = computed(() => {
  const style: Record<string, string> = {}

  // 先合并虚拟滚动样式
  if (isVirtualEnabled.value && virtualContainerStyle.value) {
    Object.assign(style, virtualContainerStyle.value)
  }

  // 用户配置的高度具有最高优先级，覆盖虚拟滚动的默认高度
  if (props.config.height) {
    style['height'] =
      typeof props.config.height === 'number' ? `${props.config.height}px` : props.config.height
  }

  if (props.config.maxHeight) {
    style['maxHeight'] =
      typeof props.config.maxHeight === 'number'
        ? `${props.config.maxHeight}px`
        : props.config.maxHeight
  }

  return style
})

// Duplicate methods removed

const getToolbarTitle = (): string => {
  if (!toolbarConfig.value?.title) return ''

  if (typeof toolbarConfig.value.title === 'string') {
    return toolbarConfig.value.title
  }

  return toolbarConfig.value.title.text || ''
}

const handleRetry = (): void => {
  error.value = null
  tableState.error = null
  // Re-trigger reactive updates
  nextTick(() => {
    initializeTableState()
  })
}

const handleSortChange = (column: TableColumn, direction: 'asc' | 'desc' | null): void => {
  handleSort(column, direction)
}

const handleColumnResize = (column: TableColumn, width: number): void => {
  // 更新列宽存储
  columnWidths.value[column.key] = width

  // 在拖动过程中，需要同步更新固定列位置
  // 使用 requestAnimationFrame 确保在下一帧渲染前更新
  requestAnimationFrame(() => {
    // 强制触发固定列位置重新计算
    // 这确保固定列位置基于最新的DOM宽度计算
    try {
      initializeFixedPositions()
    } catch (err) {
      console.warn('Failed to update fixed column positions:', err)
    }
  })
}

const handleColumnReorder = (newOrder: string[], dragEvent: DragEvent): void => {
  // 更新列顺序状态
  columnOrder.value = newOrder
  
  // 更新列的内部顺序索引
  processedColumns.value.forEach(column => {
    column._dragOrder = newOrder.indexOf(column.key)
  })

  // 使用 requestAnimationFrame 确保在下一帧重新计算固定列位置
  requestAnimationFrame(() => {
    try {
      initializeFixedPositions()
    } catch (err) {
      console.warn('Failed to update fixed column positions after reorder:', err)
    }
  })

  // 触发外部事件
  emit('columnReorder', newOrder, dragEvent)
}

const handleDragStart = (column: TableColumn): void => {
  // 拖拽开始
  console.log('Drag start:', column.key)
}

const handleDragEnd = (column: TableColumn, success: boolean): void => {
  // 拖拽结束
  console.log('Drag end:', column.key, success)
}

const handleDragCancel = (reason: DragCancelReason, column: TableColumn | null): void => {
  // 拖拽取消
  console.log('Drag cancel:', reason, column?.key)
}

const handleHeaderClick = (column: TableColumn, event: MouseEvent): void => {
  // Handle sorting if column is sortable
  if (column.sortable) {
    handleSortHeaderClick(column, event)
  }

  // Header clicked
}

const handleRowClick = (row: TableRow, index: number, _event: MouseEvent): void => {
  emit('rowClick', row, index)
  // Row clicked
}

const handleRowDoubleClick = (_row: TableRow, _index: number, _event: MouseEvent): void => {
  // Row double clicked
}

const handleRowMouseEnter = (_row: TableRow, _index: number, _event: MouseEvent): void => {
  // Row mouse enter
}

const handleRowMouseLeave = (_row: TableRow, _index: number, _event: MouseEvent): void => {
  // Row mouse leave
}

const handleRowContextMenu = (_row: TableRow, _index: number, _event: MouseEvent): void => {
  // Row context menu
}

const handleSelectionChange = (row: TableRow, selected: boolean, _event: Event): void => {
  if (!selectionConfig.value?.enabled) return

  // 更新行的选中状态
  row._selected = selected

  // 获取一致的行键
  const rowKey = row._id ?? row['id'] ?? processedData.value.indexOf(row)

  if (selectionConfig.value.mode === 'single') {
    // 单选模式：取消其他行的选中状态
    if (selected) {
      tableState.selectedRows.clear()
      processedData.value.forEach(r => {
        const rKey = r._id ?? r['id'] ?? processedData.value.indexOf(r)
        if (rKey !== rowKey) {
          r._selected = false
        }
      })
      tableState.selectedRows.add(rowKey)
    } else {
      tableState.selectedRows.delete(rowKey)
    }
  } else {
    // 多选模式
    if (selected) {
      tableState.selectedRows.add(rowKey)
    } else {
      tableState.selectedRows.delete(rowKey)
    }
  }

  // 获取当前选中的行
  const selectedRows = processedData.value.filter(r => r._selected)

  // 触发 selection-change 事件
  emit('rowSelect', selectedRows)
}

const handleSelectAll = (selected: boolean, _event: Event): void => {
  if (!selectionConfig.value?.enabled || selectionConfig.value.mode === 'single') return

  // 获取所有可选择的行
  const selectableRows = processedData.value.filter(
    row => !row._disabled && !selectionConfig.value?.getCheckboxProps?.(row)?.disabled
  )

  if (selected) {
    // 全选：添加所有可选择行到选中集合
    selectableRows.forEach(row => {
      row._selected = true
      const rowKey = row._id ?? row['id'] ?? processedData.value.indexOf(row)
      tableState.selectedRows.add(rowKey)
    })
  } else {
    // 取消全选：清空所有选中状态
    tableState.selectedRows.clear()
    processedData.value.forEach(row => {
      row._selected = false
    })
  }

  // 触发 selection-change 事件
  const selectedRows = processedData.value.filter(r => r._selected)
  emit('rowSelect', selectedRows)
}

const handleKeydown = (_event: KeyboardEvent): void => {
  // Placeholder for keyboard navigation
  // Will be implemented in later tasks
  // Handle keyboard event
}

const handlePageChange = (page: number): void => {
  setPage(page)
}

const handlePageSizeChange = (pageSize: number): void => {
  setPageSize(pageSize)
}

const handleShowSizeChange = (_current: number, _size: number): void => {
  // This is called when both page and size change
  // Page size changed
}

const initializeTableState = (): void => {
  try {
    // Initialize data state
    tableState.originalData = [...processedData.value]
    tableState.processedData = [...processedData.value]
    tableState.filteredData = [...filteredData.value]

    // Initialize sort state
    tableState.sortConfig = [...sortConfigs.value]

    // Initialize filter state
    tableState.filterConfig = {
      search: searchConfig.value,
      conditions: filterConditions.value
    }

    // Initialize pagination state
    if (paginationConfig.value?.enabled) {
      const totalItems = filteredData.value.length
      setTotal(totalItems)

      // Update pagination config if provided
      updatePagination({
        currentPage: paginationConfig.value.currentPage || 1,
        pageSize: paginationConfig.value.pageSize || 10,
        total: totalItems
      })

      // Sync with table state
      tableState.paginationState = {
        currentPage: paginationState.currentPage,
        pageSize: paginationState.pageSize,
        total: paginationState.total
      }
    }

    // Initialize theme
    tableState.currentTheme = currentTheme.value

    // Clear error state
    error.value = null
    tableState.error = null
  } catch (err) {
    handleError(err as Error)
  }
}

const setLoading = (isLoading: boolean): void => {
  loading.value = isLoading
  tableState.loading = isLoading
  emit('loadingChange', isLoading)
}

// Lifecycle hooks
onMounted(() => {
  try {
    initializeTableState()
  } catch (err) {
    handleError(err as Error)
  }
})

onUnmounted(() => {
  // Cleanup
  tableState.selectedRows.clear()
  // 清理固定列相关资源
  cleanupFixedColumns()
})

// Watchers
watch(
  () => props.config.data,
  () => {
    try {
      initializeTableState()
    } catch (err) {
      handleError(err as Error)
    }
  },
  { deep: true }
)

watch(
  () => props.config.columns,
  () => {
    try {
      initializeTableState()
    } catch (err) {
      handleError(err as Error)
    }
  },
  { deep: true }
)

watch(
  () => props.config.loading,
  newLoading => {
    if (newLoading !== undefined) {
      setLoading(newLoading)
    }
  }
)

// Watch filtered data changes to update pagination total
watch(
  filteredData,
  newFilteredData => {
    if (paginationConfig.value?.enabled) {
      const totalItems = newFilteredData.length
      setTotal(totalItems)

      // Sync with table state
      tableState.paginationState.total = totalItems
      tableState.paginationState.currentPage = paginationState.currentPage
    }
  },
  { immediate: true }
)

// Watch search text changes to reset pagination
watch(searchText, (newSearchText, oldSearchText) => {
  if (
    paginationConfig.value?.enabled &&
    oldSearchText !== undefined &&
    newSearchText !== oldSearchText
  ) {
    setPage(1)
  }
})

// Expose public methods for parent components
defineExpose({
  getTableState: () => tableState,
  refresh: initializeTableState,
  setLoading,
  clearError: () => {
    error.value = null
    tableState.error = null
  },
  // Sorting methods
  setSortConfigs,
  clearSort,
  getSortState,
  // Filtering methods
  setSearchText,
  addFilterCondition,
  removeFilterCondition,
  clearAllFilters,
  clearSearch,
  getColumnValues,
  highlightText,
  createSimpleFilter,
  // Pagination methods
  setPage,
  setPageSize,
  setTotal,
  nextPage,
  prevPage,
  firstPage,
  lastPage,
  jumpToPage,
  resetPagination,
  updatePagination,
  getPageData,
  isValidPage,
  getPageInfo,
  // State getters
  getFilteredData: () => filteredData.value,
  getSearchText: () => searchText.value,
  isFiltered: () => isFiltered.value,
  isSearching: () => isSearching.value,
  getCurrentPage: () => currentPage.value,
  getPageSize: () => pageSize.value,
  getTotal: () => total.value,
  getTotalPages: () => totalPages.value,
  // Column configuration
  processedColumns: () => processedColumns.value,
  // Virtual scrolling methods - 参考 vxetable 的方法暴露
  scrollToIndex,
  scrollToTop,
  updateVisibleRange,
  getItemHeight,
  isVirtualEnabled: () => isVirtualEnabled.value,
  getVirtualState: () => virtualState.value,
  getVirtualMetrics: getMetrics,
  // Horizontal virtual scrolling methods
  scrollToColumn,
  scrollToLeft,
  updateHorizontalVisibleRange,
  getColumnWidth,
  getTotalWidth,
  isHorizontalVirtualEnabled: () => isHorizontalVirtualEnabled.value,
  getHorizontalVirtualState: () => horizontalVirtualState.value,
  getHorizontalMetrics
})
</script>

<style scoped>
@reference "../../styles/base.css";

.vue-table-container {
  @apply w-full relative;
}

.table-container {
  @apply bg-table-bg rounded-table overflow-hidden;
}

.table-size-small {
  @apply text-sm;
}

.table-size-large {
  @apply text-lg;
}

/* Error boundary styles */
.table-error-boundary {
  @apply p-8 text-center bg-red-50 border border-red-200 rounded-table;
}

.error-content {
  @apply max-w-md mx-auto;
}

.error-title {
  @apply text-lg font-semibold text-red-800 mb-2;
}

.error-message {
  @apply text-red-600 mb-4;
}

.error-retry-btn {
  @apply px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors;
}

/* Loading styles */
.table-loading {
  @apply flex flex-col items-center justify-center p-8 text-table-text-secondary;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-table-border border-t-table-primary rounded-full animate-spin mb-2;
}

.loading-text {
  @apply text-sm;
}

/* Table content styles */
.table-content {
  @apply w-full;
}

.table-toolbar-placeholder {
  @apply p-4 border-b border-table-border bg-table-bg-secondary;
}

.toolbar-title {
  @apply font-semibold text-table-text;
}

.table-wrapper {
  @apply overflow-auto;
  /* 优化横向滚动体验 */
  scrollbar-width: thin;
  scrollbar-color: var(--table-border) transparent;
  /* 确保滚动条不会覆盖内容 */
  padding-bottom: 3px;
}

/* Webkit 浏览器的滚动条样式 */
.table-wrapper::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: var(--table-bg-secondary);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: var(--table-border);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--table-border-hover);
}

/* 横向滚动时的表格样式优化 */
.table-wrapper {
  /* 确保滚动时的性能 */
  will-change: scroll-position;
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

/* 修复滚动时可能出现的边框问题 */
.table-wrapper .table-header,
.table-wrapper .table-body {
  /* 确保表格内容不会因为滚动而出现样式问题 */
  min-width: fit-content;
}

/* 确保固定列在滚动时保持正确的层级和样式 */
.table-wrapper {
  position: relative;
}

/* 为固定列添加渐变边缘效果，增强视觉层次 */
.table-wrapper::before,
.table-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 10px;
  pointer-events: none;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.table-wrapper::before {
  left: 0;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
}

.table-wrapper::after {
  right: 0;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.1), transparent);
}

/* 当表格可以滚动时显示渐变边缘 */
.table-wrapper.can-scroll-left::before {
  opacity: 1;
}

.table-wrapper.can-scroll-right::after {
  opacity: 1;
}

/* Virtual scrolling container styles */
.virtual-scroll-container {
  @apply relative;
  /* 创建虚拟滚动的总高度 */
}

.virtual-scroll-area {
  @apply absolute top-0 left-0 right-0;
  /* 偏移可见区域到正确位置 */
}

/* Table header integration */

/* Table body integration */

/* Search highlighting styles */
:deep(.table-search-highlight) {
  @apply bg-yellow-200 text-yellow-900 px-1 rounded;
}

:deep(.table-search-highlight.dark) {
  @apply bg-yellow-800 text-yellow-100;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-cell,
  .table-cell {
    @apply px-2 py-2 text-sm;
  }

  .table-toolbar-placeholder,
  .table-pagination-placeholder {
    @apply p-2;
  }
}
</style>
