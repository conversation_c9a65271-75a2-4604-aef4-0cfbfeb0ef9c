<template>
  <div
    class="table-body"
    :class="bodyClasses"
  >
    <!-- Empty state -->
    <div
      v-if="data.length === 0"
      class="table-empty"
    >
      <slot name="empty">
        <div class="empty-content">
          <div class="empty-icon">
            <Icon
              icon="empty"
              class="empty-svg"
              size="2xl"
            />
          </div>
          <p class="empty-text">
            {{ emptyText }}
          </p>
          <p
            v-if="emptyDescription"
            class="empty-description"
          >
            {{ emptyDescription }}
          </p>
        </div>
      </slot>
    </div>

    <!-- Loading state -->
    <div
      v-else-if="loading"
      class="table-loading"
    >
      <slot name="loading">
        <div class="loading-content">
          <div class="loading-spinner" />
          <p class="loading-text">
            {{ loadingText }}
          </p>
        </div>
      </slot>
    </div>

    <!-- Data rows -->
    <div
      v-else
      class="table-rows"
      :style="rowsStyle"
    >
      <TableRow
        v-for="(row, index) in data"
        :key="getRowKey(row, index)"
        :row="row"
        :columns="columns"
        :index="index"
        :selected="isRowSelected(row)"
        :disabled="isRowDisabled(row)"
        :editing="isRowEditing(row)"
        :hover-mode="hoverMode"
        :row-height="rowHeight"
        :pagination-state="paginationState"
        :selection-config="selectionConfig"
        :selected-rows="selectedRows"
        :border-config="borderConfig"
        :cell-border-classes="cellBorderClasses"
        :get-fixed-column-border="getFixedColumnBorder"
        :get-fixed-cell-stripe="getFixedCellStripe"
        :stripe-config="stripeConfig"
        :get-row-stripe-classes="getRowStripeClasses"
        :get-current-row-background="getCurrentRowBackground"
        @click="handleRowClick(row, index, $event)"
        @dblclick="handleRowDoubleClick(row, index, $event)"
        @mouseenter="handleRowMouseEnter(row, index, $event)"
        @mouseleave="handleRowMouseLeave(row, index, $event)"
        @contextmenu="handleRowContextMenu(row, index, $event)"
        @selection-change="handleSelectionChange"
      >
        <!-- Pass through cell slots -->
        <template
          v-for="column in columns"
          :key="`cell-${column.key}`"
          #[`cell-${column.key}`]="cellProps"
        >
          <slot
            :name="`cell-${column.key}`"
            v-bind="cellProps"
          />
        </template>
      </TableRow>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '../Icon'
import TableRow from './TableRow.vue'
import type { TableColumn, TableRow as TableRowType, SelectionConfig, BorderConfig, StripeConfig } from '@/types'

// Props definition
interface Props {
  data: TableRowType[]
  columns: TableColumn[]
  loading?: boolean
  hoverMode?: 'row' | 'cell' | 'none'
  rowHeight?: number | string
  emptyText?: string
  emptyDescription?: string
  loadingText?: string
  selectedRows?: Set<string | number>
  editingRows?: Set<string | number>
  disabledRows?: Set<string | number>
  maxHeight?: number | string
  paginationState?: { currentPage: number; pageSize: number; total: number } // 分页信息
  selectionConfig?: SelectionConfig // 选择列配置
  borderConfig?: BorderConfig
  cellBorderClasses?: string[]
  getFixedColumnBorder?: (column: TableColumn) => {
    classes: string[]
    styles: Record<string, string>
  }
  getFixedCellStripe?: (column: TableColumn, rowIndex: number, selected?: boolean, hover?: boolean, editing?: boolean) => { classes: string[]; styles: Record<string, string> }
  stripeConfig?: StripeConfig
  getRowStripeClasses?: (index: number, selected?: boolean, hover?: boolean, editing?: boolean) => string[]
  getCurrentRowBackground?: (index: number, selected?: boolean, hover?: boolean, editing?: boolean) => string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hoverMode: 'none',
  rowHeight: 48,
  emptyText: '暂无数据',
  emptyDescription: '请检查您的数据源或筛选条件',
  loadingText: '加载中...',
  maxHeight: undefined,
  selectedRows: () => new Set(),
  editingRows: () => new Set(),
  disabledRows: () => new Set(),
  paginationState: undefined,
  selectionConfig: undefined,
  borderConfig: () => ({ type: 'default' }),
  cellBorderClasses: () => [],
  getFixedColumnBorder: () => (_column: TableColumn) => ({ classes: [], styles: {} }),
  getFixedCellStripe: () => (_column: TableColumn, _rowIndex: number, _selected?: boolean, _hover?: boolean, _editing?: boolean) => ({ classes: [], styles: {} }),
  stripeConfig: () => ({ type: 'default', enabled: false }),
  getRowStripeClasses: () => (_index: number, _selected?: boolean, _hover?: boolean, _editing?: boolean) => [],
  getCurrentRowBackground: () => (_index: number, _selected?: boolean, _hover?: boolean, _editing?: boolean) => 'var(--table-bg)'
})

// Emits definition
interface Emits {
  rowClick: [row: TableRowType, index: number, event: MouseEvent]
  rowDblclick: [row: TableRowType, index: number, event: MouseEvent]
  rowMouseenter: [row: TableRowType, index: number, event: MouseEvent]
  rowMouseleave: [row: TableRowType, index: number, event: MouseEvent]
  rowContextmenu: [row: TableRowType, index: number, event: MouseEvent]
  selectionChange: [row: TableRowType, selected: boolean, event: Event]
}

const emit = defineEmits<Emits>()

// Computed properties
const bodyClasses = computed(() => {
  const classes = ['body']

  if (props.loading) classes.push('body-loading')
  if (props.data.length === 0) classes.push('body-empty')
  if (props.hoverMode !== 'none') classes.push(`body-hover-${props.hoverMode}`)

  return classes
})

const rowsStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.maxHeight) {
    style['maxHeight'] =
      typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight
    style['overflowY'] = 'auto'
  }

  return style
})

// Methods
const getRowKey = (row: TableRowType, index: number): string | number => {
  return row._id ?? row['id'] ?? index
}

const isRowSelected = (row: TableRowType): boolean => {
  const rowKey = getRowKey(row, 0)
  return props.selectedRows.has(rowKey)
}

const isRowDisabled = (row: TableRowType): boolean => {
  if (row._disabled) return true
  const rowKey = getRowKey(row, 0)
  return props.disabledRows.has(rowKey)
}

const isRowEditing = (row: TableRowType): boolean => {
  if (row._editing) return true
  const rowKey = getRowKey(row, 0)
  return props.editingRows.has(rowKey)
}

const handleRowClick = (row: TableRowType, index: number, event: MouseEvent): void => {
  emit('rowClick', row, index, event)
}

const handleRowDoubleClick = (row: TableRowType, index: number, event: MouseEvent): void => {
  emit('rowDblclick', row, index, event)
}

const handleRowMouseEnter = (row: TableRowType, index: number, event: MouseEvent): void => {
  emit('rowMouseenter', row, index, event)
}

const handleRowMouseLeave = (row: TableRowType, index: number, event: MouseEvent): void => {
  emit('rowMouseleave', row, index, event)
}

const handleRowContextMenu = (row: TableRowType, index: number, event: MouseEvent): void => {
  emit('rowContextmenu', row, index, event)
}

const handleSelectionChange = (row: TableRowType, selected: boolean, event: Event): void => {
  emit('selectionChange', row, selected, event)
}
</script>

<style scoped>
@reference "../../styles/base.css";

.table-body {
  @apply relative;
}

/* Empty state styles */
.table-empty {
  @apply flex items-center justify-center py-16 px-4;
}

.empty-content {
  @apply text-center max-w-sm;
}

.empty-icon {
  @apply w-16 h-16 mx-auto mb-4 text-table-text-secondary opacity-50;
}

.empty-svg {
  @apply w-full h-full fill-current;
}

.empty-text {
  @apply text-lg font-medium text-table-text-secondary mb-2;
}

.empty-description {
  @apply text-sm text-table-text-secondary opacity-75;
}

/* Loading state styles */
.table-loading {
  @apply flex items-center justify-center py-16 px-4;
}

.loading-content {
  @apply text-center;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-table-border border-t-table-primary rounded-full animate-spin mx-auto mb-4;
}

.loading-text {
  @apply text-table-text-secondary;
}

/* Rows container styles */
.table-rows {
  @apply divide-table-border; /* divide-y */
}

/* Body state modifiers */
.body-loading {
  @apply bg-table-bg-secondary/50;
}

.body-empty {
  @apply bg-table-bg;
}

/* Responsive design */
@media (max-width: 768px) {
  .table-empty,
  .table-loading {
    @apply py-8 px-2;
  }

  .empty-icon {
    @apply w-12 h-12 mb-3;
  }

  .empty-text {
    @apply text-base mb-1;
  }

  .empty-description {
    @apply text-xs;
  }

  .loading-spinner {
    @apply w-6 h-6 mb-3;
  }

  .loading-text {
    @apply text-sm;
  }
}
</style>
