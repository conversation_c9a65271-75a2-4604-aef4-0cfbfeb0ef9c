<template>
  <div
    class="table-header"
    :class="headerClasses"
  >
    <div
      class="header-row"
      :style="headerRowStyle"
    >
      <div
        v-for="column in columns"
        :key="column.key"
        :data-testid="`header-cell-${column.key}`"
        :data-column-key="column.key"
        class="header-cell"
        :class="getHeaderCellClasses(column)"
        :style="getHeaderCellStyle(column)"
        @click="handleHeaderClick(column, $event)"
        @mousedown="handleHeaderMouseDown(column, $event)"
        @mouseenter="handleHeaderMouseEnter(column)"
        @mouseleave="handleHeaderMouseLeave(column)"
      >
        <!-- Column title and content -->
        <div class="header-cell-content">
          <!-- Custom header slot -->
          <slot
            :name="`header-${column.key}`"
            :column="column"
            :sort-config="getSortConfig(column)"
          >
            <!-- Selection column header -->
            <template v-if="column.type === 'selection'">
              <!-- Multiple selection mode: show select-all checkbox -->
              <div
                v-if="
                  props.selectionConfig?.mode === 'multiple' &&
                  props.selectionConfig.showSelectAll !== false
                "
                class="selection-header"
              >
                <input
                  type="checkbox"
                  class="selection-checkbox"
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  :disabled="selectableRows.length === 0"
                  @change="handleSelectAll"
                />
              </div>
              <!-- Single selection mode: no header control -->
              <div
                v-else-if="props.selectionConfig?.mode === 'single'"
                class="selection-header selection-header-single"
              >
                <span class="selection-title">{{ column.title || '选择' }}</span>
              </div>
            </template>

            <!-- Default header content -->
            <div
              v-else
              class="header-title"
            >
              <span class="title-text">{{ column.title }}</span>

              <!-- Sort indicator -->
              <div
                v-if="column.sortable"
                class="sort-indicator"
                :class="getSortIndicatorClasses(column)"
              >
                <Icon
                  :icon="getSortIcon(column)"
                  class="sort-icon"
                  size="sm"
                />

                <!-- Sort priority indicator for multi-column sorting -->
                <span
                  v-if="getSortPriority(column) !== null"
                  class="sort-priority"
                >
                  {{ getSortPriority(column)! + 1 }}
                </span>
              </div>
            </div>
          </slot>
        </div>

        <!-- Column resizer -->
        <div
          v-if="column.resizable !== false && resizable"
          class="column-resizer"
          :class="{
            'resizer-visible': hoveredColumn === column.key,
            'resizer-active': resizingColumn === column.key
          }"
          @mousedown="handleResizerMouseDown($event, column)"
        >
          <div class="resizer-handle">
            <div class="resizer-line" />
            <div class="resizer-grip">
              <div class="grip-dot" />
              <div class="grip-dot" />
              <div class="grip-dot" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Icon } from '../Icon'
import { useColumnDragDrop } from '@/composables'
import type {
  TableColumn,
  SortConfig,
  SelectionConfig,
  TableRow,
  BorderConfig,
  DragDropConfig,
  DragEvent,
  DragCancelReason
} from '@/types'

// Props definition
interface Props {
  columns: TableColumn[]
  sortConfig?: SortConfig[]
  resizable?: boolean
  sticky?: boolean
  height?: number | string
  selectionConfig?: SelectionConfig
  data?: TableRow[]
  selectedRows?: Set<string | number>
  borderConfig?: BorderConfig
  headerBorderClasses?: string[]
  getFixedColumnBorder?: (column: TableColumn) => {
    classes: string[]
    styles: Record<string, string>
  }
  dragDropConfig?: Partial<DragDropConfig>
}

const props = withDefaults(defineProps<Props>(), {
  sortConfig: () => [],
  resizable: true,
  sticky: false,
  height: undefined,
  selectionConfig: undefined,
  data: () => [],
  selectedRows: () => new Set(),
  borderConfig: () => ({ type: 'outer' }),
  headerBorderClasses: () => [],
  getFixedColumnBorder: () => (_column: TableColumn) => ({ classes: [], styles: {} }),
  dragDropConfig: () => ({ enabled: true })
})

// Emits definition
interface Emits {
  sort: [column: TableColumn, direction: 'asc' | 'desc' | null]
  resize: [column: TableColumn, width: number]
  headerClick: [column: TableColumn, event: MouseEvent]
  selectAll: [selected: boolean, event: Event]
  columnReorder: [newOrder: string[], dragEvent: DragEvent]
  dragStart: [column: TableColumn]
  dragEnd: [column: TableColumn, success: boolean]
  dragCancel: [reason: DragCancelReason, column: TableColumn | null]
}

const emit = defineEmits<Emits>()

// Internal state
const hoveredColumn = ref<string | null>(null)
const resizingColumn = ref<string | null>(null)
const resizeStartX = ref(0)
const resizeStartWidth = ref(0)

// Initialize column drag-drop functionality
const { isDragging, startDragDetection, getColumnGroup } = useColumnDragDrop({
  columns: props.columns,
  config: props.dragDropConfig,
  onColumnReorder: (newOrder, dragEvent) => {
    emit('columnReorder', newOrder, dragEvent)
  },
  onDragStart: column => {
    emit('dragStart', column)
  },
  onDragEnd: (column, success) => {
    emit('dragEnd', column, success)
  },
  onDragCancel: (reason, column) => {
    emit('dragCancel', reason, column)
  }
})

// Selection state computations
const getRowKey = (row: TableRow, index: number): string | number => {
  return row._id ?? row['id'] ?? index
}

const selectableRows = computed(() => {
  if (!props.data || !props.selectionConfig?.enabled) return []
  return props.data.filter(
    row => !row._disabled && !props.selectionConfig?.getCheckboxProps?.(row)?.disabled
  )
})

const isAllSelected = computed(() => {
  if (!props.selectionConfig?.enabled || selectableRows.value.length === 0) return false
  return selectableRows.value.every(row => props.selectedRows?.has(getRowKey(row, 0)))
})

const isIndeterminate = computed(() => {
  if (!props.selectionConfig?.enabled || selectableRows.value.length === 0) return false
  const selectedCount = selectableRows.value.filter(row =>
    props.selectedRows?.has(getRowKey(row, 0))
  ).length
  return selectedCount > 0 && selectedCount < selectableRows.value.length
})

// Computed properties
const headerClasses = computed(() => {
  const classes = ['header']

  // 添加统一边框类
  if (props.headerBorderClasses) {
    classes.push(...props.headerBorderClasses)
  }

  if (props.sticky) classes.push('header-sticky')
  if (resizingColumn.value) classes.push('header-resizing')

  return classes
})

const headerRowStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.height) {
    style['height'] = typeof props.height === 'number' ? `${props.height}px` : props.height
  }

  return style
})

// Methods
const calculateColumnWidths = (
  columns: TableColumn[],
  containerWidth: number
): Record<string, number> => {
  const widths: Record<string, number> = {}
  let totalFixedWidth = 0
  const flexColumns: TableColumn[] = []

  // First pass: calculate fixed widths
  columns.forEach(column => {
    if (column.width) {
      const width =
        typeof column.width === 'number' ? column.width : parseInt(column.width.toString())
      widths[column.key] = width
      totalFixedWidth += width
    } else {
      flexColumns.push(column)
    }
  })

  // Second pass: distribute remaining width among flex columns
  if (flexColumns.length > 0) {
    const remainingWidth = Math.max(0, containerWidth - totalFixedWidth)
    const baseFlexWidth = Math.floor(remainingWidth / flexColumns.length)

    flexColumns.forEach((column, index) => {
      let width = baseFlexWidth

      // Apply min/max width constraints
      if (column.minWidth && width < column.minWidth) {
        width = column.minWidth
      }
      if (column.maxWidth && width > column.maxWidth) {
        width = column.maxWidth
      }

      // Give extra pixels to the last column to fill remaining space
      if (index === flexColumns.length - 1) {
        const usedWidth =
          Object.values(widths).reduce((sum, w) => sum + w, 0) +
          flexColumns.slice(0, -1).reduce((sum, col) => sum + (widths[col.key] || baseFlexWidth), 0)
        width = Math.max(width, containerWidth - usedWidth)
      }

      widths[column.key] = width
    })
  }

  return widths
}

const getHeaderCellClasses = (column: TableColumn): string[] => {
  const classes = ['cell']

  // 添加统一边框类
  if (props.borderConfig) {
    classes.push(`table-border-header-cell-${props.borderConfig.type}`)
  }

  // 序号列特殊样式
  if (column.type === 'seq') {
    classes.push('cell-seq')
  }

  // 选择列特殊样式
  if (column.type === 'selection') {
    classes.push('cell-selection')
  }

  if (column.align) classes.push(`cell-align-${column.align}`)
  if (column.sortable) classes.push('cell-sortable')

  // 固定列基础样式类 - 添加缺失的逻辑
  if (column.fixed === 'left') classes.push('cell-fixed-left')
  if (column.fixed === 'right') classes.push('cell-fixed-right')

  // 使用统一固定列边框管理
  if (column.fixed && props.getFixedColumnBorder) {
    const fixedBorder = props.getFixedColumnBorder(column)
    classes.push(...fixedBorder.classes)
  }

  if (column._isLastLeftFixed) classes.push('cell-last-left-fixed')
  if (column._isFirstRightFixed) classes.push('cell-first-right-fixed')
  if (hoveredColumn.value === column.key) classes.push('cell-hovered')
  if (resizingColumn.value === column.key) classes.push('cell-resizing')

  // 拖拽相关样式 - 特殊列类型根据配置决定是否可拖拽
  const isSpecialColumnDragAllowed = props.dragDropConfig?.allowSpecialColumnDrag ?? false
  const isColumnTypeDisabled = column.type && props.dragDropConfig?.disabledColumnTypes?.includes(column.type)
  
  if (
    column.draggable !== false && 
    props.dragDropConfig?.enabled !== false &&
    (isSpecialColumnDragAllowed || !isColumnTypeDisabled)
  ) {
    classes.push('cell-draggable')
  }

  const sortConfig = getSortConfig(column)
  if (sortConfig) {
    classes.push('cell-sorted')
    classes.push(`cell-sorted-${sortConfig.direction}`)
  }

  return classes
}

const getHeaderCellStyle = (column: TableColumn): Record<string, string> => {
  const style: Record<string, string> = {}

  if (column.width) {
    style['width'] = typeof column.width === 'number' ? `${column.width}px` : column.width
    style['minWidth'] = style['width']
    style['maxWidth'] = style['width']
  }

  if (column.minWidth) {
    style['minWidth'] = `${column.minWidth}px`
  }

  if (column.maxWidth) {
    style['maxWidth'] = `${column.maxWidth}px`
  }

  if (column.align) {
    style['textAlign'] = column.align
  }

  // 添加拖拽相关样式 - 特殊列类型根据配置决定是否可拖拽
  const isSpecialColumnDragAllowed = props.dragDropConfig?.allowSpecialColumnDrag ?? false
  const isColumnTypeDisabled = column.type && props.dragDropConfig?.disabledColumnTypes?.includes(column.type)
  
  if (
    column.draggable !== false && 
    props.dragDropConfig?.enabled !== false &&
    (isSpecialColumnDragAllowed || !isColumnTypeDisabled)
  ) {
    style['cursor'] = isDragging.value && getColumnGroup(column) ? 'grabbing' : 'grab'
  }

  // 使用统一固定列边框管理
  if (column.fixed && props.getFixedColumnBorder) {
    const fixedBorder = props.getFixedColumnBorder(column)
    Object.assign(style, fixedBorder.styles)
    // 表头比单元格高10
    if (style.zIndex) {
      style.zIndex = String(Number(style.zIndex) + 10)
    }
  }

  return style
}

const getSortConfig = (column: TableColumn): SortConfig | undefined => {
  return props.sortConfig?.find(config => config.column === column.key)
}

const getSortIndicatorClasses = (column: TableColumn): string[] => {
  const classes = ['indicator']
  const sortConfig = getSortConfig(column)

  if (sortConfig) {
    classes.push('indicator-active')
    classes.push(`indicator-${sortConfig.direction}`)

    // Add priority class if there are multiple sorts
    if (props.sortConfig && props.sortConfig.length > 1) {
      classes.push('indicator-multi-sort')
    }
  }

  return classes
}

const getSortPriority = (column: TableColumn): number | null => {
  const sortConfig = getSortConfig(column)
  if (!sortConfig || !props.sortConfig || props.sortConfig.length <= 1) {
    return null
  }
  return sortConfig.priority ?? null
}

const getSortIcon = (column: TableColumn): string => {
  const sortConfig = getSortConfig(column)
  if (!sortConfig) {
    return 'sort-both' // 未排序时显示双向箭头
  }

  return sortConfig.direction === 'asc' ? 'sort-asc' : 'sort-desc'
}

const handleSelectAll = (event: Event): void => {
  if (!props.selectionConfig?.enabled || props.selectionConfig.mode === 'single') return

  const target = event.target as HTMLInputElement
  const selected = target.checked
  emit('selectAll', selected, event)
}

const handleHeaderClick = (column: TableColumn, event?: MouseEvent): void => {
  // Skip click handling if dragging is in progress
  if (isDragging.value) return

  emit('headerClick', column, event || new MouseEvent('click'))

  if (column.sortable) {
    const currentSort = getSortConfig(column)
    let newDirection: 'asc' | 'desc' | null = 'asc'

    if (currentSort) {
      if (currentSort.direction === 'asc') {
        newDirection = 'desc'
      } else if (currentSort.direction === 'desc') {
        newDirection = null // Clear sort
      }
    }

    emit('sort', column, newDirection)
  }
}

const handleHeaderMouseDown = (column: TableColumn, event: MouseEvent): void => {
  // Skip drag detection if dragging is disabled or if clicking on resizer
  if (props.dragDropConfig?.enabled === false || column.draggable === false) return

  // 禁止拖拽特殊列类型（根据配置决定）
  if (!props.dragDropConfig?.allowSpecialColumnDrag && column.type && 
      props.dragDropConfig?.disabledColumnTypes?.includes(column.type)) {
    return
  }

  const target = event.target as HTMLElement
  if (target.closest('.column-resizer')) return

  // Start drag detection
  startDragDetection(event, column)
}

const handleHeaderMouseEnter = (column: TableColumn): void => {
  hoveredColumn.value = column.key
}

const handleHeaderMouseLeave = (column: TableColumn): void => {
  if (hoveredColumn.value === column.key) {
    hoveredColumn.value = null
  }
}

const handleResizerMouseDown = (event: MouseEvent, column: TableColumn): void => {
  event.preventDefault()
  event.stopPropagation()

  resizingColumn.value = column.key
  resizeStartX.value = event.clientX

  // Get current column width
  const headerCell = (event.target as HTMLElement).closest('.header-cell') as HTMLElement
  if (headerCell) {
    resizeStartWidth.value = headerCell.offsetWidth
  }

  // Add global mouse event listeners
  document.addEventListener('mousemove', handleResizerMouseMove)
  document.addEventListener('mouseup', handleResizerMouseUp)

  // Prevent text selection during resize and add visual feedback
  document.body.style.userSelect = 'none'
  document.body.style.cursor = 'col-resize'
  document.body.classList.add('resizing-column')

  // Add a temporary overlay to prevent interference with other elements
  const overlay = document.createElement('div')
  overlay.id = 'resize-overlay'
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    cursor: col-resize;
    background: transparent;
  `
  document.body.appendChild(overlay)
}

const handleResizerMouseMove = (event: MouseEvent): void => {
  if (!resizingColumn.value) return

  const column = props.columns.find(col => col.key === resizingColumn.value)
  if (!column) return

  let deltaX = event.clientX - resizeStartX.value

  // 右固定列使用反向拖拽逻辑：向左拖拽增加宽度
  if (column.fixed === 'right') {
    deltaX = -deltaX
  }
  const newWidth = Math.max(50, resizeStartWidth.value + deltaX) // 最小宽度50px

  // 应用最小/最大宽度约束
  let constrainedWidth = newWidth
  if (column.minWidth && constrainedWidth < column.minWidth) {
    constrainedWidth = column.minWidth
  }
  if (column.maxWidth && constrainedWidth > column.maxWidth) {
    constrainedWidth = column.maxWidth
  }

  // 实时更新列宽（提供即时视觉反馈）
  const headerCell = document.querySelector(
    `[data-testid="header-cell-${column.key}"]`
  ) as HTMLElement
  if (headerCell) {
    headerCell.style.width = `${constrainedWidth}px`
    headerCell.style.minWidth = `${constrainedWidth}px`
    headerCell.style.maxWidth = `${constrainedWidth}px`
  }

  // 显示宽度提示（可选）
  showResizeTooltip(event.clientX, event.clientY, constrainedWidth)

  emit('resize', column, constrainedWidth)
}

// 显示拖拽时的宽度提示
const showResizeTooltip = (x: number, y: number, width: number): void => {
  let tooltip = document.getElementById('resize-tooltip')
  if (!tooltip) {
    tooltip = document.createElement('div')
    tooltip.id = 'resize-tooltip'
    tooltip.style.cssText = `
      position: fixed;
      background: var(--table-tooltip-bg, rgba(0, 0, 0, 0.8));
      color: var(--table-tooltip-text, white);
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      z-index: 10000;
      pointer-events: none;
      transition: opacity 0.2s;
      white-space: nowrap;
    `
    document.body.appendChild(tooltip)
  }

  tooltip.textContent = `${width}px`
  tooltip.style.left = `${x + 10}px`
  tooltip.style.top = `${y - 30}px`
  tooltip.style.opacity = '1'
}

const handleResizerMouseUp = (): void => {
  resizingColumn.value = null
  resizeStartX.value = 0
  resizeStartWidth.value = 0

  // Remove global mouse event listeners
  document.removeEventListener('mousemove', handleResizerMouseMove)
  document.removeEventListener('mouseup', handleResizerMouseUp)

  // Restore text selection and cursor
  document.body.style.userSelect = ''
  document.body.style.cursor = ''
  document.body.classList.remove('resizing-column')

  // Remove the temporary overlay
  const overlay = document.getElementById('resize-overlay')
  if (overlay) {
    document.body.removeChild(overlay)
  }

  // Remove the resize tooltip
  const tooltip = document.getElementById('resize-tooltip')
  if (tooltip) {
    tooltip.style.opacity = '0'
    setTimeout(() => {
      if (tooltip.parentNode) {
        document.body.removeChild(tooltip)
      }
    }, 200)
  }
}

// Expose methods for parent components
defineExpose({
  calculateColumnWidths
})
</script>

<style scoped>
@reference "../../styles/base.css";

.table-header {
  @apply bg-table-header-bg;
}

.header-sticky {
  @apply sticky top-0 z-20;
}

.header-resizing {
  @apply select-none;
}

.header-row {
  @apply flex;
}

/* Header cell styles */
.header-cell {
  @apply px-4 py-3 text-table-text;
  @apply last:border-r-0 flex-shrink-0 transition-colors;
  @apply relative flex items-center font-semibold;
  @apply bg-table-header-bg;
  overflow: hidden;
}

.header-cell.cell-sortable {
  @apply cursor-pointer hover:bg-table-row-hover-bg;
}

.header-cell.cell-hovered {
  @apply bg-table-row-hover-bg;
}

.header-cell.cell-resizing {
  @apply bg-table-row-hover-bg;
}

.header-cell.cell-sorted {
  @apply bg-table-primary/10;
}

.header-cell.cell-align-center {
  @apply text-center justify-center;
}

.header-cell.cell-align-right {
  @apply text-right justify-end;
}

/* 序号列表头样式 - 水平和垂直居中 */
.header-cell.cell-seq {
  @apply flex items-center justify-center text-center;
  min-width: 60px;
  /* background-color: var(--table-header-bg) !important; */
}

.header-cell.cell-seq .header-cell-content {
  @apply flex items-center justify-center w-full;
}

.header-cell.cell-seq .header-title {
  @apply flex items-center justify-center w-full gap-0;
}

.header-cell.cell-seq .title-text {
  @apply text-center block;
}

/* 选择列表头样式 - 水平和垂直居中 */
.header-cell.cell-selection {
  @apply justify-center items-center text-center;
  /* background-color: var(--table-header-bg) !important; */
}

.header-cell.cell-selection .header-cell-content {
  @apply flex items-center justify-center w-full;
}

.selection-header {
  @apply flex items-center justify-center w-full;
}

.selection-header-single {
  @apply text-center;
}

.selection-title {
  @apply font-semibold text-table-text;
}

.selection-checkbox {
  @apply w-4 h-4 rounded transition-all duration-200 cursor-pointer;
  @apply checked:bg-table-primary checked:border-table-primary;
  @apply focus:ring-2 focus:ring-table-primary/20 focus:ring-offset-1;
  @apply hover:border-table-primary/60;
  border: 2px solid var(--table-border-color);
  accent-color: var(--table-primary);
}

.selection-checkbox:indeterminate {
  @apply bg-table-primary border-table-primary;
}

.header-cell.cell-fixed-left {
  position: sticky;
  z-index: 20;
}

.header-cell.cell-fixed-right {
  position: sticky;
  z-index: 20;
}

/* z-index通过JavaScript动态计算和设置 */

/* 悬停状态下的固定表头样式 */
.header-cell.cell-fixed-left:hover,
.header-cell.cell-fixed-right:hover {
  @apply bg-table-row-hover-bg;
  border-bottom: var(--table-border);
}

/* 排序状态下的固定表头样式 */
.header-cell.cell-fixed-left.cell-sorted,
.header-cell.cell-fixed-right.cell-sorted {
  background-color: color-mix(in srgb, var(--table-primary) 10%, var(--table-header-bg));
  border-bottom: var(--table-border);
}

/* Header cell content */
.header-cell-content {
  @apply flex items-center w-full;
}

.header-title {
  @apply flex items-center gap-2 w-full;
}

.title-text {
  @apply truncate;
}

/* Sort indicator styles */
.sort-indicator {
  @apply relative flex items-center justify-center ml-1 opacity-30 transition-opacity;
}

.sort-indicator.indicator-active {
  @apply opacity-100;
}

.sort-indicator.indicator-multi-sort {
  @apply w-5; /* Slightly wider to accommodate priority number */
}

.sort-icon {
  @apply text-current transition-colors;
}

.sort-indicator.indicator-active .sort-icon {
  @apply text-table-primary;
}

.cell-sortable:hover .sort-indicator {
  @apply opacity-60;
}

/* Sort priority indicator */
.sort-priority {
  @apply absolute -top-1 -right-1 w-5 h-5 bg-table-primary text-white text-xs;
  @apply rounded-full flex items-center justify-center font-bold leading-none;
  font-size: 8px;
}

/* Column resizer styles */
.column-resizer {
  @apply absolute right-0 top-0 bottom-0 w-2 cursor-col-resize;
  @apply flex items-center justify-center opacity-0;
  @apply transition-all duration-200 ease-in-out;
  /* 扩大交互区域 */
  margin-right: -4px;
  padding: 0 4px;
}

/* 右固定列的调整器样式 - 显示在列的左侧 */
.header-cell.cell-fixed-right .column-resizer {
  @apply left-0 right-auto;
  /* 扩大交互区域 */
  margin-left: -4px;
  margin-right: 0;
  padding: 0 4px;
}

.column-resizer.resizer-visible {
  @apply opacity-60;
}

.column-resizer:hover {
  @apply opacity-100;
}

.column-resizer.resizer-active {
  @apply opacity-100;
}

.resizer-handle {
  @apply relative flex items-center justify-center h-full;
  @apply transition-all duration-200;
}

.resizer-line {
  @apply w-0.5 h-3/5 bg-table-border;
  @apply transition-all duration-200 ease-in-out;
  border-radius: 1px;
}

.column-resizer:hover .resizer-line {
  @apply bg-table-primary h-4/5 w-1;
  box-shadow: 0 0 4px rgba(var(--table-primary-rgb), 0.3);
}

.column-resizer.resizer-active .resizer-line {
  @apply bg-table-primary h-full w-1;
  box-shadow: 0 0 8px rgba(var(--table-primary-rgb), 0.5);
}

/* Resizer grip dots */
.resizer-grip {
  @apply absolute flex flex-col items-center justify-center;
  @apply opacity-0 transition-opacity duration-200;
  gap: 1px;
}

.column-resizer:hover .resizer-grip {
  @apply opacity-100;
}

.grip-dot {
  @apply w-1 h-1 bg-table-primary rounded-full;
  @apply transition-all duration-200;
}

.column-resizer:hover .grip-dot {
  @apply bg-table-primary;
  box-shadow: 0 0 2px rgba(var(--table-primary-rgb), 0.4);
}

/* 拖拽时的全局样式 */
.header-resizing {
  @apply select-none;
}

.header-resizing * {
  cursor: col-resize !important;
}

/* 拖拽时的视觉反馈线 */
.column-resizer.resizer-active::after {
  content: '';
  @apply absolute top-0 left-1/2 w-0.5 bg-table-primary;
  height: 200vh;
  transform: translateX(-50%);
  z-index: 1000;
  box-shadow: 0 0 4px rgba(var(--table-primary-rgb), 0.3);
  pointer-events: none;
}

/* 全局拖拽状态样式 */
:global(body.resizing-column) {
  cursor: col-resize;
  user-select: none;
}

:global(body.resizing-column *) {
  cursor: col-resize;
}

/* 改进的悬停效果 */
.header-cell:hover .column-resizer {
  @apply opacity-60;
}

.header-cell:hover .column-resizer:hover {
  @apply opacity-100;
}

/* 拖拽时的表格样式 */
.table-header.header-resizing {
  @apply select-none;
}

.table-header.header-resizing .header-cell {
  @apply transition-none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-cell {
    @apply px-2 py-2 text-sm;
  }

  .sort-indicator .sort-icon {
    @apply w-3 h-3;
  }

  .column-resizer {
    @apply w-3;
    margin-right: -6px;
    padding: 0 6px;
  }

  /* 响应式右固定列调整器样式 */
  .header-cell.cell-fixed-right .column-resizer {
    margin-left: -6px;
    margin-right: 0;
    padding: 0 6px;
  }

  .resizer-line {
    @apply w-1;
  }

  .grip-dot {
    @apply w-0.5 h-0.5;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .resizer-line {
    @apply border border-current;
  }

  .column-resizer:hover .resizer-line {
    @apply border-2;
  }
}

/* 拖拽排序样式 */
.cell-draggable {
  cursor: grab;
}

.cell-draggable:active {
  cursor: grabbing;
}

.header-cell-dragging {
  @apply opacity-50 transform rotate-1;
  z-index: 1000;
  cursor: grabbing;
}

.drop-zone-valid {
  @apply relative;
}

.drop-zone-valid .header-cell-content {
  opacity: 1;
}

.drop-zone-valid .title-text {
  opacity: 1;
}

.drop-zone-invalid {
  @apply relative;
  background-color: rgba(239, 68, 68, 0.1);
}

.drop-zone-invalid .header-cell-content {
  @apply opacity-60;
}

:global(.column-drag-preview) {
  @apply transition-none;
  animation: dragPulse 0.6s ease-in-out infinite alternate;
}

@keyframes dragPulse {
  from {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  to {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
  }
}

:global(.drop-indicator) {
  animation: dropIndicatorGlow 0.8s ease-in-out infinite alternate;
}

@keyframes dropIndicatorGlow {
  from {
    opacity: 0.8;
    transform: scaleX(1);
  }
  to {
    opacity: 1;
    transform: scaleX(1.1);
  }
}

:global(body.column-dragging) {
  cursor: grabbing;
  user-select: none;
}

:global(body.column-dragging *) {
  cursor: grabbing;
}

/* 拖拽时禁用正在拖拽列的调整器 */
.header-cell-dragging .column-resizer {
  @apply opacity-0;
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .column-resizer,
  .resizer-handle,
  .resizer-line,
  .resizer-grip,
  .grip-dot {
    @apply transition-none;
  }

  /* 拖拽动画也要禁用 */
  .header-cell-dragging,
  :global(.column-drag-preview),
  :global(.drop-indicator) {
    @apply transition-none;
    animation: none;
  }
}
</style>
