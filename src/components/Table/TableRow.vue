<template>
  <div
    class="table-row"
    :class="rowClasses"
    :style="rowStyle"
    @click="handleClick"
    @dblclick="handleDoubleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @contextmenu="handleContextMenu"
  >
    <TableCell
      v-for="column in columns"
      :key="column.key"
      :column="column"
      :row="row"
      :value="getCellValue(column)"
      :index="index"
      :selected="selected"
      :disabled="disabled"
      :editing="editing && isColumnEditable(column)"
      :hovered="hovered"
      :pagination-state="paginationState"
      :selection-config="selectionConfig"
      :selected-rows="selectedRows"
      :border-config="borderConfig"
      :cell-border-classes="cellBorderClasses"
      :get-fixed-column-border="getFixedColumnBorder"
      :get-fixed-cell-stripe="getFixedCellStripe"
      @cell-click="handleCellClick"
      @cell-dblclick="handleCellDoubleClick"
      @cell-edit="handleCellEdit"
      @cell-change="handleCellChange"
      @selection-change="handleSelectionChange"
    >
      <!-- Pass through cell slots -->
      <template #default="cellProps">
        <slot
          :name="`cell-${column.key}`"
          v-bind="cellProps"
        >
          <!-- 如果列有自定义render函数或template模板，或者是特殊列类型，不提供默认内容，让TableCell处理 -->
          <template
            v-if="
              !column.render &&
              !column.template &&
              column.type !== 'seq' &&
              column.type !== 'selection'
            "
          >
            <!-- Default cell content -->
            {{ getCellDisplayValue(column) }}
          </template>
        </slot>
      </template>
    </TableCell>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import TableCell from './TableCell.vue'
import type { TableColumn, TableRow as TableRowType, SelectionConfig, BorderConfig, StripeConfig, HoverMode } from '@/types'

// Props definition
interface Props {
  row: TableRowType
  columns: TableColumn[]
  index: number
  selected?: boolean
  disabled?: boolean
  editing?: boolean
  hoverMode?: HoverMode
  rowHeight?: number | string
  paginationState?: { currentPage: number; pageSize: number; total: number } // 分页信息
  selectionConfig?: SelectionConfig // 选择列配置
  selectedRows?: Set<string | number> // 选中行的 Set，用于响应式更新
  borderConfig?: BorderConfig
  cellBorderClasses?: string[]
  getFixedColumnBorder?: (column: TableColumn) => { classes: string[]; styles: Record<string, string> }
  getFixedCellStripe?: (column: TableColumn, rowIndex: number, selected?: boolean, hover?: boolean, editing?: boolean) => { classes: string[]; styles: Record<string, string> }
  stripeConfig?: StripeConfig
  getRowStripeClasses?: (index: number, selected?: boolean, hover?: boolean, editing?: boolean) => string[]
  getCurrentRowBackground?: (index: number, selected?: boolean, hover?: boolean, editing?: boolean) => string
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  disabled: false,
  editing: false,
  hoverMode: 'none',
  rowHeight: undefined,
  paginationState: undefined, // 添加这行
  selectionConfig: undefined,
  selectedRows: () => new Set(),
  borderConfig: () => ({ type: 'outer' }),
  cellBorderClasses: () => [],
  getFixedColumnBorder: () => (_column: TableColumn) => ({ classes: [], styles: {} }),
  getFixedCellStripe: () => (_column: TableColumn, _rowIndex: number, _selected?: boolean, _hover?: boolean, _editing?: boolean) => ({ classes: [], styles: {} }),
  stripeConfig: () => ({ type: 'default', enabled: false }),
  getRowStripeClasses: () => (_index: number, _selected?: boolean, _hover?: boolean, _editing?: boolean) => [],
  getCurrentRowBackground: () => (_index: number, _selected?: boolean, _hover?: boolean, _editing?: boolean) => 'var(--table-bg)'
})

// Emits definition
interface Emits {
  click: [event: MouseEvent]
  dblclick: [event: MouseEvent]
  mouseenter: [event: MouseEvent]
  mouseleave: [event: MouseEvent]
  contextmenu: [event: MouseEvent]
  cellClick: [column: TableColumn, value: unknown, event: MouseEvent]
  cellDblclick: [column: TableColumn, value: unknown, event: MouseEvent]
  cellEdit: [column: TableColumn, value: unknown]
  cellChange: [column: TableColumn, oldValue: unknown, newValue: unknown]
  selectionChange: [row: TableRowType, selected: boolean, event: Event]
}

const emit = defineEmits<Emits>()

// Reactive state
const hovered = ref(false)

// Computed properties
const rowClasses = computed(() => {
  const classes = ['row']

  if (props.selected) classes.push('row-selected')
  if (props.disabled) classes.push('row-disabled')
  if (props.editing) classes.push('row-editing')
  if (props.hoverMode === 'row' && !props.disabled) classes.push('row-hover')
  if (props.hoverMode === 'cell' && !props.disabled) classes.push('row-cell-hover')

  // Add unified stripe classes
  if (props.getRowStripeClasses) {
    const stripeClasses = props.getRowStripeClasses(
      props.index,
      props.selected,
      hovered.value, // pass actual hover state
      props.editing
    )
    if (Array.isArray(stripeClasses)) {
      classes.push(...stripeClasses)
    }
  }

  return classes
})

const rowStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.rowHeight) {
    style['height'] = typeof props.rowHeight === 'number' ? `${props.rowHeight}px` : props.rowHeight
    style['minHeight'] = style['height']
  }


  return style
})

// Methods
const getCellValue = (column: TableColumn): unknown => {
  // 序号列不从数据中取值，而是在 TableCell 中计算
  if (column.type === 'seq') {
    return null // 序号在 TableCell 中计算
  }
  return props.row[column.key]
}

const getCellDisplayValue = (column: TableColumn): string => {
  const value = getCellValue(column)

  if (value === null || value === undefined) {
    return ''
  }

  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  if (typeof value === 'object') {
    return JSON.stringify(value)
  }

  return String(value)
}

const isColumnEditable = (column: TableColumn): boolean => {
  return column.editable === true
}

const handleClick = (event: MouseEvent): void => {
  if (props.disabled) return
  emit('click', event)
}

const handleDoubleClick = (event: MouseEvent): void => {
  if (props.disabled) return
  emit('dblclick', event)
}

const handleMouseEnter = (event: MouseEvent): void => {
  hovered.value = true
  emit('mouseenter', event)
}

const handleMouseLeave = (event: MouseEvent): void => {
  hovered.value = false
  emit('mouseleave', event)
}

const handleContextMenu = (event: MouseEvent): void => {
  if (props.disabled) return
  emit('contextmenu', event)
}

const handleCellClick = (column: TableColumn, value: unknown, event: MouseEvent): void => {
  emit('cellClick', column, value, event)
}

const handleCellDoubleClick = (column: TableColumn, value: unknown, event: MouseEvent): void => {
  emit('cellDblclick', column, value, event)
}

const handleCellEdit = (column: TableColumn, value: unknown): void => {
  emit('cellEdit', column, value)
}

const handleCellChange = (column: TableColumn, oldValue: unknown, newValue: unknown): void => {
  emit('cellChange', column, oldValue, newValue)
}

const handleSelectionChange = (row: TableRowType, selected: boolean, event: Event): void => {
  emit('selectionChange', row, selected, event)
}
</script>

<style scoped>
@reference "../../styles/base.css";

.table-row {
  display: flex;
  transition: all 150ms ease-in-out;
}

/* 行级状态样式现在通过统一的单元格样式系统管理 */
.row-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Focus styles for keyboard navigation */
.table-row:focus {
  @apply outline-none ring-2 ring-table-primary ring-offset-2 ring-offset-table-bg;
}

.table-row:focus-visible {
  @apply outline-none ring-2 ring-table-primary ring-offset-2 ring-offset-table-bg;
}

/* 独立的行悬停样式 - 不依赖 stripe 系统 */
/* 行级悬停模式 */
.table-row.row-hover:hover {
  background-color: var(--table-row-hover-bg) !important;
}

/* 单元格悬停模式 - 行级别不添加背景，由单元格自行处理 */
.table-row.row-cell-hover {
  /* 行级别不设置悬停背景，由单元格级别处理 */
}

/* Responsive design */
@media (max-width: 768px) {
  .table-row {
    @apply text-sm;
  }
}
</style>
