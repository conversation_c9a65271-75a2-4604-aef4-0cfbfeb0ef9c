<template>
  <div class="h-full bg-gradient-to-br from-blue-50 to-indigo-100 p-4 sm:p-8 overflow-auto">
    <div class="max-w-6xl mx-auto space-y-4 sm:space-y-8">
      <!-- Header -->
      <header class="text-center space-y-4">
        <h1
          class="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
        >
          Tailwind CSS 4 验证
        </h1>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          验证 Tailwind CSS 4 的最新功能和最佳实践配置
        </p>
      </header>

      <!-- Theme Switcher -->
      <div class="flex justify-center">
        <div class="bg-white rounded-lg shadow-lg p-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">主题切换</label>
          <select
            v-model="currentTheme"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            @change="changeTheme"
          >
            <option value="default">默认主题</option>
            <option value="dark">深色主题</option>
            <option value="enterprise">企业主题</option>
          </select>
        </div>
      </div>

      <!-- Custom Colors Test -->
      <section class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">自定义颜色测试</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <div class="text-center">
            <div class="w-16 h-16 bg-table-primary rounded-lg mx-auto mb-2 shadow-md" />
            <span class="text-sm text-gray-600">Primary</span>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-table-secondary rounded-lg mx-auto mb-2 shadow-md" />
            <span class="text-sm text-gray-600">Secondary</span>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-table-accent rounded-lg mx-auto mb-2 shadow-md" />
            <span class="text-sm text-gray-600">Accent</span>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-table-success rounded-lg mx-auto mb-2 shadow-md" />
            <span class="text-sm text-gray-600">Success</span>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-table-warning rounded-lg mx-auto mb-2 shadow-md" />
            <span class="text-sm text-gray-600">Warning</span>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-table-error rounded-lg mx-auto mb-2 shadow-md" />
            <span class="text-sm text-gray-600">Error</span>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-table-info rounded-lg mx-auto mb-2 shadow-md" />
            <span class="text-sm text-gray-600">Info</span>
          </div>
        </div>
      </section>

      <!-- Responsive Design Test -->
      <section class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">响应式设计测试</h2>
        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 3xl:grid-cols-5 4xl:grid-cols-6 gap-4"
        >
          <div
            v-for="i in 12"
            :key="i"
            class="bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg p-4 text-white text-center"
          >
            <div class="text-lg font-semibold">Card {{ i }}</div>
            <div class="text-sm opacity-90">响应式卡片</div>
          </div>
        </div>
        <div class="mt-4 text-sm text-gray-600">
          <p>断点测试: 1列(默认) → 2列(md) → 3列(lg) → 4列(xl) → 5列(3xl) → 6列(4xl)</p>
        </div>
      </section>

      <!-- Custom Spacing Test -->
      <section class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">自定义间距测试</h2>
        <div class="space-y-4">
          <div class="flex items-center space-x-4">
            <div class="w-4 h-4 bg-blue-500 rounded" />
            <span class="text-sm">table-xs spacing (0.25rem)</span>
          </div>
          <div class="flex items-center space-x-table-sm">
            <div class="w-4 h-4 bg-green-500 rounded" />
            <span class="text-sm">table-sm spacing (0.5rem)</span>
          </div>
          <div class="flex items-center space-x-table-md">
            <div class="w-4 h-4 bg-yellow-500 rounded" />
            <span class="text-sm">table-md spacing (1rem)</span>
          </div>
          <div class="flex items-center space-x-table-lg">
            <div class="w-4 h-4 bg-red-500 rounded" />
            <span class="text-sm">table-lg spacing (1.5rem)</span>
          </div>
          <div class="flex items-center space-x-table-xl">
            <div class="w-4 h-4 bg-purple-500 rounded" />
            <span class="text-sm">table-xl spacing (2rem)</span>
          </div>
        </div>
      </section>

      <!-- Animation Test -->
      <section class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">动画测试</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 animate-bounce" />
            <span class="text-sm text-gray-600">Bounce Animation</span>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-green-500 rounded-full mx-auto mb-4 animate-pulse" />
            <span class="text-sm text-gray-600">Pulse Animation</span>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-purple-500 rounded-full mx-auto mb-4 animate-spin" />
            <span class="text-sm text-gray-600">Spin Animation</span>
          </div>
        </div>
      </section>

      <!-- Interactive Elements Test -->
      <section class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">交互元素测试</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <button
            class="bg-table-primary hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-table-fast shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Primary Button
          </button>
          <button
            class="bg-table-secondary hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-table-normal shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Secondary Button
          </button>
          <button
            class="bg-table-accent hover:bg-cyan-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-table-slow shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Accent Button
          </button>
        </div>
      </section>

      <!-- Typography Test -->
      <section class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">字体测试</h2>
        <div class="space-y-4">
          <div class="font-table">
            <h3 class="text-xl font-bold text-table-text">Table Font Family</h3>
            <p class="text-table-text-secondary">这是使用自定义 table 字体族的文本</p>
          </div>
          <div class="font-mono">
            <h3 class="text-xl font-bold text-table-text">Monospace Font</h3>
            <p class="text-table-text-secondary">
              这是使用等宽字体的代码文本: console.log('Hello World')
            </p>
          </div>
        </div>
      </section>

      <!-- Shadow Test -->
      <section class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">阴影测试</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="bg-white p-6 rounded-lg shadow-table-sm">
            <h3 class="font-semibold mb-2">Small Shadow</h3>
            <p class="text-gray-600">使用 shadow-table-sm</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-table">
            <h3 class="font-semibold mb-2">Default Shadow</h3>
            <p class="text-gray-600">使用 shadow-table</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-table-lg">
            <h3 class="font-semibold mb-2">Large Shadow</h3>
            <p class="text-gray-600">使用 shadow-table-lg</p>
          </div>
        </div>
      </section>

      <!-- Status Indicator -->
      <div class="text-center">
        <div class="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full">
          <Icon
            icon="lucide:check-circle"
            class="w-5 h-5 mr-2"
            color="current"
          />
          Tailwind CSS 4 配置验证成功
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Icon } from '../components/Icon'

const currentTheme = ref('default')

const changeTheme = () => {
  document.documentElement.setAttribute('data-theme', currentTheme.value)
}

onMounted(() => {
  // 初始化主题
  changeTheme()
})
</script>
