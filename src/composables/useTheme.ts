import { ref, computed, onMounted } from 'vue'
import type { ThemeName, ThemeContext } from '../types/theme'

// Global theme state
const currentTheme = ref<ThemeName | string>('default')
const transitionsEnabled = ref(true)
const customVars = ref<Record<string, string>>({})

// Theme storage key
const THEME_STORAGE_KEY = 'vue-table-theme'

/**
 * Theme composable for managing theme state and operations
 */
export function useTheme(): ThemeContext {
  // Load theme from localStorage on initialization
  const loadStoredTheme = () => {
    try {
      const stored = localStorage.getItem(THEME_STORAGE_KEY)
      if (stored) {
        currentTheme.value = stored
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error)
    }
  }

  // Save theme to localStorage
  const saveTheme = () => {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, currentTheme.value)
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error)
    }
  }

  // Set theme
  const setTheme = (theme: ThemeName | string) => {
    const previousTheme = currentTheme.value
    currentTheme.value = theme

    // Apply theme to document
    const root = document.documentElement
    root.setAttribute('data-theme', theme)

    // Save to localStorage
    saveTheme()

    // Emit theme change event only if theme actually changed
    if (previousTheme !== theme) {
      const event = new CustomEvent('theme-change', {
        detail: { theme, previousTheme }
      })
      document.dispatchEvent(event)
    }
  }

  // Toggle between light and dark theme
  const toggleDarkMode = () => {
    const isDark = currentTheme.value === 'dark'
    setTheme(isDark ? 'default' : 'dark')
  }

  // Check if current theme is dark
  const isDarkTheme = computed(() => {
    return currentTheme.value === 'dark'
  })

  // Enable/disable transitions
  const setTransitions = (enabled: boolean) => {
    transitionsEnabled.value = enabled
    const root = document.documentElement
    if (enabled) {
      root.classList.remove('theme-transitions-disabled')
    } else {
      root.classList.add('theme-transitions-disabled')
    }
  }

  // Set custom CSS variables
  const setCustomVars = (vars: Record<string, string>) => {
    customVars.value = { ...vars }
    const root = document.documentElement
    
    // Apply custom variables to document root
    Object.entries(vars).forEach(([key, value]) => {
      if (key.startsWith('--')) {
        root.style.setProperty(key, value)
      }
    })
  }

  // Initialize theme on mount
  onMounted(() => {
    loadStoredTheme()
    setTheme(currentTheme.value)
  })

  return {
    currentTheme: computed(() => currentTheme.value),
    customVars: computed(() => customVars.value),
    transitionsEnabled: computed(() => transitionsEnabled.value),
    isDarkTheme,
    setTheme,
    setCustomVars,
    toggleDarkMode,
    setTransitions
  }
}

// Global theme instance for sharing across components
let globalThemeInstance: ThemeContext | null = null

/**
 * Get or create global theme instance
 */
export function useGlobalTheme(): ThemeContext {
  if (!globalThemeInstance) {
    globalThemeInstance = useTheme()
  }
  return globalThemeInstance
}