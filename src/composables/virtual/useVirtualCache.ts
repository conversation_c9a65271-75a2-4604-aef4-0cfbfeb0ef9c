// Virtual scrolling cache management
import { performance } from '@/utils/performance'

/**
 * 预测性样式缓存类
 * 基于滚动方向预加载样式，优化滚动性能
 */
export class PredictiveStyleCache {
  private cache = new Map<
    string,
    {
      style: Record<string, string>
      lastAccessed: number
      accessCount: number
    }
  >()

  private maxSize = 150
  private scrollDirection: 'up' | 'down' | 'left' | 'right' | null = null
  private lastVisibleStart = 0

  get(key: string): Record<string, string> | undefined {
    const item = this.cache.get(key)
    if (item) {
      item.lastAccessed = performance.now()
      item.accessCount++
      return item.style
    }
    return undefined
  }

  set(key: string, style: Record<string, string>) {
    // 如果缓存已满，清理最少使用的项目
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed()
    }

    this.cache.set(key, {
      style: { ...style },
      lastAccessed: performance.now(),
      accessCount: 1
    })
  }

  updateScrollDirection(
    currentVisibleStart: number,
    direction: 'vertical' | 'horizontal' = 'vertical'
  ) {
    if (direction === 'vertical') {
      if (currentVisibleStart > this.lastVisibleStart) {
        this.scrollDirection = 'down'
      } else if (currentVisibleStart < this.lastVisibleStart) {
        this.scrollDirection = 'up'
      }
    } else {
      if (currentVisibleStart > this.lastVisibleStart) {
        this.scrollDirection = 'right'
      } else if (currentVisibleStart < this.lastVisibleStart) {
        this.scrollDirection = 'left'
      }
    }
    this.lastVisibleStart = currentVisibleStart
  }

  /**
   * 预加载样式（纵向和横向通用）
   */
  preloadStyles(
    currentStart: number,
    itemSize: number,
    count: number = 5,
    direction: 'vertical' | 'horizontal' = 'vertical'
  ) {
    const scrollDirection = this.scrollDirection
    if (!scrollDirection) return

    const directionMultiplier = scrollDirection === 'down' || scrollDirection === 'right' ? 1 : -1

    for (let i = 1; i <= count; i++) {
      const predictedStart = currentStart + directionMultiplier * i
      if (predictedStart >= 0) {
        const key = `${predictedStart}-${itemSize}-${direction}`
        if (!this.cache.has(key)) {
          const offset = predictedStart * itemSize
          const style =
            direction === 'vertical'
              ? {
                  transform: `translateY(${offset}px)`,
                  position: 'relative' as const
                }
              : {
                  transform: `translateX(${offset}px)`,
                  position: 'relative' as const
                }
          this.set(key, style)
        }
      }
    }
  }

  private evictLeastUsed() {
    let leastUsedKey: string | undefined
    let leastUsedScore = Infinity
    const now = performance.now()

    for (const [key, item] of this.cache.entries()) {
      // 计算使用分数：考虑访问次数和最近访问时间
      const timeSinceAccess = now - item.lastAccessed
      const score = item.accessCount / (1 + timeSinceAccess / 1000) // 时间权重

      if (score < leastUsedScore) {
        leastUsedScore = score
        leastUsedKey = key
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey)
    }
  }

  clear() {
    this.cache.clear()
    this.scrollDirection = null
    this.lastVisibleStart = 0
  }

  size(): number {
    return this.cache.size
  }

  getHitRate(): number {
    let totalAccess = 0
    let hitCount = 0

    for (const item of this.cache.values()) {
      totalAccess += item.accessCount
      if (item.accessCount > 1) {
        hitCount += item.accessCount - 1
      }
    }

    return totalAccess > 0 ? hitCount / totalAccess : 0
  }
}

/**
 * 高度/宽度缓存管理器
 */
export class DimensionCache {
  private cache = new Map<number, number>()
  private maxSize = 1000

  get(index: number): number | undefined {
    return this.cache.get(index)
  }

  set(index: number, size: number) {
    if (this.cache.size >= this.maxSize) {
      // 删除最老的缓存项
      const firstKey = this.cache.keys().next().value
      if (firstKey !== undefined) {
        this.cache.delete(firstKey)
      }
    }
    this.cache.set(index, size)
  }

  clear() {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }

  has(index: number): boolean {
    return this.cache.has(index)
  }

  /**
   * 获取指定范围内的平均尺寸
   */
  getAverageSize(start: number, end: number): number {
    let total = 0
    let count = 0

    for (let i = start; i <= end; i++) {
      const size = this.cache.get(i)
      if (size !== undefined) {
        total += size
        count++
      }
    }

    return count > 0 ? total / count : 0
  }
}
