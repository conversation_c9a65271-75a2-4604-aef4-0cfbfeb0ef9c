// Vertical virtual scrolling for table rows (optimized version)
import { ref, computed, watch, onMounted, onUnmounted, nextTick, type Ref } from 'vue'
import type { TableRow, VirtualConfig, VirtualState } from '@/types'
import { PredictiveStyleCache, DimensionCache } from './useVirtualCache'
import { useVirtualMetrics } from './useVirtualMetrics'

// 默认纵向虚拟滚动配置 - 参考 vxetable 的默认值
interface InternalVerticalConfig {
  enabled: boolean
  threshold: number
  itemHeight: number | 'auto'
  bufferSize: number
  overscan: number
  adaptive: boolean
}

const DEFAULT_VERTICAL_CONFIG: InternalVerticalConfig = {
  enabled: true,
  threshold: 40, // 降低阈值以更早启用虚拟滚动
  itemHeight: 48,
  bufferSize: 10,
  overscan: 5,
  adaptive: true // 自适应启用
}

export interface UseVerticalVirtualOptions {
  containerRef: Ref<HTMLElement | null>
  data: Ref<TableRow[]>
  config: Ref<VirtualConfig | undefined>
}

export interface UseVerticalVirtualReturn {
  // State
  verticalVirtualState: Ref<VirtualState>
  isVerticalVirtualEnabled: Ref<boolean>

  // Computed
  visibleData: Ref<TableRow[]>
  verticalContainerStyle: Ref<Record<string, string | undefined>>
  verticalScrollAreaStyle: Ref<Record<string, string | undefined>>

  // Methods
  scrollToIndex: (index: number) => void
  scrollToTop: () => void
  updateVerticalVisibleRange: () => void
  getItemHeight: (index: number) => number

  // Performance monitoring
  getVerticalMetrics: () => any
}

/**
 * 纵向虚拟滚动 composable (优化版本)
 * 优化大数据集的纵向滚动性能
 */
export function useVerticalVirtual({
  containerRef,
  data,
  config
}: UseVerticalVirtualOptions): UseVerticalVirtualReturn {
  // 纵向虚拟滚动状态
  const verticalVirtualState = ref<VirtualState>({
    scrollTop: 0,
    visibleStart: 0,
    visibleEnd: 0,
    totalHeight: 0,
    containerHeight: 0
  })

  // 缓存和性能监控
  const verticalStyleCache = new PredictiveStyleCache()
  const heightCache = new DimensionCache()
  const {
    metrics,
    updateMetrics,
    recordUpdate,
    updateFPS,
    throttleScroll,
    getScrollVelocity,
    cleanup
  } = useVirtualMetrics()

  // 增量更新状态缓存 - 优化性能
  const incrementalState = {
    lastScrollTop: 0,
    lastVisibleStart: 0,
    lastVisibleEnd: 0,
    lastContainerHeight: 0,
    lastItemHeight: 0,
    updateCount: 0
  }

  // 检查是否启用纵向虚拟滚动 - 参考 vxetable 的智能启用逻辑
  const isVerticalVirtualEnabled = computed(() => {
    const cfg = config.value
    if (!cfg) return false

    // 如果显式禁用，直接返回 false
    if (cfg.enabled === false) return false

    const threshold = cfg.threshold || DEFAULT_VERTICAL_CONFIG.threshold
    const dataLength = data.value.length

    // 自适应模式：类似 vxetable 的 gt 机制
    if (cfg.adaptive !== false) {
      return dataLength >= threshold
    }

    // 非自适应模式：需要显式启用且满足阈值
    return cfg.enabled === true && dataLength >= threshold
  })

  // 获取合并配置
  const mergedVerticalConfig = computed(() => ({
    ...DEFAULT_VERTICAL_CONFIG,
    ...config.value
  }))

  // 增强的行高计算 - 支持DOM测量和缓存
  const getItemHeight = (index: number): number => {
    const cfg = mergedVerticalConfig.value

    if (cfg.itemHeight === 'auto') {
      // 从缓存中获取高度
      if (heightCache.has(index)) {
        return heightCache.get(index)!
      }

      // 尝试从实际DOM中测量高度
      if (containerRef.value && data.value[index]) {
        const rows = containerRef.value.querySelectorAll('tbody tr')
        if (rows.length > 0) {
          // 找到对应的行并测量高度
          const relativeIndex = index - verticalVirtualState.value.visibleStart
          if (relativeIndex >= 0 && relativeIndex < rows.length) {
            const row = rows[relativeIndex] as HTMLElement
            const height = row.getBoundingClientRect().height
            if (height > 0) {
              heightCache.set(index, height)
              return height
            }
          }

          // 如果找不到具体行，使用第一个可见行的高度作为估算
          const firstRow = rows[0] as HTMLElement
          const estimatedHeight = firstRow.getBoundingClientRect().height
          if (estimatedHeight > 0) {
            return estimatedHeight
          }
        }
      }

      // 动态高度模式的智能估算
      const baseHeight = 48
      const dataItem = data.value[index]
      if (dataItem && typeof dataItem === 'object') {
        // 根据数据内容估算高度（简单的启发式方法）
        const contentLength = Object.values(dataItem).join(' ').length

        // 内容越多，预估高度越高
        const estimatedExtraHeight = Math.min(Math.floor(contentLength / 100) * 12, 48)
        const estimatedHeight = baseHeight + estimatedExtraHeight

        // 缓存估算高度
        heightCache.set(index, estimatedHeight)
        return estimatedHeight
      }

      return baseHeight // 最后的回退高度
    }

    return typeof cfg.itemHeight === 'number' ? cfg.itemHeight : 48
  }

  // 计算总高度
  const totalHeight = computed(() => {
    if (!isVerticalVirtualEnabled.value) return 0

    const cfg = mergedVerticalConfig.value
    if (cfg.itemHeight === 'auto') {
      // 使用平均高度估算
      const sampleSize = Math.min(data.value.length, 10)
      let avgHeight = 48

      if (heightCache.size() > 0) {
        avgHeight =
          heightCache.getAverageSize(0, Math.min(sampleSize - 1, heightCache.size() - 1)) || 48
      }

      return data.value.length * avgHeight
    }

    return data.value.length * (cfg.itemHeight as number)
  })

  // 动态计算缓冲区大小 - 根据滚动速度调整
  const getDynamicBufferSize = (baseBufferSize: number, scrollVelocity: number): number => {
    // 根据滚动速度动态调整缓冲区
    if (scrollVelocity > 5) {
      // 高速滚动：增加缓冲区防止白屏
      return Math.min(baseBufferSize * 2, 25)
    } else if (scrollVelocity > 2) {
      // 中速滚动：适度增加缓冲区
      return Math.ceil(baseBufferSize * 1.5)
    }
    // 低速或静止：使用基本缓冲区
    return baseBufferSize
  }

  // 更新纵向可见范围 - 优化版本
  const updateVerticalVisibleRange = () => {
    const updateStartTime = performance.now()

    if (!isVerticalVirtualEnabled.value || !containerRef.value) {
      return
    }

    // FPS 监控
    updateFPS('vertical')

    const container = containerRef.value
    const containerHeight = container.clientHeight
    const scrollTop = container.scrollTop
    const cfg = mergedVerticalConfig.value
    const itemHeight = getItemHeight(0) // 假设统一行高

    // 增量更新检测 - 避免不必要的重计算
    const scrollDelta = Math.abs(scrollTop - incrementalState.lastScrollTop)
    const containerHeightChanged = containerHeight !== incrementalState.lastContainerHeight
    const itemHeightChanged = itemHeight !== incrementalState.lastItemHeight

    // 如果变化很小且不是容器尺寸或高度变化，则跳过更新
    if (scrollDelta < itemHeight * 0.1 && !containerHeightChanged && !itemHeightChanged) {
      return
    }

    // 获取当前滚动速度并动态调整缓冲区
    const scrollVelocity = getScrollVelocity('vertical')
    const dynamicBufferSize = getDynamicBufferSize(cfg.bufferSize, scrollVelocity)
    const dynamicOverscan = Math.min(cfg.overscan + Math.floor(scrollVelocity), 15)

    // 计算可见范围
    const visibleStart = Math.max(0, Math.floor(scrollTop / itemHeight) - dynamicBufferSize)
    const visibleCount =
      Math.ceil(containerHeight / itemHeight) + dynamicBufferSize * 2 + dynamicOverscan
    const visibleEnd = Math.min(data.value.length, visibleStart + visibleCount)

    // 更新状态
    verticalVirtualState.value = {
      scrollTop,
      visibleStart,
      visibleEnd,
      totalHeight: totalHeight.value,
      containerHeight
    }

    // 更新增量缓存状态
    incrementalState.lastScrollTop = scrollTop
    incrementalState.lastVisibleStart = visibleStart
    incrementalState.lastVisibleEnd = visibleEnd
    incrementalState.lastContainerHeight = containerHeight
    incrementalState.lastItemHeight = itemHeight
    incrementalState.updateCount++

    // 更新样式缓存滚动方向
    verticalStyleCache.updateScrollDirection(visibleStart, 'vertical')

    // 记录性能指标
    const updateTime = performance.now() - updateStartTime
    recordUpdate(updateTime)
  }

  // 获取可见数据
  const visibleData = computed(() => {
    if (!isVerticalVirtualEnabled.value) {
      return data.value
    }

    const { visibleStart, visibleEnd } = verticalVirtualState.value
    const slicedData = data.value.slice(visibleStart, visibleEnd)

    return slicedData
  })

  // 容器样式
  const verticalContainerStyle = computed(() => {
    if (!isVerticalVirtualEnabled.value) return {}

    return {
      overflow: 'auto',
      position: 'relative',
      // 优化滚动性能
      willChange: 'scroll-position'
    }
  })

  // 滚动区域样式 - 预测性缓存优化
  const verticalScrollAreaStyle = computed(() => {
    if (!isVerticalVirtualEnabled.value) {
      verticalStyleCache.clear()
      return {}
    }

    const { visibleStart } = verticalVirtualState.value
    const itemHeight = getItemHeight(0)
    const offsetY = visibleStart * itemHeight
    const cacheKey = `${visibleStart}-${itemHeight}-vertical`

    let style = verticalStyleCache.get(cacheKey)
    if (!style) {
      style = {
        transform: `translateY(${offsetY}px)`,
        position: 'relative' as const
      }
      verticalStyleCache.set(cacheKey, style)
    }

    // 预加载相邻的样式
    nextTick(() => {
      verticalStyleCache.preloadStyles(visibleStart, itemHeight, 3, 'vertical')
    })

    return style
  })

  // 滚动到指定索引
  const scrollToIndex = (index: number) => {
    if (!containerRef.value || !isVerticalVirtualEnabled.value) return

    const itemHeight = getItemHeight(index)
    const scrollTop = index * itemHeight
    containerRef.value.scrollTop = scrollTop
  }

  // 滚动到顶部
  const scrollToTop = () => {
    if (containerRef.value) {
      containerRef.value.scrollTop = 0
    }
  }

  // 纵向滚动事件处理
  const handleVerticalScroll = () => {
    if (!containerRef.value || !isVerticalVirtualEnabled.value) return

    const scrollPosition = {
      x: containerRef.value.scrollLeft,
      y: containerRef.value.scrollTop
    }

    throttleScroll(scrollPosition, updateVerticalVisibleRange, 'vertical')
  }

  // 获取性能指标
  const getVerticalMetrics = () => {
    const visibleCount =
      verticalVirtualState.value.visibleEnd - verticalVirtualState.value.visibleStart
    const totalCount = data.value.length

    // 如果纵向虚拟滚动未启用，返回基本指标
    if (!isVerticalVirtualEnabled.value) {
      return {
        updateCount: 0,
        averageUpdateTime: 0,
        scrollFPS: 0,
        cacheHitRate: 0,
        visibleItemCount: totalCount,
        bufferEfficiency: 0
      }
    }

    // 更新 FPS
    updateFPS('vertical')

    // 更新指标
    updateMetrics(visibleCount, totalCount, verticalStyleCache.getHitRate())

    return metrics.value
  }

  // 初始化虚拟状态
  const initializeVerticalVirtualState = () => {
    if (!isVerticalVirtualEnabled.value) return

    const cfg = mergedVerticalConfig.value
    const itemHeight = getItemHeight(0)
    const containerHeight = containerRef.value?.clientHeight || 500
    const visibleCount = Math.ceil(containerHeight / itemHeight) + cfg.bufferSize * 2 + cfg.overscan

    verticalVirtualState.value = {
      scrollTop: 0,
      visibleStart: 0,
      visibleEnd: Math.min(data.value.length, visibleCount),
      totalHeight: totalHeight.value,
      containerHeight
    }
  }

  // 监听数据变化
  watch(
    [data, config],
    () => {
      if (isVerticalVirtualEnabled.value && verticalVirtualState.value.visibleEnd === 0) {
        initializeVerticalVirtualState()
      } else {
        updateVerticalVisibleRange()
      }
    },
    { immediate: true }
  )

  // 设置纵向滚动监听器
  onMounted(() => {
    nextTick(() => {
      if (containerRef.value && isVerticalVirtualEnabled.value) {
        containerRef.value.addEventListener('scroll', handleVerticalScroll, { passive: true })
        if (verticalVirtualState.value.visibleEnd === 0) {
          initializeVerticalVirtualState()
        } else {
          updateVerticalVisibleRange()
        }
      }
    })
  })

  // 监听纵向虚拟滚动状态变化
  watch(isVerticalVirtualEnabled, (enabled, wasEnabled) => {
    if (!containerRef.value) return

    if (enabled && !wasEnabled) {
      containerRef.value.addEventListener('scroll', handleVerticalScroll, { passive: true })
      if (verticalVirtualState.value.visibleEnd === 0) {
        initializeVerticalVirtualState()
      } else {
        updateVerticalVisibleRange()
      }
    } else if (!enabled && wasEnabled) {
      containerRef.value.removeEventListener('scroll', handleVerticalScroll)
    }
  })

  // 清理
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleVerticalScroll)
    }
    cleanup()
    verticalStyleCache.clear()
    heightCache.clear()
  })

  return {
    verticalVirtualState,
    isVerticalVirtualEnabled,
    visibleData,
    verticalContainerStyle,
    verticalScrollAreaStyle,
    scrollToIndex,
    scrollToTop,
    updateVerticalVisibleRange,
    getItemHeight,
    getVerticalMetrics
  }
}
