// Horizontal virtual scrolling for table columns
import { ref, computed, watch, onMounted, onUnmounted, nextTick, type Ref } from 'vue'
import type { TableColumn, VirtualConfig, VirtualState } from '@/types'
import { PredictiveStyleCache, DimensionCache } from './useVirtualCache'
import { useVirtualMetrics } from './useVirtualMetrics'

// 默认横向虚拟滚动配置
interface InternalHorizontalConfig {
  enabled: boolean
  threshold: number
  columnWidth: number | 'auto'
  bufferColumns: number
  overscanColumns: number
  enableFixedColumns: boolean
  adaptiveColumnWidth: boolean
}

const DEFAULT_HORIZONTAL_CONFIG: InternalHorizontalConfig = {
  enabled: true,
  threshold: 20, // 列数超过20时启用
  columnWidth: 'auto',
  bufferColumns: 5,
  overscanColumns: 3,
  enableFixedColumns: true,
  adaptiveColumnWidth: false
}

export interface UseHorizontalVirtualOptions {
  containerRef: Ref<HTMLElement | null>
  columns: Ref<TableColumn[]>
  config: Ref<VirtualConfig | undefined>
}

export interface UseHorizontalVirtualReturn {
  // State
  horizontalVirtualState: Ref<VirtualState>
  isHorizontalVirtualEnabled: Ref<boolean>

  // Computed
  visibleColumns: Ref<TableColumn[]>
  leftFixedColumns: Ref<TableColumn[]>
  rightFixedColumns: Ref<TableColumn[]>
  scrollableColumns: Ref<TableColumn[]>
  horizontalContainerStyle: Ref<Record<string, string | undefined>>
  horizontalScrollAreaStyle: Ref<Record<string, string | undefined>>

  // Methods
  scrollToColumn: (columnIndex: number) => void
  scrollToLeft: () => void
  updateHorizontalVisibleRange: () => void
  getColumnWidth: (columnIndex: number) => number
  getTotalWidth: () => number

  // Performance monitoring
  getHorizontalMetrics: () => any
}

/**
 * 横向虚拟滚动 composable
 * 优化多列表格的横向滚动性能
 */
export function useHorizontalVirtual({
  containerRef,
  columns,
  config
}: UseHorizontalVirtualOptions): UseHorizontalVirtualReturn {
  // 横向虚拟滚动状态
  const horizontalVirtualState = ref<VirtualState>({
    scrollTop: 0,
    visibleStart: 0,
    visibleEnd: 0,
    totalHeight: 0,
    containerHeight: 0,

    // 横向状态
    scrollLeft: 0,
    visibleColumnStart: 0,
    visibleColumnEnd: 0,
    totalWidth: 0,
    containerWidth: 0
  })

  // 缓存和性能监控
  const horizontalStyleCache = new PredictiveStyleCache()
  const columnWidthCache = new DimensionCache()
  const { metrics, updateMetrics, recordUpdate, updateFPS, throttleScroll, cleanup } =
    useVirtualMetrics()

  // 检查是否启用横向虚拟滚动
  const isHorizontalVirtualEnabled = computed(() => {
    const cfg = config.value
    if (!cfg?.horizontal) return false

    // 如果显式禁用，直接返回 false
    if (cfg.horizontal.enabled === false) return false

    const threshold = cfg.horizontal.threshold || DEFAULT_HORIZONTAL_CONFIG.threshold
    const columnCount = columns.value.length

    // 自适应模式
    return columnCount >= threshold
  })

  // 获取合并配置
  const mergedHorizontalConfig = computed(() => ({
    ...DEFAULT_HORIZONTAL_CONFIG,
    ...(config.value?.horizontal || {})
  }))

  // 计算固定列
  const leftFixedColumns = computed(() => columns.value.filter(col => col.fixed === 'left'))

  const rightFixedColumns = computed(() => columns.value.filter(col => col.fixed === 'right'))

  const scrollableColumns = computed(() => columns.value.filter(col => !col.fixed))

  // 获取列宽度
  const getColumnWidth = (columnIndex: number): number => {
    if (columnIndex < 0 || columnIndex >= columns.value.length) {
      return 0
    }

    const column = columns.value[columnIndex]
    const cfg = mergedHorizontalConfig.value

    // 从缓存获取
    if (columnWidthCache.has(columnIndex)) {
      return columnWidthCache.get(columnIndex)!
    }

    let width: number

    if (cfg.columnWidth === 'auto') {
      // 自动计算列宽
      if (column.width) {
        width = typeof column.width === 'number' ? column.width : parseInt(column.width.toString())
      } else {
        // 尝试从DOM中测量
        const measuredWidth = measureColumnWidth(columnIndex)
        width = measuredWidth > 0 ? measuredWidth : 150 // 默认列宽
      }
    } else {
      width = typeof cfg.columnWidth === 'number' ? cfg.columnWidth : 150
    }

    // 应用最小宽度和最大宽度约束
    if (column.minWidth && width < column.minWidth) {
      width = column.minWidth
    }
    if (column.maxWidth && width > column.maxWidth) {
      width = column.maxWidth
    }

    // 缓存结果
    columnWidthCache.set(columnIndex, width)
    return width
  }

  // 从DOM测量列宽度
  const measureColumnWidth = (columnIndex: number): number => {
    if (!containerRef.value) return 0

    const headerCells = containerRef.value.querySelectorAll('.header-cell')
    if (headerCells.length > columnIndex) {
      const cell = headerCells[columnIndex] as HTMLElement
      const rect = cell.getBoundingClientRect()
      return rect.width
    }

    return 0
  }

  // 计算总宽度
  const getTotalWidth = (): number => {
    if (!isHorizontalVirtualEnabled.value) {
      return columns.value.reduce((total, _, index) => total + getColumnWidth(index), 0)
    }

    return scrollableColumns.value.reduce((total, _, index) => {
      const actualIndex = leftFixedColumns.value.length + index
      return total + getColumnWidth(actualIndex)
    }, 0)
  }

  // 更新横向可见范围
  const updateHorizontalVisibleRange = () => {
    const updateStartTime = performance.now()

    if (!isHorizontalVirtualEnabled.value || !containerRef.value) {
      return
    }

    // FPS 监控
    updateFPS('horizontal')

    const container = containerRef.value
    const containerWidth = container.clientWidth
    const scrollLeft = container.scrollLeft
    const cfg = mergedHorizontalConfig.value

    // 计算可见列范围（仅考虑可滚动列）
    let currentOffset = 0
    let visibleColumnStart = 0
    let visibleColumnEnd = 0

    // 找到可见开始列
    const scrollableStartIndex = leftFixedColumns.value.length
    for (let i = 0; i < scrollableColumns.value.length; i++) {
      const columnIndex = scrollableStartIndex + i
      const columnWidth = getColumnWidth(columnIndex)

      if (currentOffset + columnWidth > scrollLeft) {
        visibleColumnStart = Math.max(0, i - cfg.bufferColumns)
        break
      }
      currentOffset += columnWidth
    }

    // 找到可见结束列
    currentOffset = 0
    for (let i = visibleColumnStart; i < scrollableColumns.value.length; i++) {
      const columnIndex = scrollableStartIndex + i
      const columnWidth = getColumnWidth(columnIndex)
      currentOffset += columnWidth

      if (currentOffset >= scrollLeft + containerWidth + cfg.bufferColumns * 150) {
        visibleColumnEnd = Math.min(scrollableColumns.value.length, i + cfg.overscanColumns)
        break
      }
    }

    if (visibleColumnEnd === 0) {
      visibleColumnEnd = Math.min(
        scrollableColumns.value.length,
        visibleColumnStart + Math.ceil(containerWidth / 150) + cfg.bufferColumns * 2
      )
    }

    // 更新状态
    horizontalVirtualState.value = {
      ...horizontalVirtualState.value,
      scrollLeft,
      visibleColumnStart,
      visibleColumnEnd,
      totalWidth: getTotalWidth(),
      containerWidth
    }

    // 更新样式缓存滚动方向
    horizontalStyleCache.updateScrollDirection(visibleColumnStart, 'horizontal')

    // 记录性能指标
    const updateTime = performance.now() - updateStartTime
    recordUpdate(updateTime)
  }

  // 获取可见列
  const visibleColumns = computed(() => {
    if (!isHorizontalVirtualEnabled.value) {
      return columns.value
    }

    const { visibleColumnStart, visibleColumnEnd } = horizontalVirtualState.value

    // 组合固定列和可见可滚动列
    const visibleScrollableColumns = scrollableColumns.value.slice(
      visibleColumnStart || 0,
      visibleColumnEnd || scrollableColumns.value.length
    )

    return [...leftFixedColumns.value, ...visibleScrollableColumns, ...rightFixedColumns.value]
  })

  // 容器样式
  const horizontalContainerStyle = computed(() => {
    if (!isHorizontalVirtualEnabled.value) return {}

    return {
      overflowX: 'auto',
      position: 'relative',
      // 优化横向滚动性能
      willChange: 'scroll-position'
    }
  })

  // 滚动区域样式
  const horizontalScrollAreaStyle = computed(() => {
    if (!isHorizontalVirtualEnabled.value) {
      horizontalStyleCache.clear()
      return {}
    }

    const { visibleColumnStart } = horizontalVirtualState.value
    if (visibleColumnStart === undefined) return {}

    // 计算横向偏移
    let offsetX = 0
    const scrollableStartIndex = leftFixedColumns.value.length
    for (let i = 0; i < visibleColumnStart; i++) {
      const columnIndex = scrollableStartIndex + i
      offsetX += getColumnWidth(columnIndex)
    }

    const cacheKey = `${visibleColumnStart}-${offsetX}-horizontal`

    let style = horizontalStyleCache.get(cacheKey)
    if (!style) {
      style = {
        transform: `translateX(${offsetX}px)`,
        position: 'relative'
      }
      horizontalStyleCache.set(cacheKey, style)
    }

    // 预加载相邻的样式
    nextTick(() => {
      horizontalStyleCache.preloadStyles(visibleColumnStart, 150, 3, 'horizontal')
    })

    return style
  })

  // 滚动到指定列
  const scrollToColumn = (columnIndex: number) => {
    if (!containerRef.value || !isHorizontalVirtualEnabled.value) return

    let targetScrollLeft = 0
    const scrollableStartIndex = leftFixedColumns.value.length

    if (columnIndex >= scrollableStartIndex) {
      const scrollableColumnIndex = columnIndex - scrollableStartIndex
      for (let i = 0; i < scrollableColumnIndex; i++) {
        targetScrollLeft += getColumnWidth(scrollableStartIndex + i)
      }
    }

    containerRef.value.scrollLeft = targetScrollLeft
  }

  // 滚动到左侧
  const scrollToLeft = () => {
    if (containerRef.value) {
      containerRef.value.scrollLeft = 0
    }
  }

  // 横向滚动事件处理
  const handleHorizontalScroll = () => {
    if (!containerRef.value || !isHorizontalVirtualEnabled.value) return

    const scrollPosition = {
      x: containerRef.value.scrollLeft,
      y: containerRef.value.scrollTop
    }

    throttleScroll(scrollPosition, updateHorizontalVisibleRange, 'horizontal')
  }

  // 获取性能指标
  const getHorizontalMetrics = () => {
    const visibleCount =
      (horizontalVirtualState.value.visibleColumnEnd || 0) -
      (horizontalVirtualState.value.visibleColumnStart || 0)
    const totalCount = scrollableColumns.value.length

    // 如果横向虚拟滚动未启用，返回基本指标
    if (!isHorizontalVirtualEnabled.value) {
      return {
        updateCount: 0,
        averageUpdateTime: 0,
        scrollFPS: 0,
        cacheHitRate: 0,
        visibleItemCount: 0,
        bufferEfficiency: 0,
        // 横向虚拟滚动特有指标
        horizontalScrollFPS: 0,
        visibleColumnCount: totalCount,
        columnCacheHitRate: 0
      }
    }

    // 更新 FPS
    updateFPS('horizontal')

    updateMetrics(
      0, // visibleItemCount (not applicable for horizontal)
      0, // totalItemCount (not applicable for horizontal)
      0, // cacheHitRate (not applicable for horizontal)
      visibleCount,
      totalCount,
      horizontalStyleCache.getHitRate()
    )

    // 返回包含横向滚动指标的完整指标
    const baseMetrics = metrics.value
    return {
      ...baseMetrics,
      // 横向虚拟滚动特有指标
      horizontalScrollFPS: baseMetrics.horizontalScrollFPS || 0,
      visibleColumnCount: visibleCount,
      columnCacheHitRate: Math.round(horizontalStyleCache.getHitRate() * 100)
    }
  }

  // 监听列变化
  watch(
    columns,
    () => {
      if (isHorizontalVirtualEnabled.value) {
        columnWidthCache.clear()
        updateHorizontalVisibleRange()
      }
    },
    { deep: true }
  )

  // 监听配置变化
  watch(
    config,
    () => {
      if (isHorizontalVirtualEnabled.value) {
        updateHorizontalVisibleRange()
      }
    },
    { deep: true }
  )

  // 设置横向滚动监听器
  onMounted(() => {
    nextTick(() => {
      if (containerRef.value && isHorizontalVirtualEnabled.value) {
        containerRef.value.addEventListener('scroll', handleHorizontalScroll, { passive: true })
        updateHorizontalVisibleRange()
      }
    })
  })

  // 监听横向虚拟滚动状态变化
  watch(isHorizontalVirtualEnabled, (enabled, wasEnabled) => {
    if (!containerRef.value) return

    if (enabled && !wasEnabled) {
      containerRef.value.addEventListener('scroll', handleHorizontalScroll, { passive: true })
      updateHorizontalVisibleRange()
    } else if (!enabled && wasEnabled) {
      containerRef.value.removeEventListener('scroll', handleHorizontalScroll)
    }
  })

  // 清理
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleHorizontalScroll)
    }
    cleanup()
    horizontalStyleCache.clear()
    columnWidthCache.clear()
  })

  return {
    horizontalVirtualState,
    isHorizontalVirtualEnabled,
    visibleColumns,
    leftFixedColumns,
    rightFixedColumns,
    scrollableColumns,
    horizontalContainerStyle,
    horizontalScrollAreaStyle,
    scrollToColumn,
    scrollToLeft,
    updateHorizontalVisibleRange,
    getColumnWidth,
    getTotalWidth,
    getHorizontalMetrics
  }
}
