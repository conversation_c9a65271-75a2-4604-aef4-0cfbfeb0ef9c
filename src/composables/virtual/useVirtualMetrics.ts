// Virtual scrolling performance metrics
import { ref, type Ref } from 'vue'

export interface VirtualScrollMetrics {
  updateCount: number
  averageUpdateTime: number
  scrollFPS: number
  cacheHitRate: number
  visibleItemCount: number
  bufferEfficiency: number
  // 新增横向指标
  visibleColumnCount?: number
  columnCacheHitRate?: number
  horizontalScrollFPS?: number
}

/**
 * 性能监控类 - 优化内存使用
 */
export class PerformanceMonitor {
  private updateTimes: number[] = []
  private updateCount = 0
  private lastFPSTime = 0
  private frameCount = 0
  private scrollFPS = 0
  private horizontalScrollFPS = 0
  private maxHistoryLength = 50 // 减少历史记录长度从100到50
  private lastCleanupTime = 0
  private cleanupInterval = 5000 // 5秒清理一次

  recordUpdate(updateTime: number) {
    this.updateTimes.push(updateTime)
    this.updateCount++

    // 定期清理历史记录，避免数组无限增长
    const now = performance.now()
    if (now - this.lastCleanupTime > this.cleanupInterval) {
      this.cleanupHistory()
      this.lastCleanupTime = now
    }

    // 限制历史记录长度 - 使用splice而不是shift以提高性能
    if (this.updateTimes.length > this.maxHistoryLength) {
      this.updateTimes.splice(0, this.updateTimes.length - this.maxHistoryLength)
    }
  }

  private cleanupHistory() {
    // 保留最近20个记录，清理旧数据
    if (this.updateTimes.length > 20) {
      this.updateTimes = this.updateTimes.slice(-20)
    }
  }

  updateFPS(type: 'vertical' | 'horizontal' = 'vertical') {
    const now = performance.now()
    this.frameCount++

    // 初始化时间戳
    if (this.lastFPSTime === 0) {
      this.lastFPSTime = now
      return
    }

    const elapsed = now - this.lastFPSTime
    if (elapsed >= 500) {
      // 降低到500ms以便更快响应
      const fps = Math.round((this.frameCount * 1000) / elapsed)

      if (type === 'vertical') {
        this.scrollFPS = fps
      } else {
        this.horizontalScrollFPS = fps
      }

      this.frameCount = 0
      this.lastFPSTime = now
    }
  }

  getMetrics(
    visibleItemCount: number,
    totalItemCount: number,
    cacheHitRate: number,
    visibleColumnCount?: number,
    totalColumnCount?: number,
    columnCacheHitRate?: number
  ): VirtualScrollMetrics {
    const avgUpdateTime =
      this.updateTimes.length > 0
        ? this.updateTimes.reduce((a, b) => a + b, 0) / this.updateTimes.length
        : 0

    const bufferEfficiency =
      totalItemCount > 0 ? Math.max(0, 100 - (visibleItemCount / totalItemCount) * 100) : 0

    const metrics: VirtualScrollMetrics = {
      updateCount: this.updateCount,
      averageUpdateTime: Math.round(avgUpdateTime * 100) / 100,
      scrollFPS: this.scrollFPS,
      cacheHitRate: Math.round(cacheHitRate * 100),
      visibleItemCount,
      bufferEfficiency: Math.round(bufferEfficiency * 100) / 100
    }

    // 添加横向指标
    if (visibleColumnCount !== undefined && totalColumnCount !== undefined) {
      metrics.visibleColumnCount = visibleColumnCount
      metrics.horizontalScrollFPS = this.horizontalScrollFPS

      if (columnCacheHitRate !== undefined) {
        metrics.columnCacheHitRate = Math.round(columnCacheHitRate * 100)
      }
    }

    return metrics
  }

  reset() {
    // 完全清理所有历史数据
    this.updateTimes.length = 0 // 清空数组而不是重新分配
    this.updateCount = 0
    this.frameCount = 0
    this.scrollFPS = 0
    this.horizontalScrollFPS = 0
    this.lastFPSTime = 0
    this.lastCleanupTime = 0
  }

  // 获取当前内存使用情况（用于调试）
  getMemoryInfo() {
    return {
      updateTimesLength: this.updateTimes.length,
      updateCount: this.updateCount,
      memoryUsageKB: Math.round(((this.updateTimes.length * 8) / 1024) * 100) / 100 // 估算内存使用
    }
  }
}

/**
 * 滚动节流器（支持纵向和横向）- 优化资源管理
 */
export class ScrollThrottler {
  private rafId: number | undefined
  private lastScrollTime = 0
  private lastScrollPosition = { x: 0, y: 0 }
  private scrollVelocity = { x: 0, y: 0 }
  private isScrolling = false
  private scrollEndTimer: number | undefined
  private isDestroyed = false // 添加销毁状态标记

  throttleScroll(
    scrollPosition: { x: number; y: number },
    callback: () => void,
    type: 'vertical' | 'horizontal' | 'both' = 'vertical'
  ) {
    // 如果已销毁，不执行任何操作
    if (this.isDestroyed) {
      return
    }

    const now = performance.now()
    const timeDelta = now - this.lastScrollTime

    // 计算滚动速度
    if (timeDelta > 0) {
      const deltaX = Math.abs(scrollPosition.x - this.lastScrollPosition.x)
      const deltaY = Math.abs(scrollPosition.y - this.lastScrollPosition.y)

      this.scrollVelocity.x = deltaX / timeDelta
      this.scrollVelocity.y = deltaY / timeDelta
    }

    this.isScrolling = true

    // 清除之前的滚动结束定时器
    this.clearScrollEndTimer()

    // 根据滚动速度和类型调整节流策略
    const currentVelocity =
      type === 'horizontal'
        ? this.scrollVelocity.x
        : type === 'vertical'
          ? this.scrollVelocity.y
          : Math.max(this.scrollVelocity.x, this.scrollVelocity.y)

    if (currentVelocity > 2) {
      // 高速滚动：使用RAF确保流畅性
      this.clearRAF()
      this.rafId = requestAnimationFrame(() => {
        if (!this.isDestroyed) {
          callback()
        }
        this.rafId = undefined
      })
    } else {
      // 低速滚动：直接执行，减少RAF开销
      if (!this.isDestroyed) {
        callback()
      }
    }

    // 设置滚动结束检测
    this.scrollEndTimer = window.setTimeout(() => {
      if (!this.isDestroyed) {
        this.isScrolling = false
        this.scrollVelocity = { x: 0, y: 0 }
      }
    }, 150)

    this.lastScrollTime = now
    this.lastScrollPosition = { ...scrollPosition }
  }

  private clearRAF() {
    if (this.rafId) {
      cancelAnimationFrame(this.rafId)
      this.rafId = undefined
    }
  }

  private clearScrollEndTimer() {
    if (this.scrollEndTimer) {
      clearTimeout(this.scrollEndTimer)
      this.scrollEndTimer = undefined
    }
  }

  getScrollVelocity(type: 'vertical' | 'horizontal' = 'vertical'): number {
    return type === 'vertical' ? this.scrollVelocity.y : this.scrollVelocity.x
  }

  isScrollingActive(): boolean {
    return this.isScrolling
  }

  cleanup() {
    this.isDestroyed = true
    this.clearRAF()
    this.clearScrollEndTimer()
    // 重置所有状态
    this.isScrolling = false
    this.scrollVelocity = { x: 0, y: 0 }
    this.lastScrollTime = 0
    this.lastScrollPosition = { x: 0, y: 0 }
  }

  // 检查是否已销毁（用于调试）
  getDestroyStatus(): boolean {
    return this.isDestroyed
  }
}

/**
 * 使用虚拟滚动性能监控的 composable
 */
export function useVirtualMetrics() {
  const performanceMonitor = new PerformanceMonitor()
  const scrollThrottler = new ScrollThrottler()

  const metrics = ref<VirtualScrollMetrics>({
    updateCount: 0,
    averageUpdateTime: 0,
    scrollFPS: 0,
    cacheHitRate: 0,
    visibleItemCount: 0,
    bufferEfficiency: 0
  })

  const updateMetrics = (
    visibleItemCount: number,
    totalItemCount: number,
    cacheHitRate: number,
    visibleColumnCount?: number,
    totalColumnCount?: number,
    columnCacheHitRate?: number
  ) => {
    metrics.value = performanceMonitor.getMetrics(
      visibleItemCount,
      totalItemCount,
      cacheHitRate,
      visibleColumnCount,
      totalColumnCount,
      columnCacheHitRate
    )
  }

  const recordUpdate = (updateTime: number) => {
    performanceMonitor.recordUpdate(updateTime)
  }

  const updateFPS = (type: 'vertical' | 'horizontal' = 'vertical') => {
    performanceMonitor.updateFPS(type)
  }

  const throttleScroll = (
    scrollPosition: { x: number; y: number },
    callback: () => void,
    type: 'vertical' | 'horizontal' | 'both' = 'vertical'
  ) => {
    scrollThrottler.throttleScroll(scrollPosition, callback, type)
  }

  const getScrollVelocity = (type: 'vertical' | 'horizontal' = 'vertical'): number => {
    return scrollThrottler.getScrollVelocity(type)
  }

  const cleanup = () => {
    performanceMonitor.reset()
    scrollThrottler.cleanup()
  }

  return {
    metrics: metrics as Ref<VirtualScrollMetrics>,
    updateMetrics,
    recordUpdate,
    updateFPS,
    throttleScroll,
    getScrollVelocity,
    cleanup
  }
}
