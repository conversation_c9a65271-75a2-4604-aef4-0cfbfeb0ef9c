// Virtual scrolling module exports
export {
  useVerticalVirtual,
  type UseVerticalVirtualOptions,
  type UseVerticalVirtualReturn
} from './useVerticalVirtual'
export {
  useHorizontalVirtual,
  type UseHorizontalVirtualOptions,
  type UseHorizontalVirtualReturn
} from './useHorizontalVirtual'
export { useVirtualMetrics, type VirtualScrollMetrics } from './useVirtualMetrics'
export { PredictiveStyleCache, DimensionCache } from './useVirtualCache'

// Unified virtual scrolling composable
import { computed, type Ref } from 'vue'
import type { TableRow, TableColumn, VirtualConfig, VirtualState } from '@/types'
import { useVerticalVirtual } from './useVerticalVirtual'
import { useHorizontalVirtual } from './useHorizontalVirtual'

export interface UseVirtualOptions {
  containerRef: Ref<HTMLElement | null>
  data: Ref<TableRow[]>
  columns: Ref<TableColumn[]>
  config: Ref<VirtualConfig | undefined>
}

export interface UseVirtualReturn {
  // Vertical virtual scrolling
  virtualState: Ref<VirtualState>
  isVirtualEnabled: Ref<boolean>
  visibleData: Ref<TableRow[]>
  containerStyle: Ref<Record<string, string | undefined>>
  scrollAreaStyle: Ref<Record<string, string | undefined>>
  scrollToIndex: (index: number) => void
  scrollToTop: () => void
  updateVisibleRange: () => void
  getItemHeight: (index: number) => number
  getMetrics: () => any

  // Horizontal virtual scrolling
  horizontalVirtualState: Ref<VirtualState>
  isHorizontalVirtualEnabled: Ref<boolean>
  visibleColumns: Ref<TableColumn[]>
  leftFixedColumns: Ref<TableColumn[]>
  rightFixedColumns: Ref<TableColumn[]>
  scrollableColumns: Ref<TableColumn[]>
  horizontalContainerStyle: Ref<Record<string, string | undefined>>
  horizontalScrollAreaStyle: Ref<Record<string, string | undefined>>
  scrollToColumn: (columnIndex: number) => void
  scrollToLeft: () => void
  updateHorizontalVisibleRange: () => void
  getColumnWidth: (columnIndex: number) => number
  getTotalWidth: () => number
  getHorizontalMetrics: () => any
}

/**
 * 统一的虚拟滚动 composable
 * 集成纵向和横向虚拟滚动功能
 */
export function useVirtual({
  containerRef,
  data,
  columns,
  config
}: UseVirtualOptions): UseVirtualReturn {
  // 纵向虚拟滚动
  const {
    verticalVirtualState,
    isVerticalVirtualEnabled,
    visibleData,
    verticalContainerStyle,
    verticalScrollAreaStyle,
    scrollToIndex,
    scrollToTop,
    updateVerticalVisibleRange,
    getItemHeight,
    getVerticalMetrics
  } = useVerticalVirtual({ containerRef, data, config })

  // 横向虚拟滚动
  const {
    horizontalVirtualState,
    isHorizontalVirtualEnabled,
    visibleColumns,
    leftFixedColumns,
    rightFixedColumns,
    scrollableColumns,
    horizontalContainerStyle,
    horizontalScrollAreaStyle,
    scrollToColumn,
    scrollToLeft,
    updateHorizontalVisibleRange,
    getColumnWidth,
    getTotalWidth,
    getHorizontalMetrics
  } = useHorizontalVirtual({ containerRef, columns, config })

  // 统一的容器样式（合并纵向和横向）
  const containerStyle = computed(() => ({
    ...verticalContainerStyle.value,
    ...horizontalContainerStyle.value
  }))

  // 统一的滚动区域样式（优先考虑纵向）
  const scrollAreaStyle = computed(() => ({
    ...verticalScrollAreaStyle.value
    // 横向偏移通过其他方式处理，避免冲突
  }))

  // 统一的虚拟滚动状态（合并纵向和横向状态）
  const virtualState = computed(() => ({
    ...verticalVirtualState.value,
    ...horizontalVirtualState.value
  }))

  // 统一的虚拟滚动启用状态
  const isVirtualEnabled = computed(
    () => isVerticalVirtualEnabled.value || isHorizontalVirtualEnabled.value
  )

  // 统一更新可见范围
  const updateVisibleRange = () => {
    updateVerticalVisibleRange()
    updateHorizontalVisibleRange()
  }

  // 统一性能指标
  const getMetrics = () => {
    const verticalMetrics = getVerticalMetrics()
    const horizontalMetrics = getHorizontalMetrics()

    // 正确合并指标，避免横向指标覆盖纵向指标
    return {
      // 纵向虚拟滚动指标
      updateCount: verticalMetrics.updateCount || 0,
      averageUpdateTime: verticalMetrics.averageUpdateTime || 0,
      scrollFPS: verticalMetrics.scrollFPS || 0,
      cacheHitRate: verticalMetrics.cacheHitRate || 0,
      visibleItemCount: verticalMetrics.visibleItemCount || 0,
      bufferEfficiency: verticalMetrics.bufferEfficiency || 0,

      // 横向虚拟滚动特有指标
      horizontalScrollFPS: horizontalMetrics.horizontalScrollFPS || 0,
      visibleColumnCount: horizontalMetrics.visibleColumnCount || 0,
      columnCacheHitRate: horizontalMetrics.columnCacheHitRate || 0
    }
  }

  return {
    // Vertical virtual scrolling
    virtualState,
    isVirtualEnabled,
    visibleData,
    containerStyle,
    scrollAreaStyle,
    scrollToIndex,
    scrollToTop,
    updateVisibleRange,
    getItemHeight,
    getMetrics,

    // Horizontal virtual scrolling
    horizontalVirtualState,
    isHorizontalVirtualEnabled,
    visibleColumns,
    leftFixedColumns,
    rightFixedColumns,
    scrollableColumns,
    horizontalContainerStyle,
    horizontalScrollAreaStyle,
    scrollToColumn,
    scrollToLeft,
    updateHorizontalVisibleRange,
    getColumnWidth,
    getTotalWidth,
    getHorizontalMetrics
  }
}

// 向后兼容：导出原有接口
export type { UseVirtualOptions as UseVirtualScrollingOptions }
export type { UseVirtualReturn as UseVirtualScrollingReturn }
