/**
 * 固定列管理 Composable - 基于 Intersection Observer 的高性能方案
 * 实现技术设计文档中的 CSS position: sticky + Intersection Observer 组合方案
 */

import { ref, computed, watch, onMounted, onUnmounted, nextTick, unref } from 'vue'
import type { TableColumn } from '@/types'
import type {
  UseFixedColumnsOptions,
  UseFixedColumnsReturn,
  SentinelElement,
  SentinelElements,
  FixedColumnScrollState,
  ShadowState,
  FixedColumnBoundary,
  FixedColumnStyleResult,
  ObserverConfig,
  ObserverCleanup
} from '@/types/fixedColumns'

/**
 * 默认观察者配置
 */
const DEFAULT_OBSERVER_CONFIG: ObserverConfig = {
  root: null as any, // 将在运行时设置
  threshold: 1.0, // 元素完全可见时触发
  rootMargin: '0px'
}

/**
 * 固定列管理 Hook
 * 
 * 核心特性：
 * 1. 使用 Intersection Observer 高效监测滚动状态
 * 2. 自动创建和管理哨兵元素
 * 3. 动态计算固定列位置和阴影状态
 * 4. 提供完整的类型安全支持
 */
export function useFixedColumns(options: UseFixedColumnsOptions): UseFixedColumnsReturn {
  const {
    containerRef,
    columns,
    enabled = true,
    observerConfig = {},
    callbacks = {}
  } = options

  // 内部状态
  const sentinels = ref<SentinelElements>({})
  const observerCleanups = ref<Array<() => void>>([])
  const isScrolledLeft = ref(false)  // 初始状态：左侧哨兵可见，无阴影
  const isScrolledRight = ref(false) // 初始状态：右侧哨兵可见，无阴影
  let scrollRafId: number | null = null // requestAnimationFrame ID for scroll throttling
  let initRafId: number | null = null // requestAnimationFrame ID for initialization

  /**
   * 计算固定列边界信息
   */
  const boundaryInfo = computed<FixedColumnBoundary>(() => {
    const cols = unref(columns)
    let lastLeftFixedIndex = -1
    let firstRightFixedIndex = -1

    // 找到最后一个左固定列
    for (let i = cols.length - 1; i >= 0; i--) {
      if (cols[i].fixed === 'left') {
        lastLeftFixedIndex = i
        break
      }
    }

    // 找到第一个右固定列
    for (let i = 0; i < cols.length; i++) {
      if (cols[i].fixed === 'right') {
        firstRightFixedIndex = i
        break
      }
    }

    return {
      lastLeftFixedIndex,
      firstRightFixedIndex,
      lastLeftFixedColumn: lastLeftFixedIndex >= 0 ? cols[lastLeftFixedIndex] : undefined,
      firstRightFixedColumn: firstRightFixedIndex >= 0 ? cols[firstRightFixedIndex] : undefined
    }
  })

  /**
   * 计算滚动状态
   */
  const scrollState = computed<FixedColumnScrollState>(() => {
    const container = unref(containerRef)
    if (!container) {
      return {
        isScrolledLeft: false,
        isScrolledRight: false,
        isAtLeftEnd: true,
        isAtRightEnd: true,
        scrollLeft: 0,
        containerWidth: 0,
        tableWidth: 0
      }
    }

    const tableElement = container.querySelector('table')
    const scrollLeft = container.scrollLeft
    const containerWidth = container.clientWidth
    const tableWidth = tableElement?.scrollWidth || 0

    const isAtLeftEnd = scrollLeft === 0
    const isAtRightEnd = scrollLeft >= (tableWidth - containerWidth)

    return {
      isScrolledLeft: isScrolledLeft.value,
      isScrolledRight: isScrolledRight.value,
      isAtLeftEnd,
      isAtRightEnd,
      scrollLeft,
      containerWidth,
      tableWidth
    }
  })

  /**
   * 计算阴影状态
   */
  const shadowState = computed<ShadowState>(() => {
    const boundary = boundaryInfo.value

    // 只有当哨兵列不可见时才显示阴影
    // isScrolledLeft/Right 由 Intersection Observer 控制，当哨兵不可见时为 true
    const showLeftShadow = boundary.lastLeftFixedIndex >= 0 && isScrolledLeft.value
    const showRightShadow = boundary.firstRightFixedIndex >= 0 && isScrolledRight.value

    return {
      showLeftShadow,
      showRightShadow
    }
  })

  /**
   * 初始化固定列位置
   * 动态计算每个固定列的 left 和 right 偏移值
   */
  // Store column widths persistently
  const columnWidthCache: Record<string, number> = {}
  const fixedColumnStyles = ref<Record<string, FixedColumnStyleResult>>({})

  const initializeFixedPositions = (): void => {
    const cols = unref(columns)
    const container = unref(containerRef)
    if (!cols.length || !container) return

    let leftPosition = 0
    let rightPosition = 0
    let leftZIndex = 15
    let rightZIndex = 15

    // Update width cache with current DOM widths before clearing positions
    cols.forEach(column => {
      if (column.fixed) {
        const headerCell = container.querySelector(`.table-header .header-cell[data-column-key="${column.key}"]`) as HTMLElement
        const bodyCell = container.querySelector(`.table-body .table-cell[data-column-key="${column.key}"]`) as HTMLElement
        
        if (headerCell && headerCell.offsetWidth > 0) {
          columnWidthCache[column.key] = headerCell.offsetWidth
        } else if (bodyCell && bodyCell.offsetWidth > 0) {
          columnWidthCache[column.key] = bodyCell.offsetWidth
        }
        // Keep existing cache value if DOM query fails
      }
    })

    // Clear previous position markers
    // No longer needed as we are building a new object for fixedColumnStyles
    // cols.forEach(column => {
    //   column._fixedLeftPosition = undefined
    //   column._fixedRightPosition = undefined
    //   column._fixedZIndex = undefined
    //   column._isLastLeftFixed = false
    //   column._isFirstRightFixed = false
    // })

    // Temporary object to build up new styles
    const newFixedColumnStyles: Record<string, FixedColumnStyleResult> = {}

    // Calculate left fixed column positions
    for (let i = 0; i < cols.length; i++) {
      const column = cols[i]
      if (column.fixed === 'left') {
        let width = 0
        
        // Priority order for width determination (modified):
        // 1. Reactive column.width (from props, updated by Table.vue's handleColumnResize)
        // 2. Cached width (from previous DOM measurement)
        // 3. Current DOM measurement
        // 4. Configuration fallback

        if (column.width && typeof column.width === 'number' && column.width > 0) {
          // Prioritize the reactive width from the column config if it's a number and valid
          width = column.width
          columnWidthCache[column.key] = width // Update cache with this value
        } else if (columnWidthCache[column.key]) {
          width = columnWidthCache[column.key]
        } else {
          // 3. Current DOM measurement
          const headerCell = container.querySelector(`.table-header .header-cell[data-column-key="${column.key}"]`) as HTMLElement
          const bodyCell = container.querySelector(`.table-body .table-cell[data-column-key="${column.key}"]`) as HTMLElement
          
          if (headerCell && headerCell.offsetWidth > 0) {
            width = headerCell.offsetWidth
            columnWidthCache[column.key] = width
          } else if (bodyCell && bodyCell.offsetWidth > 0) {
            width = bodyCell.offsetWidth
            columnWidthCache[column.key] = width
          } else {
            // 4. Index-based lookup
            const allHeaderCells = container.querySelectorAll('.table-header .header-cell')
            if (allHeaderCells[i] && (allHeaderCells[i] as HTMLElement).offsetWidth > 0) {
              width = (allHeaderCells[i] as HTMLElement).offsetWidth
              columnWidthCache[column.key] = width
            } else {
              // 5. Configuration fallback
              width = typeof column.width === 'number' 
                ? column.width 
                : parseInt(column.width?.toString() || '100')
              columnWidthCache[column.key] = width
            }
          }
        }
        
        // Update column properties directly
        column._fixedLeftPosition = leftPosition
        column._fixedZIndex = leftZIndex

        newFixedColumnStyles[column.key] = {
          ...newFixedColumnStyles[column.key], // Preserve existing properties if any
          styles: {
            ...newFixedColumnStyles[column.key]?.styles,
            position: 'sticky',
            left: `${leftPosition}px`,
            zIndex: String(leftZIndex)
          }
        }
        leftPosition += width
        leftZIndex--
      }
    }

    // Calculate right fixed column positions
    for (let i = cols.length - 1; i >= 0; i--) {
      const column = cols[i]
      if (column.fixed === 'right') {
        let width = 0
        
        // Same priority order for right columns
        if (column.width && typeof column.width === 'number' && column.width > 0) {
          width = column.width
          columnWidthCache[column.key] = width
        } else if (columnWidthCache[column.key]) {
          width = columnWidthCache[column.key]
        } else {
          const headerCell = container.querySelector(`.table-header .header-cell[data-column-key="${column.key}"]`) as HTMLElement
          const bodyCell = container.querySelector(`.table-body .table-cell[data-column-key="${column.key}"]`) as HTMLElement
          
          if (headerCell && headerCell.offsetWidth > 0) {
            width = headerCell.offsetWidth
            columnWidthCache[column.key] = width
          } else if (bodyCell && bodyCell.offsetWidth > 0) {
            width = bodyCell.offsetWidth
            columnWidthCache[column.key] = width
          } else {
            const allHeaderCells = container.querySelectorAll('.table-header .header-cell')
            if (allHeaderCells[i] && (allHeaderCells[i] as HTMLElement).offsetWidth > 0) {
              width = (allHeaderCells[i] as HTMLElement).offsetWidth
              columnWidthCache[column.key] = width
            } else {
              width = typeof column.width === 'number'
                ? column.width
                : parseInt(column.width?.toString() || '100')
              columnWidthCache[column.key] = width
            }
          }
        }
        
        // Update column properties directly
        column._fixedRightPosition = rightPosition
        column._fixedZIndex = rightZIndex

        newFixedColumnStyles[column.key] = {
          ...newFixedColumnStyles[column.key],
          styles: {
            ...newFixedColumnStyles[column.key]?.styles,
            position: 'sticky',
            right: `${rightPosition}px`,
            zIndex: String(rightZIndex)
          }
        }
        rightPosition += width
        rightZIndex--
      }
    }

    // Mark boundary columns
    const boundary = boundaryInfo.value
    if (boundary.lastLeftFixedIndex >= 0) {
      cols[boundary.lastLeftFixedIndex]._isLastLeftFixed = true
    }
    if (boundary.firstRightFixedIndex >= 0) {
      cols[boundary.firstRightFixedIndex]._isFirstRightFixed = true
    }

    // Update the reactive ref
    fixedColumnStyles.value = newFixedColumnStyles
  }

  /**
   * 创建哨兵元素
   * 根据设计文档在第一个非固定列和最后一个非固定列中放置哨兵
   */
  const createSentinels = (): void => {
    const container = unref(containerRef)
    if (!container || !enabled) return

    const cols = unref(columns)
    const boundary = boundaryInfo.value

    // 清理现有哨兵
    cleanup()

    // 找到第一个非固定列（左侧哨兵位置）
    const firstScrollableIndex = boundary.lastLeftFixedIndex + 1
    if (firstScrollableIndex < cols.length && firstScrollableIndex >= 0) {
      const leftSentinelColumn = cols[firstScrollableIndex]
      if (leftSentinelColumn && !leftSentinelColumn.fixed) {
        createSentinelElement('left', firstScrollableIndex, leftSentinelColumn)
      }
    }

    // 找到最后一个非固定列（右侧哨兵位置）
    const lastScrollableIndex = boundary.firstRightFixedIndex > 0 
      ? boundary.firstRightFixedIndex - 1 
      : cols.length - 1

    if (lastScrollableIndex >= 0 && lastScrollableIndex !== firstScrollableIndex) {
      const rightSentinelColumn = cols[lastScrollableIndex]
      if (rightSentinelColumn && !rightSentinelColumn.fixed) {
        createSentinelElement('right', lastScrollableIndex, rightSentinelColumn)
      }
    }
  }

  /**
   * 创建单个哨兵元素
   */
  const createSentinelElement = (side: 'left' | 'right', _columnIndex: number, column: TableColumn): void => {
    const container = unref(containerRef)
    if (!container) return

    // 在对应的表头单元格中创建哨兵
    const headerCell = container.querySelector(`.table-header .header-cell[data-column-key="${column.key}"]`)
    if (!headerCell) return

    // 创建哨兵元素
    const sentinelElement = document.createElement('div')
    sentinelElement.className = `sentinel-${side}`
    sentinelElement.style.cssText = `
      position: absolute;
      top: 0;
      ${side}: 0;
      width: 1px;
      height: 1px;
      pointer-events: none;
      visibility: hidden;
    `

    headerCell.appendChild(sentinelElement)

    const sentinel: SentinelElement = {
      element: sentinelElement,
      column,
      side
    }

    sentinels.value[`${side}Sentinel`] = sentinel
  }

  /**
   * 设置 Intersection Observer
   * 监视哨兵元素的可见性变化
   */
  const setupObservers = (): ObserverCleanup => {
    const container = unref(containerRef)
    if (!container || !enabled) return () => {}

    // Check if IntersectionObserver is available
    if (typeof IntersectionObserver === 'undefined') {
      console.warn('IntersectionObserver is not available, fixed column scroll detection disabled')
      return () => {}
    }

    const config: ObserverConfig = {
      ...DEFAULT_OBSERVER_CONFIG,
      ...observerConfig,
      root: container
    }

    const cleanupFunctions: Array<() => void> = []

    // 实际的滚动处理逻辑
    const updateScrollState = () => {
      const scrollLeft = container.scrollLeft
      const scrollWidth = container.scrollWidth
      const clientWidth = container.clientWidth
      
      // 左侧阴影：只要开始滚动就显示（scrollLeft > 0）
      if (boundaryInfo.value.lastLeftFixedIndex >= 0) {
        isScrolledLeft.value = scrollLeft > 0
      }
      
      // 右侧阴影：当未滚动到最右端时显示
      if (boundaryInfo.value.firstRightFixedIndex >= 0) {
        const maxScroll = scrollWidth - clientWidth
        isScrolledRight.value = scrollLeft < maxScroll - 1 // -1 处理浮点精度问题
      }
      
      scrollRafId = null // 重置 RAF ID
    }

    // 使用 requestAnimationFrame 节流的滚动事件处理器
    const handleScroll = () => {
      // 如果已经有一个 RAF 在等待，则跳过
      if (scrollRafId !== null) return
      
      // 安排在下一个动画帧更新
      scrollRafId = requestAnimationFrame(updateScrollState)
    }
    
    // 监听滚动事件
    container.addEventListener('scroll', handleScroll, { passive: true })
    cleanupFunctions.push(() => {
      container.removeEventListener('scroll', handleScroll)
      // 清理未执行的 RAF
      if (scrollRafId !== null) {
        cancelAnimationFrame(scrollRafId)
        scrollRafId = null
      }
    })

    // 设置左侧观察者（作为备用方案）
    const leftSentinel = sentinels.value.leftSentinel
    if (leftSentinel) {
      const leftObserver = new IntersectionObserver(([entry]) => {
        // 仅在没有滚动事件时使用哨兵检测
        if (container.scrollLeft === 0 && entry.isIntersecting) {
          isScrolledLeft.value = false
        }
        callbacks.onLeftSentinelChange?.(entry.isIntersecting)
      }, config)

      // 立即检查初始状态
      nextTick(() => {
        updateScrollState() // 直接更新状态，不需要节流
      })

      leftObserver.observe(leftSentinel.element)
      leftSentinel.observer = leftObserver
      
      cleanupFunctions.push(() => {
        leftObserver.disconnect()
        leftSentinel.observer = undefined
      })
    }

    // 设置右侧观察者（作为备用方案）
    const rightSentinel = sentinels.value.rightSentinel
    if (rightSentinel) {
      const rightObserver = new IntersectionObserver(([entry]) => {
        // 仅在滚动到最右端时使用哨兵检测
        const maxScroll = container.scrollWidth - container.clientWidth
        if (Math.abs(container.scrollLeft - maxScroll) < 1 && entry.isIntersecting) {
          isScrolledRight.value = false
        }
        callbacks.onRightSentinelChange?.(entry.isIntersecting)
      }, config)

      // 立即检查初始状态
      nextTick(() => {
        updateScrollState() // 直接更新状态，不需要节流
      })

      rightObserver.observe(rightSentinel.element)
      rightSentinel.observer = rightObserver

      cleanupFunctions.push(() => {
        rightObserver.disconnect()
        rightSentinel.observer = undefined
      })
    }

    // 初始化时立即检查滚动状态
    updateScrollState()

    // 返回清理函数
    const cleanup = () => {
      cleanupFunctions.forEach(fn => fn())
      observerCleanups.value = []
    }

    observerCleanups.value.push(cleanup)
    return cleanup
  }

  /**
   * 获取固定列样式
   * 替代原有的 getFixedColumnBorder 函数
   */
  const getFixedColumnStyle = (column: TableColumn): FixedColumnStyleResult => {
    const classes: string[] = []
    const styles: Record<string, string> = {}

    if (!enabled || !column.fixed) {
      return { classes, styles }
    }

    const columnFixedStyles = fixedColumnStyles.value[column.key]

    if (!columnFixedStyles) {
      // Fallback if styles are not yet calculated (should be rare with the new approach)
      // console.warn(`Fixed column ${column.key} has no calculated styles yet.`)
      return { classes, styles }
    }

    // 基础固定列类
    classes.push(`table-border-fixed-${column.fixed}`)

    // 边界固定列阴影 - 基于 Intersection Observer 的结果
    const shadow = shadowState.value
    if (column._isLastLeftFixed && shadow.showLeftShadow) {
      classes.push('table-border-fixed-left-shadow')
    }
    if (column._isFirstRightFixed && shadow.showRightShadow) {
      classes.push('table-border-fixed-right-shadow')
    }

    // Apply calculated styles
    Object.assign(styles, columnFixedStyles.styles)

    return { classes, styles }
  }

  /**
   * 清理资源
   */
  const cleanup = (): void => {
    // 清理观察者
    observerCleanups.value.forEach(fn => fn())
    observerCleanups.value = []

    // 清理未执行的 requestAnimationFrame
    if (scrollRafId !== null) {
      cancelAnimationFrame(scrollRafId)
      scrollRafId = null
    }
    if (initRafId !== null) {
      cancelAnimationFrame(initRafId)
      initRafId = null
    }

    // 清理哨兵元素
    Object.values(sentinels.value).forEach(sentinel => {
      if (sentinel?.observer) {
        sentinel.observer.disconnect()
      }
      if (sentinel?.element.parentNode) {
        sentinel.element.parentNode.removeChild(sentinel.element)
      }
    })

    sentinels.value = {}
  }

  /**
   * 初始化固定列系统
   */
  const initializeFixedColumns = (): void => {
    if (!enabled) return

    // 取消之前的初始化 RAF
    if (initRafId !== null) {
      cancelAnimationFrame(initRafId)
      initRafId = null
    }

    nextTick(() => {
      requestAnimationFrame(() => {
        initializeFixedPositions()
        createSentinels()
        setupObservers()

        // 使用 requestAnimationFrame 延迟检查初始状态，确保 DOM 完全渲染
        // 通常需要 2-3 帧来确保布局稳定
        let frameCount = 0
        const checkInitialState = () => {
          frameCount++
          const container = unref(containerRef)
          
          if (container && frameCount >= 2) {
            // 直接使用滚动位置检查初始状态
            const scrollLeft = container.scrollLeft
            const scrollWidth = container.scrollWidth
            const clientWidth = container.clientWidth
            
            // 左侧阴影：只要有滚动就显示
            if (boundaryInfo.value.lastLeftFixedIndex >= 0) {
              isScrolledLeft.value = scrollLeft > 0
            }
            
            // 右侧阴影：当未滚动到最右端时显示
            if (boundaryInfo.value.firstRightFixedIndex >= 0) {
              const maxScroll = scrollWidth - clientWidth
              isScrolledRight.value = scrollLeft < maxScroll - 1
            }
            
            initRafId = null
          } else if (frameCount < 3) {
            // 继续等待下一帧
            initRafId = requestAnimationFrame(checkInitialState)
          }
        }
        
        initRafId = requestAnimationFrame(checkInitialState)
      })
    })
  }

  // 监听列配置变化
  watch(
    columns,
    () => {
      if (enabled) {
        initializeFixedColumns()
      }
    },
    { deep: true }
  )

  // 监听启用状态变化
  watch(
    () => enabled,
    (newEnabled) => {
      if (newEnabled) {
        initializeFixedColumns()
      } else {
        cleanup()
      }
    }
  )

  // 组件挂载时初始化
  onMounted(() => {
    if (enabled) {
      initializeFixedColumns()
    }
  })

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  // 监听窗口大小变化，重新计算固定列位置
  const handleResize = (): void => {
    if (enabled) {
      initializeFixedPositions()
    }
  }

  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    scrollState,
    shadowState,
    boundaryInfo,
    sentinels,
    getFixedColumnStyle,
    initializeFixedPositions,
    createSentinels,
    setupObservers,
    cleanup
  }
}

/**
 * 纯函数：计算阴影状态
 * 可用于单元测试和独立使用
 */
export function calculateShadowState(
  column: TableColumn,
  scrollState: FixedColumnScrollState
): string[] {
  const classes: string[] = []

  if (column._isLastLeftFixed && scrollState.isScrolledLeft && !scrollState.isAtLeftEnd) {
    classes.push('table-border-fixed-left-shadow')
  }

  if (column._isFirstRightFixed && scrollState.isScrolledRight && !scrollState.isAtRightEnd) {
    classes.push('table-border-fixed-right-shadow')
  }

  return classes
}

/**
 * 纯函数：创建哨兵元素配置
 * 可用于单元测试
 */
export function createSentinelConfig(
  _container: HTMLElement,
  columns: TableColumn[]
): { leftIndex: number; rightIndex: number } {
  let lastLeftFixedIndex = -1
  let firstRightFixedIndex = -1

  // 找到边界索引
  for (let i = columns.length - 1; i >= 0; i--) {
    if (columns[i].fixed === 'left') {
      lastLeftFixedIndex = i
      break
    }
  }

  for (let i = 0; i < columns.length; i++) {
    if (columns[i].fixed === 'right') {
      firstRightFixedIndex = i
      break
    }
  }

  const leftIndex = lastLeftFixedIndex + 1 // 第一个非固定列
  const rightIndex = firstRightFixedIndex > 0 ? firstRightFixedIndex - 1 : columns.length - 1 // 最后一个非固定列

  return { leftIndex, rightIndex }
}