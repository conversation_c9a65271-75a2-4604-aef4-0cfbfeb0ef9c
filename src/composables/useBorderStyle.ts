/**
 * 边框样式管理 Composable
 * 提供统一的边框样式计算和管理功能
 * 注意：固定列相关逻辑已迁移到 useFixedColumns composable
 */

import { computed, unref, type ComputedRef } from 'vue'
import type { BorderConfig, BorderVariant, BorderComponent } from '@/types/border'

export interface UseBorderStyleOptions {
  /** 边框配置 */
  borderConfig: ComputedRef<BorderConfig> | BorderConfig
  /** 是否启用响应式边框 */
  responsive?: boolean
}

export interface UseBorderStyleReturn {
  /** 获取容器边框类 */
  containerBorderClasses: ComputedRef<string[]>
  /** 获取表头边框类 */
  headerBorderClasses: ComputedRef<string[]>
  /** 获取单元格边框类 */
  cellBorderClasses: ComputedRef<string[]>
  /** 获取边框CSS变量 */
  borderVariables: ComputedRef<Record<string, string>>
  /** 获取分页边框类 */
  paginationBorderClasses: ComputedRef<string[]>
  /** 获取输入框边框类 */
  inputBorderClasses: ComputedRef<string[]>
}

/**
 * 边框样式管理 Hook
 */
export function useBorderStyle(options: UseBorderStyleOptions): UseBorderStyleReturn {
  const { borderConfig, responsive = true } = options

  // 创建响应式的 borderConfig 访问器
  const getBorderConfig = () => unref(borderConfig)

  /**
   * 获取边框类名
   */
  const getBorderClasses = (component: BorderComponent, variant: BorderVariant): string[] => {
    const baseClass = `table-border-${component}-${variant}`
    const classes = [baseClass]

    // 添加响应式类
    if (responsive) {
      classes.push(`${baseClass}-responsive`)
    }

    return classes
  }

  /**
   * 容器边框类
   */
  const containerBorderClasses = computed(() => {
    return getBorderClasses('container', getBorderConfig().type)
  })

  /**
   * 表头边框类
   */
  const headerBorderClasses = computed(() => {
    const config = getBorderConfig()
    const classes = getBorderClasses('header', config.type)
    
    // 添加表头特定的边框类
    if (config.type !== 'none') {
      classes.push('table-border-header-default')
    }

    return classes
  })

  /**
   * 单元格边框类
   */
  const cellBorderClasses = computed(() => {
    return getBorderClasses('cell', getBorderConfig().type)
  })


  /**
   * 边框CSS变量
   */
  const borderVariables = computed(() => {
    const variables: Record<string, string> = {}
    const config = getBorderConfig()

    if (config.width) {
      variables['--table-border-width'] = typeof config.width === 'number' 
        ? `${config.width}px` 
        : config.width
    }

    if (config.style) {
      variables['--table-border-style'] = config.style
    }

    if (config.color) {
      variables['--table-border-color'] = config.color
    }

    if (config.radius) {
      variables['--table-border-radius'] = typeof config.radius === 'number' 
        ? `${config.radius}px` 
        : config.radius
    }

    return variables
  })

  /**
   * 分页边框类
   */
  const paginationBorderClasses = computed(() => {
    if (getBorderConfig().type === 'none') {
      return []
    }
    return ['border-t', 'border-table-border']
  })

  /**
   * 输入框边框类
   */
  const inputBorderClasses = computed(() => {
    return ['border', 'border-table-border', 'rounded']
  })

  return {
    containerBorderClasses,
    headerBorderClasses,
    cellBorderClasses,
    borderVariables,
    paginationBorderClasses,
    inputBorderClasses
  }
}

/**
 * 获取边框类型对应的样式描述
 */
export function getBorderTypeDescription(type: BorderVariant): string {
  const descriptions = {
    default: '默认边框（容器边框）',
    full: '完整边框（容器和所有单元格）',
    outer: '外边框（仅容器边框）',
    inner: '内边框（仅单元格边框）',
    none: '无边框'
  }
  return descriptions[type] || descriptions.default
}

/**
 * 验证边框配置
 */
export function validateBorderConfig(config: BorderConfig): boolean {
  const validTypes: BorderVariant[] = ['default', 'full', 'outer', 'inner', 'none']
  const validStyles = ['solid', 'dashed', 'dotted', 'double']

  if (!validTypes.includes(config.type)) {
    console.warn(`Invalid border type: ${config.type}`)
    return false
  }

  if (config.style && !validStyles.includes(config.style)) {
    console.warn(`Invalid border style: ${config.style}`)
    return false
  }

  return true
}