/**
 * 斑马行样式管理 Composable
 * 提供统一的斑马行样式计算和管理功能
 */

import { computed, type ComputedRef } from 'vue'
import type { TableColumn } from '@/types'
import type { StripeConfig, StripeVariant } from '@/types/stripe'

export interface UseStripeStyleOptions {
  /** 斑马行配置 */
  stripeConfig: StripeConfig
  /** 是否启用固定列 */
  enableFixed?: boolean
}

export interface UseStripeStyleReturn {
  /** 获取容器斑马行类 */
  containerStripeClasses: ComputedRef<string[]>
  /** 获取行斑马行类 */
  getRowStripeClasses: (index: number, selected?: boolean, hover?: boolean, editing?: boolean) => string[]
  /** 获取固定列单元格斑马行样式 */
  getFixedCellStripe: (column: TableColumn, rowIndex: number, selected?: boolean, hover?: boolean, editing?: boolean) => { classes: string[]; styles: Record<string, string> }
  /** 获取当前行背景色（用于CSS变量） */
  getCurrentRowBackground: (index: number, selected?: boolean, hover?: boolean, editing?: boolean) => string
  /** 获取斑马行CSS变量 */
  stripeVariables: ComputedRef<Record<string, string>>
}

/**
 * 斑马行样式管理 Hook
 */
export function useStripeStyle(options: UseStripeStyleOptions): UseStripeStyleReturn {
  const { stripeConfig, enableFixed = true } = options

  /**
   * 容器斑马行类
   */
  const containerStripeClasses = computed(() => {
    if (!stripeConfig.enabled || stripeConfig.type === 'none') {
      return []
    }
    return [`table-stripe-container-${stripeConfig.type}`]
  })

  /**
   * 获取行斑马行类
   */
  const getRowStripeClasses = (
    index: number, 
    selected = false, 
    hover = false, 
    editing = false
  ): string[] => {
    const classes: string[] = []

    if (!stripeConfig.enabled || stripeConfig.type === 'none') {
      return classes
    }

    // 基础斑马行类
    const parity = index % 2 === 0 ? 'even' : 'odd'
    classes.push(`table-stripe-row-${stripeConfig.type}-${parity}`)

    // 状态类 - 优先级管理
    if (editing) {
      classes.push('table-stripe-row-editing')
    } else if (selected) {
      classes.push('table-stripe-row-selected')
    } else if (hover) {
      classes.push('table-stripe-row-hover')
    }

    return classes
  }

  /**
   * 获取固定列单元格斑马行样式
   */
  const getFixedCellStripe = (
    column: TableColumn,
    rowIndex: number,
    selected = false,
    hover = false,
    editing = false
  ) => {
    const classes: string[] = []
    const styles: Record<string, string> = {}

    if (!enableFixed || !column.fixed || !stripeConfig.enabled || stripeConfig.type === 'none') {
      return { classes, styles }
    }

    // 基础固定列斑马行类
    const parity = rowIndex % 2 === 0 ? 'even' : 'odd'
    classes.push(`table-stripe-cell-fixed-${stripeConfig.type}-${parity}`)

    // 状态类 - 与行状态保持一致
    if (editing) {
      classes.push('table-stripe-cell-fixed-editing')
    } else if (selected) {
      classes.push('table-stripe-cell-fixed-selected')
    } else if (hover) {
      classes.push('table-stripe-cell-fixed-hover')
    }

    return { classes, styles }
  }

  /**
   * 获取当前行背景色（用于CSS变量）
   */
  const getCurrentRowBackground = (index: number, selected?: boolean, hover?: boolean, editing?: boolean): string => {
    // 状态优先级：selected > editing > hover > stripe
    if (selected) {
      return 'var(--table-row-selected-bg)'
    }
    
    if (editing) {
      return 'var(--table-primary-bg-5)'
    }
    
    if (hover) {
      return 'var(--table-row-hover-bg)'
    }

    // 斑马纹背景
    if (!stripeConfig.enabled || stripeConfig.type === 'none') {
      return 'var(--table-bg)'
    }

    const isOdd = index % 2 === 1
    switch (stripeConfig.type) {
      case 'default':
        return isOdd ? 'var(--table-stripe-bg-primary)' : 'var(--table-bg)'
      case 'subtle':
        return isOdd ? 'var(--table-stripe-bg-subtle)' : 'var(--table-bg)'
      default:
        return 'var(--table-bg)'
    }
  }

  /**
   * 斑马行CSS变量
   */
  const stripeVariables = computed(() => {
    const variables: Record<string, string> = {}

    if (stripeConfig.intensity !== undefined && stripeConfig.type === 'subtle') {
      variables['--table-stripe-intensity'] = String(stripeConfig.intensity)
    }

    return variables
  })

  return {
    containerStripeClasses,
    getRowStripeClasses,
    getFixedCellStripe,
    getCurrentRowBackground,
    stripeVariables
  }
}

/**
 * 获取斑马行类型对应的样式描述
 */
export function getStripeTypeDescription(type: StripeVariant): string {
  const descriptions = {
    default: '默认斑马行（标准对比度）',
    subtle: '淡化斑马行（低对比度）',
    none: '无斑马行'
  }
  return descriptions[type] || descriptions.default
}

/**
 * 验证斑马行配置
 */
export function validateStripeConfig(config: StripeConfig): boolean {
  const validTypes: StripeVariant[] = ['default', 'subtle', 'none']
  
  if (!validTypes.includes(config.type)) {
    console.warn(`Invalid stripe type: ${config.type}`)
    return false
  }

  if (config.intensity !== undefined && (config.intensity < 0 || config.intensity > 1)) {
    console.warn(`Invalid stripe intensity: ${config.intensity}. Must be between 0 and 1`)
    return false
  }

  return true
}