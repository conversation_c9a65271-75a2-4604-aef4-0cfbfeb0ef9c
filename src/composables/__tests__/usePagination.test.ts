import { describe, it, expect, vi, beforeEach } from 'vitest'
import { usePagination } from '../usePagination'

describe('usePagination', () => {
  let pagination: ReturnType<typeof usePagination>

  beforeEach(() => {
    pagination = usePagination(100, {
      defaultPageSize: 10,
      defaultCurrentPage: 1
    })
  })

  describe('initialization', () => {
    it('should initialize with correct default values', () => {
      expect(pagination.currentPage.value).toBe(1)
      expect(pagination.pageSize.value).toBe(10)
      expect(pagination.total.value).toBe(100)
      expect(pagination.totalPages.value).toBe(10)
    })

    it('should calculate pagination state correctly', () => {
      expect(pagination.startIndex.value).toBe(0)
      expect(pagination.endIndex.value).toBe(10)
      expect(pagination.hasNextPage.value).toBe(true)
      expect(pagination.hasPrevPage.value).toBe(false)
    })

    it('should generate correct page range for small total pages', () => {
      const smallPagination = usePagination(50, { defaultPageSize: 10 })
      expect(smallPagination.pageRange.value).toEqual([1, 2, 3, 4, 5])
    })

    it('should generate correct page range with ellipsis for large total pages', () => {
      const largePagination = usePagination(1000, { defaultPageSize: 10 })
      expect(largePagination.pageRange.value).toEqual([1, 2, 3, 4, 5, -1, 100])
    })
  })

  describe('page navigation', () => {
    it('should set page correctly', () => {
      pagination.setPage(5)
      expect(pagination.currentPage.value).toBe(5)
      expect(pagination.startIndex.value).toBe(40)
      expect(pagination.endIndex.value).toBe(50)
    })

    it('should not set invalid page numbers', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      pagination.setPage(0)
      expect(pagination.currentPage.value).toBe(1)

      pagination.setPage(11)
      expect(pagination.currentPage.value).toBe(1)

      pagination.setPage(1.5)
      expect(pagination.currentPage.value).toBe(1)

      expect(consoleSpy).toHaveBeenCalledTimes(3)
      consoleSpy.mockRestore()
    })

    it('should navigate to next page', () => {
      pagination.nextPage()
      expect(pagination.currentPage.value).toBe(2)
    })

    it('should navigate to previous page', () => {
      pagination.setPage(5)
      pagination.prevPage()
      expect(pagination.currentPage.value).toBe(4)
    })

    it('should navigate to first page', () => {
      pagination.setPage(5)
      pagination.firstPage()
      expect(pagination.currentPage.value).toBe(1)
    })

    it('should navigate to last page', () => {
      pagination.lastPage()
      expect(pagination.currentPage.value).toBe(10)
    })

    it('should jump to specific page', () => {
      pagination.jumpToPage(7)
      expect(pagination.currentPage.value).toBe(7)
    })

    it('should not navigate beyond boundaries', () => {
      // Test next page at boundary
      pagination.setPage(10)
      pagination.nextPage()
      expect(pagination.currentPage.value).toBe(10)

      // Test previous page at boundary
      pagination.setPage(1)
      pagination.prevPage()
      expect(pagination.currentPage.value).toBe(1)
    })
  })

  describe('page size changes', () => {
    it('should set page size correctly', () => {
      pagination.setPageSize(20)
      expect(pagination.pageSize.value).toBe(20)
      expect(pagination.totalPages.value).toBe(5)
    })

    it('should adjust current page when page size changes', () => {
      pagination.setPage(5) // Page 5 with size 10 = items 40-49
      pagination.setPageSize(20) // Should adjust to page 3 (items 40-59)
      expect(pagination.currentPage.value).toBe(3)
    })

    it('should not set invalid page sizes', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      pagination.setPageSize(0)
      expect(pagination.pageSize.value).toBe(10)

      pagination.setPageSize(-5)
      expect(pagination.pageSize.value).toBe(10)

      pagination.setPageSize(1.5)
      expect(pagination.pageSize.value).toBe(10)

      expect(consoleSpy).toHaveBeenCalledTimes(3)
      consoleSpy.mockRestore()
    })
  })

  describe('total changes', () => {
    it('should set total correctly', () => {
      pagination.setTotal(200)
      expect(pagination.total.value).toBe(200)
      expect(pagination.totalPages.value).toBe(20)
    })

    it('should adjust current page when total decreases', () => {
      pagination.setPage(10) // Last page with total 100
      pagination.setTotal(50) // Should adjust to page 5 (new last page)
      expect(pagination.currentPage.value).toBe(5)
    })

    it('should not set invalid totals', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      pagination.setTotal(-10)
      expect(pagination.total.value).toBe(100)

      pagination.setTotal(1.5)
      expect(pagination.total.value).toBe(100)

      expect(consoleSpy).toHaveBeenCalledTimes(2)
      consoleSpy.mockRestore()
    })
  })

  describe('utility methods', () => {
    it('should validate page numbers correctly', () => {
      expect(pagination.isValidPage(1)).toBe(true)
      expect(pagination.isValidPage(10)).toBe(true)
      expect(pagination.isValidPage(0)).toBe(false)
      expect(pagination.isValidPage(11)).toBe(false)
      expect(pagination.isValidPage(1.5)).toBe(false)
    })

    it('should get page data correctly', () => {
      const data = Array.from({ length: 100 }, (_, i) => ({ id: i + 1 }))

      pagination.setPage(1)
      const page1Data = pagination.getPageData(data)
      expect(page1Data).toHaveLength(10)
      expect(page1Data[0].id).toBe(1)
      expect(page1Data[9].id).toBe(10)

      pagination.setPage(2)
      const page2Data = pagination.getPageData(data)
      expect(page2Data).toHaveLength(10)
      expect(page2Data[0].id).toBe(11)
      expect(page2Data[9].id).toBe(20)
    })

    it('should get page info correctly', () => {
      pagination.setPage(3)
      const info = pagination.getPageInfo()

      expect(info).toEqual({
        current: 3,
        pageSize: 10,
        total: 100,
        totalPages: 10,
        startIndex: 20,
        endIndex: 30
      })
    })

    it('should reset pagination correctly', () => {
      pagination.setPage(5)
      pagination.setPageSize(20)
      pagination.setTotal(200)

      pagination.reset()

      expect(pagination.currentPage.value).toBe(1)
      expect(pagination.pageSize.value).toBe(10)
      expect(pagination.total.value).toBe(100)
    })

    it('should update pagination config correctly', () => {
      // Set total first, then page size, then current page to avoid auto-adjustment
      pagination.setTotal(150)
      pagination.setPageSize(20)
      pagination.setPage(3)

      expect(pagination.currentPage.value).toBe(3)
      expect(pagination.pageSize.value).toBe(20)
      expect(pagination.total.value).toBe(150)
    })
  })

  describe('callbacks', () => {
    it('should call onPageChange when page changes', () => {
      const onPageChange = vi.fn()
      const paginationWithCallback = usePagination(100, {
        defaultPageSize: 10,
        onPageChange
      })

      paginationWithCallback.setPage(3)
      expect(onPageChange).toHaveBeenCalledWith(3, 10)
    })

    it('should call onPageSizeChange when page size changes', () => {
      const onPageSizeChange = vi.fn()
      const paginationWithCallback = usePagination(100, {
        defaultPageSize: 10,
        onPageSizeChange
      })

      paginationWithCallback.setPageSize(20)
      expect(onPageSizeChange).toHaveBeenCalledWith(1, 20)
    })

    it('should not call callbacks when values do not change', () => {
      const onPageChange = vi.fn()
      const paginationWithCallback = usePagination(100, {
        defaultPageSize: 10,
        onPageChange
      })

      paginationWithCallback.setPage(1) // Same as current
      expect(onPageChange).not.toHaveBeenCalled()
    })
  })

  describe('edge cases', () => {
    it('should handle zero total correctly', () => {
      const emptyPagination = usePagination(0)
      expect(emptyPagination.totalPages.value).toBe(1)
      expect(emptyPagination.currentPage.value).toBe(1)
      expect(emptyPagination.hasNextPage.value).toBe(false)
      expect(emptyPagination.hasPrevPage.value).toBe(false)
    })

    it('should handle single item correctly', () => {
      const singlePagination = usePagination(1, { defaultPageSize: 10 })
      expect(singlePagination.totalPages.value).toBe(1)
      expect(singlePagination.endIndex.value).toBe(1)
    })

    it('should handle exact page size multiples', () => {
      const exactPagination = usePagination(100, { defaultPageSize: 10 })
      expect(exactPagination.totalPages.value).toBe(10)

      exactPagination.setPage(10)
      expect(exactPagination.startIndex.value).toBe(90)
      expect(exactPagination.endIndex.value).toBe(100)
    })
  })

  describe('page range generation', () => {
    it('should generate correct range for current page in middle', () => {
      const largePagination = usePagination(1000, { defaultPageSize: 10 })
      largePagination.setPage(50)

      const range = largePagination.pageRange.value
      expect(range).toEqual([1, -1, 49, 50, 51, -1, 100])
    })

    it('should generate correct range for current page near end', () => {
      const largePagination = usePagination(1000, { defaultPageSize: 10 })
      largePagination.setPage(98)

      const range = largePagination.pageRange.value
      expect(range).toEqual([1, -1, 96, 97, 98, 99, 100])
    })

    it('should handle boundary cases in page range', () => {
      const mediumPagination = usePagination(70, { defaultPageSize: 10 }) // 7 pages
      expect(mediumPagination.pageRange.value).toEqual([1, 2, 3, 4, 5, 6, 7])
    })
  })
})
