// Unit tests for useSorting composable
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ref, nextTick } from 'vue'
import { useSorting } from '../useSorting'
import type { TableRow, TableColumn, SortConfig } from '../../types'

describe('useSorting composable', () => {
  // Test data
  const mockColumns = ref<TableColumn[]>([
    { key: 'name', title: 'Name', sortable: true },
    { key: 'age', title: 'Age', sortable: true },
    { key: 'email', title: 'Email', sortable: true },
    { key: 'readonly', title: 'Readonly', sortable: false }
  ])

  const mockData = ref<TableRow[]>([
    { name: '<PERSON>', age: 30, email: '<EMAIL>' },
    { name: '<PERSON>', age: 25, email: '<EMAIL>' },
    { name: '<PERSON>', age: 35, email: '<EMAIL>' }
  ])

  beforeEach(() => {
    // Reset data before each test
    mockData.value = [
      { name: '<PERSON>', age: 30, email: '<EMAIL>' },
      { name: '<PERSON>', age: 25, email: '<EMAIL>' },
      { name: '<PERSON>', age: 35, email: '<EMAIL>' }
    ]
  })

  describe('initialization', () => {
    it('should initialize with default values', () => {
      const { sortConfigs, isSorted, sortedData } = useSorting(mockData, mockColumns)

      expect(sortConfigs.value).toEqual([])
      expect(isSorted.value).toBe(false)
      expect(sortedData.value).toEqual(mockData.value)
    })

    it('should initialize with default sort configs', () => {
      const defaultSort: SortConfig[] = [{ column: 'name', direction: 'asc', priority: 0 }]

      const { sortConfigs, isSorted } = useSorting(mockData, mockColumns, {
        defaultSortConfigs: defaultSort
      })

      expect(sortConfigs.value).toEqual(defaultSort)
      expect(isSorted.value).toBe(true)
    })

    it('should initialize with multi-sort enabled', () => {
      const { handleSort } = useSorting(mockData, mockColumns, {
        multiSort: true
      })

      expect(handleSort).toBeDefined()
    })
  })

  describe('sorting functionality', () => {
    it('should sort data by single column', async () => {
      const { handleSort, sortedData, sortConfigs } = useSorting(mockData, mockColumns)

      const nameColumn = mockColumns.value.find(col => col.key === 'name')
      expect(nameColumn).toBeDefined()
      handleSort(nameColumn as TableColumn, 'asc')

      await nextTick()

      expect(sortConfigs.value).toHaveLength(1)
      expect(sortConfigs.value[0]).toEqual({
        column: 'name',
        direction: 'asc',
        priority: 0
      })

      expect(sortedData.value[0]?.['name']).toBe('Alice')
      expect(sortedData.value[1]?.['name']).toBe('Bob')
      expect(sortedData.value[2]?.['name']).toBe('Charlie')
    })

    it('should sort data by multiple columns', async () => {
      const { handleSort, sortedData, sortConfigs } = useSorting(mockData, mockColumns, {
        multiSort: true
      })

      // Add data with same age to test secondary sort
      mockData.value.push({ name: 'David', age: 25, email: '<EMAIL>' })

      const ageColumn = mockColumns.value.find(col => col.key === 'age')
      expect(ageColumn).toBeDefined()
      const nameColumn = mockColumns.value.find(col => col.key === 'name')
      expect(nameColumn).toBeDefined()

      // Sort by age first, then by name
      handleSort(ageColumn as TableColumn, 'asc')
      handleSort(nameColumn as TableColumn, 'asc', true) // Enable multi-sort

      await nextTick()

      expect(sortConfigs.value).toHaveLength(2)

      // Should sort by age first, then by name for same age
      const result = sortedData.value
      expect(result[0].age).toBe(25)
      expect(result[0].name).toBe('Bob') // Bob comes before David alphabetically
      expect(result[1].age).toBe(25)
      expect(result[1].name).toBe('David')
    })

    it('should handle header click for sorting', async () => {
      const { handleHeaderClick, sortConfigs } = useSorting(mockData, mockColumns)

      const nameColumn = mockColumns.value.find(col => col.key === 'name')
      expect(nameColumn).toBeDefined()

      // First click - should sort ascending
      handleHeaderClick(nameColumn as TableColumn)
      await nextTick()
      expect(sortConfigs.value[0].direction).toBe('asc')

      // Second click - should sort descending
      handleHeaderClick(nameColumn as TableColumn)
      await nextTick()
      expect(sortConfigs.value[0].direction).toBe('desc')

      // Third click - should clear sort
      handleHeaderClick(nameColumn as TableColumn)
      await nextTick()
      expect(sortConfigs.value).toHaveLength(0)
    })

    it('should handle header click with multi-sort via keyboard modifier', async () => {
      const { handleHeaderClick, sortConfigs } = useSorting(mockData, mockColumns)

      const nameColumn = mockColumns.value.find(col => col.key === 'name')
      expect(nameColumn).toBeDefined()
      const ageColumn = mockColumns.value.find(col => col.key === 'age')
      expect(ageColumn).toBeDefined()

      // First column
      handleHeaderClick(nameColumn as TableColumn)
      await nextTick()
      expect(sortConfigs.value).toHaveLength(1)

      // Second column with Ctrl key (simulating multi-sort)
      const mockEvent = { ctrlKey: true } as MouseEvent
      handleHeaderClick(ageColumn as TableColumn, mockEvent)
      await nextTick()
      expect(sortConfigs.value).toHaveLength(2)
    })

    it('should not sort non-sortable columns', () => {
      const { handleSort, sortConfigs } = useSorting(mockData, mockColumns)

      const readonlyColumn = mockColumns.value.find(col => col.key === 'readonly')
      expect(readonlyColumn).toBeDefined()
      handleSort(readonlyColumn as TableColumn, 'asc')

      expect(sortConfigs.value).toHaveLength(0)
    })
  })

  describe('sort configuration management', () => {
    it('should set sort configurations', async () => {
      const { setSortConfigs, sortConfigs, sortedData } = useSorting(mockData, mockColumns)

      const newConfigs: SortConfig[] = [{ column: 'age', direction: 'desc', priority: 0 }]

      setSortConfigs(newConfigs)
      await nextTick()

      expect(sortConfigs.value).toEqual(newConfigs)
      expect(sortedData.value[0].age).toBe(35) // Charlie (oldest)
    })

    it('should filter out invalid sort configurations', () => {
      const onSortChange = vi.fn()
      const { setSortConfigs, sortConfigs } = useSorting(mockData, mockColumns, {
        onSortChange
      })

      const invalidConfigs: SortConfig[] = [
        { column: 'name', direction: 'asc', priority: 0 }, // Valid
        { column: 'nonexistent', direction: 'asc', priority: 1 }, // Invalid
        { column: 'readonly', direction: 'desc', priority: 2 } // Invalid (not sortable)
      ]

      setSortConfigs(invalidConfigs)

      expect(sortConfigs.value).toHaveLength(1)
      expect(sortConfigs.value[0].column).toBe('name')
      expect(onSortChange).toHaveBeenCalledWith(sortConfigs.value)
    })

    it('should clear sort for specific column', async () => {
      const { setSortConfigs, clearSort, sortConfigs } = useSorting(mockData, mockColumns)

      // Set multiple sort configs
      setSortConfigs([
        { column: 'name', direction: 'asc', priority: 0 },
        { column: 'age', direction: 'desc', priority: 1 }
      ])

      expect(sortConfigs.value).toHaveLength(2)

      // Clear sort for specific column
      clearSort('name')
      await nextTick()

      expect(sortConfigs.value).toHaveLength(1)
      expect(sortConfigs.value[0].column).toBe('age')
    })

    it('should clear all sorts', async () => {
      const { setSortConfigs, clearSort, sortConfigs } = useSorting(mockData, mockColumns)

      // Set multiple sort configs
      setSortConfigs([
        { column: 'name', direction: 'asc', priority: 0 },
        { column: 'age', direction: 'desc', priority: 1 }
      ])

      expect(sortConfigs.value).toHaveLength(2)

      // Clear all sorts
      clearSort()
      await nextTick()

      expect(sortConfigs.value).toHaveLength(0)
    })
  })

  describe('sort state helpers', () => {
    it('should get sort state for column', () => {
      const { setSortConfigs, getSortState } = useSorting(mockData, mockColumns)

      setSortConfigs([{ column: 'name', direction: 'asc', priority: 0 }])

      const nameColumn = mockColumns.value.find(col => col.key === 'name')
      expect(nameColumn).toBeDefined()
      const ageColumn = mockColumns.value.find(col => col.key === 'age')
      expect(ageColumn).toBeDefined()

      const nameState = getSortState(nameColumn as TableColumn)
      expect(nameState.direction).toBe('asc')
      expect(nameState.priority).toBe(0)
      expect(nameState.isActive).toBe(true)

      const ageState = getSortState(ageColumn as TableColumn)
      expect(ageState.direction).toBe(null)
      expect(ageState.priority).toBe(null)
      expect(ageState.isActive).toBe(false)
    })

    it('should validate sort configuration', () => {
      const { validateSort } = useSorting(mockData, mockColumns)

      const validConfig: SortConfig = { column: 'name', direction: 'asc', priority: 0 }
      const invalidConfig: SortConfig = { column: 'nonexistent', direction: 'asc', priority: 0 }

      expect(validateSort(validConfig)).toBe(true)
      expect(validateSort(invalidConfig)).toBe(false)
    })
  })

  describe('custom sort functions', () => {
    it('should use custom sort function', async () => {
      const customSortFunctions = {
        name: (a: any, b: any) => {
          // Sort by string length instead of alphabetically
          return (a || '').length - (b || '').length
        }
      }

      const { handleSort, sortedData } = useSorting(mockData, mockColumns, {
        customSortFunctions
      })

      const nameColumn = mockColumns.value.find(col => col.key === 'name')
      expect(nameColumn).toBeDefined()
      handleSort(nameColumn as TableColumn, 'asc')

      await nextTick()

      // Should sort by name length: Bob (3) < Alice (5) < Charlie (7)
      expect(sortedData.value[0].name).toBe('Bob')
      expect(sortedData.value[1].name).toBe('Alice')
      expect(sortedData.value[2].name).toBe('Charlie')
    })
  })

  describe('callbacks', () => {
    it('should call onSortChange callback', () => {
      const onSortChange = vi.fn()
      const { handleSort } = useSorting(mockData, mockColumns, {
        onSortChange
      })

      const nameColumn = mockColumns.value.find(col => col.key === 'name')
      expect(nameColumn).toBeDefined()
      handleSort(nameColumn as TableColumn, 'asc')

      expect(onSortChange).toHaveBeenCalledWith([{ column: 'name', direction: 'asc', priority: 0 }])
    })
  })

  describe('reactive updates', () => {
    it('should update when data changes', async () => {
      const { sortedData, handleSort } = useSorting(mockData, mockColumns)

      const nameColumn = mockColumns.value.find(col => col.key === 'name')
      expect(nameColumn).toBeDefined()
      handleSort(nameColumn as TableColumn, 'asc')

      await nextTick()
      expect(sortedData.value).toHaveLength(3)

      // Add new data
      mockData.value.push({ name: 'Eve', age: 28, email: '<EMAIL>' })

      await nextTick()
      expect(sortedData.value).toHaveLength(4)
      expect(sortedData.value[0].name).toBe('Alice') // Still sorted
    })

    it('should validate sort configs when columns change', async () => {
      const { setSortConfigs, sortConfigs } = useSorting(mockData, mockColumns)

      // Set sort config for existing column
      setSortConfigs([
        { column: 'name', direction: 'asc', priority: 0 },
        { column: 'age', direction: 'desc', priority: 1 }
      ])

      expect(sortConfigs.value).toHaveLength(2)

      // Remove a column
      mockColumns.value = mockColumns.value.filter(col => col.key !== 'age')

      await nextTick()

      // Sort config for removed column should be filtered out
      expect(sortConfigs.value).toHaveLength(1)
      expect(sortConfigs.value[0].column).toBe('name')
    })
  })

  describe('error handling', () => {
    it('should handle sorting errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // Create data that might cause sorting errors
      const problematicData = ref([
        { name: 'Alice', age: 30 },
        { name: 'Bob', age: 'invalid' }, // Invalid age
        { name: 'Charlie', age: null }
      ])

      const { handleSort, sortedData } = useSorting(problematicData, mockColumns)

      const ageColumn = mockColumns.value.find(col => col.key === 'age')
      if (ageColumn) {
        handleSort(ageColumn as TableColumn, 'asc')

        await nextTick()

        // Should not throw error and return data (possibly sorted or original)
        expect(sortedData.value).toBeDefined()
        expect(sortedData.value.length).toBe(3)
      }

      consoleSpy.mockRestore()
    })
  })
})
