// Virtual scrolling composable for high performance table rendering
import { ref, computed, watch, onMounted, onUnmounted, nextTick, type Ref } from 'vue'
import type { TableRow, VirtualConfig, VirtualState } from '@/types'

// Default virtual scrolling configuration - 参考 vxetable 的默认值
interface InternalVirtualConfig {
  enabled: boolean
  threshold: number
  itemHeight: number | 'auto'
  bufferSize: number
  overscan: number
  adaptive: boolean
}

const DEFAULT_CONFIG: InternalVirtualConfig = {
  enabled: true,
  threshold: 40, // 降低阈值以更早启用虚拟滚动
  itemHeight: 48,
  bufferSize: 10,
  overscan: 5,
  adaptive: true // 自适应启用
}

export interface UseVirtualOptions {
  containerRef: Ref<HTMLElement | null>
  data: Ref<TableRow[]>
  config: Ref<VirtualConfig | undefined>
}

export interface VirtualScrollMetrics {
  updateCount: number
  averageUpdateTime: number
  scrollFPS: number
  cacheHitRate: number
  visibleItemCount: number
  bufferEfficiency: number
}

export interface UseVirtualReturn {
  // State
  virtualState: Ref<VirtualState>
  isVirtualEnabled: Ref<boolean>

  // Computed
  visibleData: Ref<TableRow[]>
  containerStyle: Ref<Record<string, string | undefined>>
  scrollAreaStyle: Ref<Record<string, string | undefined>>

  // Methods
  scrollToIndex: (index: number) => void
  scrollToTop: () => void
  updateVisibleRange: () => void
  getItemHeight: (index: number) => number

  // Performance monitoring
  getMetrics: () => VirtualScrollMetrics
}

/**
 * Virtual scrolling composable
 * Optimizes rendering performance for large datasets by only rendering visible items
 */
export function useVirtual({ containerRef, data, config }: UseVirtualOptions): UseVirtualReturn {
  // Virtual scrolling state
  const virtualState = ref<VirtualState>({
    scrollTop: 0,
    visibleStart: 0,
    visibleEnd: 0,
    totalHeight: 0,
    containerHeight: 0
  })

  // 性能监控状态
  const performanceMetrics = {
    updateTimes: [] as number[],
    updateCount: 0,
    lastFPSTime: 0,
    frameCount: 0,
    scrollFPS: 0
  }

  // 增量更新状态缓存
  const incrementalState = {
    lastScrollTop: 0,
    lastVisibleStart: 0,
    lastVisibleEnd: 0,
    lastContainerHeight: 0,
    lastItemHeight: 0,
    updateCount: 0
  }

  // 预测性样式缓存
  class PredictiveStyleCache {
    private cache = new Map<
      string,
      { style: Record<string, string>; lastAccessed: number; accessCount: number }
    >()
    private maxSize = 150
    private scrollDirection: 'up' | 'down' | null = null
    private lastVisibleStart = 0

    get(key: string): Record<string, string> | undefined {
      const item = this.cache.get(key)
      if (item) {
        item.lastAccessed = performance.now()
        item.accessCount++
        return item.style
      }
      return undefined
    }

    set(key: string, style: Record<string, string>) {
      // 如果缓存已满，清理最少使用的项目
      if (this.cache.size >= this.maxSize) {
        this.evictLeastUsed()
      }

      this.cache.set(key, {
        style: { ...style },
        lastAccessed: performance.now(),
        accessCount: 1
      })
    }

    updateScrollDirection(currentVisibleStart: number) {
      if (currentVisibleStart > this.lastVisibleStart) {
        this.scrollDirection = 'down'
      } else if (currentVisibleStart < this.lastVisibleStart) {
        this.scrollDirection = 'up'
      }
      this.lastVisibleStart = currentVisibleStart
    }

    preloadStyles(currentStart: number, itemHeight: number, count: number = 5) {
      // 根据滚动方向预加载样式
      const direction = this.scrollDirection === 'down' ? 1 : -1
      for (let i = 1; i <= count; i++) {
        const predictedStart = currentStart + direction * i
        if (predictedStart >= 0) {
          const key = `${predictedStart}-${itemHeight}`
          if (!this.cache.has(key)) {
            const offsetY = predictedStart * itemHeight
            const style = {
              transform: `translateY(${offsetY}px)`,
              position: 'relative' as const
            }
            this.set(key, style)
          }
        }
      }
    }

    private evictLeastUsed() {
      let leastUsedKey: string | undefined
      let leastUsedScore = Infinity
      const now = performance.now()

      for (const [key, item] of this.cache.entries()) {
        // 计算使用分数：考虑访问次数和最近访问时间
        const timeSinceAccess = now - item.lastAccessed
        const score = item.accessCount / (1 + timeSinceAccess / 1000) // 时间权重

        if (score < leastUsedScore) {
          leastUsedScore = score
          leastUsedKey = key
        }
      }

      if (leastUsedKey) {
        this.cache.delete(leastUsedKey)
      }
    }

    clear() {
      this.cache.clear()
      this.scrollDirection = null
      this.lastVisibleStart = 0
    }

    size(): number {
      return this.cache.size
    }

    getHitRate(): number {
      let totalAccess = 0
      let hitCount = 0

      for (const item of this.cache.values()) {
        totalAccess += item.accessCount
        if (item.accessCount > 1) {
          hitCount += item.accessCount - 1
        }
      }

      return totalAccess > 0 ? hitCount / totalAccess : 0
    }
  }

  const predictiveStyleCache = new PredictiveStyleCache()

  // Initialize visible range with proper defaults
  const initializeVirtualState = () => {
    if (!isVirtualEnabled.value) return

    const cfg = mergedConfig.value
    const itemHeight = getItemHeight(0)
    const containerHeight = containerRef.value?.clientHeight || 500
    const visibleCount = Math.ceil(containerHeight / itemHeight) + cfg.bufferSize * 2 + cfg.overscan

    virtualState.value = {
      scrollTop: 0,
      visibleStart: 0,
      visibleEnd: Math.min(data.value.length, visibleCount),
      totalHeight: totalHeight.value,
      containerHeight
    }

    // 初始化性能监控
    if (performanceMetrics.lastFPSTime === 0) {
      performanceMetrics.lastFPSTime = performance.now()
    }
    performanceMetrics.updateCount = 0
    performanceMetrics.frameCount = 0
    performanceMetrics.scrollFPS = 0
  }

  // Check if virtual scrolling is enabled - 参考 vxetable 的智能启用逻辑
  const isVirtualEnabled = computed(() => {
    const cfg = config.value
    if (!cfg) return false

    // 如果显式禁用，直接返回 false
    if (cfg.enabled === false) return false

    const threshold = cfg.threshold || DEFAULT_CONFIG.threshold
    const dataLength = data.value.length

    // 自适应模式：类似 vxetable 的 gt 机制
    if (cfg.adaptive !== false) {
      return dataLength >= threshold
    }

    // 非自适应模式：需要显式启用且满足阈值
    return cfg.enabled === true && dataLength >= threshold
  })

  // Get merged configuration with defaults
  const mergedConfig = computed(() => ({
    ...DEFAULT_CONFIG,
    ...config.value
  }))

  // 高度测量缓存
  const heightCache = new Map<number, number>()

  // Enhanced item height calculation with real DOM measurement
  const getItemHeight = (index: number): number => {
    const cfg = mergedConfig.value

    if (cfg.itemHeight === 'auto') {
      // 从缓存中获取高度
      if (heightCache.has(index)) {
        return heightCache.get(index)!
      }

      // 尝试从实际DOM中测量高度
      if (containerRef.value && data.value[index] && typeof containerRef.value.querySelectorAll === 'function') {
        const rows = containerRef.value.querySelectorAll('tbody tr')
        if (rows.length > 0) {
          // 找到对应的行并测量高度
          const relativeIndex = index - virtualState.value.visibleStart
          if (relativeIndex >= 0 && relativeIndex < rows.length) {
            const row = rows[relativeIndex] as HTMLElement
            const height = row.getBoundingClientRect().height
            if (height > 0) {
              heightCache.set(index, height)
              return height
            }
          }

          // 如果找不到具体行，使用第一个可见行的高度作为估算
          const firstRow = rows[0] as HTMLElement
          const estimatedHeight = firstRow.getBoundingClientRect().height
          if (estimatedHeight > 0) {
            return estimatedHeight
          }
        }
      }

      // 动态高度模式的智能估算
      const baseHeight = 48
      const dataItem = data.value[index]
      if (dataItem && typeof dataItem === 'object') {
        // 根据数据内容估算高度（简单的启发式方法）
        const contentLength = Object.values(dataItem).join(' ').length

        // 内容越多，预估高度越高
        const estimatedExtraHeight = Math.min(Math.floor(contentLength / 100) * 12, 48)
        const estimatedHeight = baseHeight + estimatedExtraHeight

        // 缓存估算高度
        heightCache.set(index, estimatedHeight)
        return estimatedHeight
      }

      return baseHeight // 最后的回退高度
    }

    return typeof cfg.itemHeight === 'number' ? cfg.itemHeight : 48
  }

  // 清理高度缓存的函数
  const clearHeightCache = () => {
    heightCache.clear()
  }

  // Calculate total height of all items
  const totalHeight = computed(() => {
    if (!isVirtualEnabled.value) return 0

    const cfg = mergedConfig.value
    if (cfg.itemHeight === 'auto') {
      // Sum up all item heights (simplified)
      return data.value.length * 48
    }

    return data.value.length * (cfg.itemHeight as number)
  })

  // 动态计算缓冲区大小
  const getDynamicBufferSize = (baseBufferSize: number, scrollVelocity: number): number => {
    // 根据滚动速度动态调整缓冲区
    if (scrollVelocity > 5) {
      // 高速滚动：增加缓冲区防止白屏
      return Math.min(baseBufferSize * 2, 25)
    } else if (scrollVelocity > 2) {
      // 中速滚动：适度增加缓冲区
      return Math.ceil(baseBufferSize * 1.5)
    }
    // 低速或静止：使用基本缓冲区
    return baseBufferSize
  }

  // Update virtual state when data or container size changes
  const updateVisibleRange = () => {
    const updateStartTime = performance.now()

    if (!isVirtualEnabled.value || !containerRef.value) {
      return
    }

    // FPS 监控
    const now = performance.now()
    performanceMetrics.frameCount++
    if (now - performanceMetrics.lastFPSTime >= 1000) {
      performanceMetrics.scrollFPS = Math.round(
        (performanceMetrics.frameCount * 1000) / (now - performanceMetrics.lastFPSTime)
      )
      performanceMetrics.frameCount = 0
      performanceMetrics.lastFPSTime = now
    } else if (performanceMetrics.lastFPSTime === 0) {
      // 初始化FPS监控
      performanceMetrics.lastFPSTime = now
    }

    const container = containerRef.value
    const containerHeight = container.clientHeight
    const scrollTop = container.scrollTop
    const cfg = mergedConfig.value
    const itemHeight = getItemHeight(0) // Assume uniform height for now

    // 增量更新检测 - 避免不必要的重计算
    const scrollDelta = Math.abs(scrollTop - incrementalState.lastScrollTop)
    const containerHeightChanged = containerHeight !== incrementalState.lastContainerHeight
    const itemHeightChanged = itemHeight !== incrementalState.lastItemHeight

    // 如果变化很小且不是容器尺寸或高度变化，则跳过更新
    if (scrollDelta < itemHeight * 0.1 && !containerHeightChanged && !itemHeightChanged) {
      return
    }

    // 获取当前滚动速度并动态调整缓冲区
    const scrollVelocity = scrollThrottler.getScrollVelocity()
    const dynamicBufferSize = getDynamicBufferSize(cfg.bufferSize, scrollVelocity)
    const dynamicOverscan = Math.min(cfg.overscan + Math.floor(scrollVelocity), 15)

    // Calculate visible range with dynamic buffer
    const visibleStart = Math.max(0, Math.floor(scrollTop / itemHeight) - dynamicBufferSize)
    const visibleCount =
      Math.ceil(containerHeight / itemHeight) + dynamicBufferSize * 2 + dynamicOverscan
    const visibleEnd = Math.min(data.value.length, visibleStart + visibleCount)

    // Update state
    virtualState.value = {
      scrollTop,
      visibleStart,
      visibleEnd,
      totalHeight: totalHeight.value,
      containerHeight
    }

    // 更新增量缓存状态
    incrementalState.lastScrollTop = scrollTop
    incrementalState.lastVisibleStart = visibleStart
    incrementalState.lastVisibleEnd = visibleEnd
    incrementalState.lastContainerHeight = containerHeight
    incrementalState.lastItemHeight = itemHeight
    incrementalState.updateCount++

    // 记录性能指标
    const updateTime = performance.now() - updateStartTime
    performanceMetrics.updateTimes.push(updateTime)
    performanceMetrics.updateCount++

    // 限制性能历史记录长度
    if (performanceMetrics.updateTimes.length > 100) {
      performanceMetrics.updateTimes.shift()
    }
  }

  // Get visible data slice
  const visibleData = computed(() => {
    if (!isVirtualEnabled.value) {
      return data.value
    }

    const { visibleStart, visibleEnd } = virtualState.value
    const slicedData = data.value.slice(visibleStart, visibleEnd)

    return slicedData
  })

  // Container style for positioning
  const containerStyle = computed(() => {
    if (!isVirtualEnabled.value) return {}

    return {
      // 不设置固定高度，让外层容器决定高度
      // height: '100%', // 删除这行，避免覆盖用户设置的高度
      overflow: 'auto',
      position: 'relative'
    }
  })

  // Scroll area style with predictive caching
  const scrollAreaStyle = computed(() => {
    if (!isVirtualEnabled.value) {
      predictiveStyleCache.clear()
      return {}
    }

    const { visibleStart } = virtualState.value
    const itemHeight = getItemHeight(0)
    const offsetY = visibleStart * itemHeight
    const cacheKey = `${visibleStart}-${itemHeight}`

    // 更新滚动方向
    predictiveStyleCache.updateScrollDirection(visibleStart)

    let style = predictiveStyleCache.get(cacheKey)
    if (!style) {
      style = {
        transform: `translateY(${offsetY}px)`,
        position: 'relative' as const
      }
      predictiveStyleCache.set(cacheKey, style)
    }

    // 预加载相邻的样式（异步执行避免阻塞）
    nextTick(() => {
      predictiveStyleCache.preloadStyles(visibleStart, itemHeight)
    })

    return style
  })

  // Scroll to specific index
  const scrollToIndex = (index: number) => {
    if (!containerRef.value || !isVirtualEnabled.value) return

    const itemHeight = getItemHeight(index)
    const scrollTop = index * itemHeight
    containerRef.value.scrollTop = scrollTop
  }

  // Scroll to top
  const scrollToTop = () => {
    if (containerRef.value) {
      containerRef.value.scrollTop = 0
    }
  }

  // Enhanced scroll throttler for better performance
  class ScrollThrottler {
    private rafId: number | undefined
    private lastScrollTime = 0
    private lastScrollTop = 0
    private scrollVelocity = 0
    private isScrolling = false
    private scrollEndTimer: number | undefined

    throttleScroll(scrollTop: number, callback: () => void) {
      const now = performance.now()
      const timeDelta = now - this.lastScrollTime
      const scrollDelta = Math.abs(scrollTop - this.lastScrollTop)

      // 计算滚动速度 (像素/毫秒)
      this.scrollVelocity = timeDelta > 0 ? scrollDelta / timeDelta : 0
      this.isScrolling = true

      // 清除之前的滚动结束定时器
      if (this.scrollEndTimer) {
        clearTimeout(this.scrollEndTimer)
      }

      // 根据滚动速度调整节流策略
      if (this.scrollVelocity > 2) {
        // 高速滚动：使用RAF确保流畅性
        if (this.rafId) {
          cancelAnimationFrame(this.rafId)
        }
        this.rafId = requestAnimationFrame(() => {
          callback()
          this.rafId = undefined
        })
      } else {
        // 低速滚动：直接执行，减少RAF开销
        callback()
      }

      // 设置滚动结束检测
      this.scrollEndTimer = window.setTimeout(() => {
        this.isScrolling = false
        this.scrollVelocity = 0
      }, 150)

      this.lastScrollTime = now
      this.lastScrollTop = scrollTop
    }

    getScrollVelocity(): number {
      return this.scrollVelocity
    }

    isScrollingActive(): boolean {
      return this.isScrolling
    }

    cleanup() {
      if (this.rafId) {
        cancelAnimationFrame(this.rafId)
        this.rafId = undefined
      }
      if (this.scrollEndTimer) {
        clearTimeout(this.scrollEndTimer)
        this.scrollEndTimer = undefined
      }
    }
  }

  // Create scroll throttler instance
  const scrollThrottler = new ScrollThrottler()

  // Optimized scroll event handler
  const handleScroll = () => {
    if (!containerRef.value || !isVirtualEnabled.value) return

    const scrollTop = containerRef.value.scrollTop
    scrollThrottler.throttleScroll(scrollTop, updateVisibleRange)
  }

  // Watch for data changes
  watch(
    [data, config],
    () => {
      if (isVirtualEnabled.value && virtualState.value.visibleEnd === 0) {
        // Initialize if not yet initialized
        initializeVirtualState()
      } else {
        updateVisibleRange()
      }
    },
    { immediate: true }
  )

  // Setup scroll listener
  onMounted(() => {
    nextTick(() => {
      if (containerRef.value && isVirtualEnabled.value) {
        containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
        if (virtualState.value.visibleEnd === 0) {
          initializeVirtualState()
        } else {
          updateVisibleRange()
        }
      }
    })
  })

  // Watch for virtual enabled changes to setup/remove listeners
  watch(isVirtualEnabled, (enabled, wasEnabled) => {
    if (!containerRef.value) return

    if (enabled && !wasEnabled) {
      // Virtual scrolling was enabled
      containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
      if (virtualState.value.visibleEnd === 0) {
        initializeVirtualState()
      } else {
        updateVisibleRange()
      }
    } else if (!enabled && wasEnabled) {
      // Virtual scrolling was disabled
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
  })

  // 获取性能指标
  const getMetrics = (): VirtualScrollMetrics => {
    // 如果虚拟滚动未启用，返回基本指标
    if (!isVirtualEnabled.value) {
      return {
        updateCount: 0,
        averageUpdateTime: 0,
        scrollFPS: 0,
        cacheHitRate: 0,
        visibleItemCount: data.value.length,
        bufferEfficiency: 0
      }
    }

    const avgUpdateTime =
      performanceMetrics.updateTimes.length > 0
        ? performanceMetrics.updateTimes.reduce((a, b) => a + b, 0) /
          performanceMetrics.updateTimes.length
        : 0

    const visibleItemCount = virtualState.value.visibleEnd - virtualState.value.visibleStart
    const totalItems = data.value.length
    const bufferEfficiency =
      totalItems > 0 ? Math.max(0, 100 - (visibleItemCount / totalItems) * 100) : 0

    return {
      updateCount: performanceMetrics.updateCount,
      averageUpdateTime: Math.round(avgUpdateTime * 100) / 100,
      scrollFPS: performanceMetrics.scrollFPS,
      cacheHitRate: Math.round(predictiveStyleCache.getHitRate() * 100),
      visibleItemCount,
      bufferEfficiency: Math.round(bufferEfficiency * 100) / 100
    }
  }

  // Cleanup
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
    scrollThrottler.cleanup()
    predictiveStyleCache.clear()
    clearHeightCache()
  })

  return {
    virtualState,
    isVirtualEnabled,
    visibleData,
    containerStyle,
    scrollAreaStyle,
    scrollToIndex,
    scrollToTop,
    updateVisibleRange,
    getItemHeight,
    getMetrics
  }
}
