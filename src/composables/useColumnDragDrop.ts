/**
 * 列头拖拽排序 Composable
 * 实现 AG Grid 风格的列头拖拽重排功能
 */

import { reactive, computed, onUnmounted } from 'vue'
import type { TableColumn } from '@/types'
import type {
  DragDropState,
  DragDropConfig,
  DragEvent,
  DragCancelReason,
  DragValidationResult,
  ColumnGroup,
  UseColumnDragDropOptions,
  UseColumnDragDropReturn
} from '@/types/dragDrop'
import { DEFAULT_DRAG_DROP_CONFIG } from '@/types/dragDrop'

/**
 * 列头拖拽排序 Hook
 */
export function useColumnDragDrop(options: UseColumnDragDropOptions): UseColumnDragDropReturn {
  const { columns, onColumnReorder, onDragStart, onDragEnd, onDragCancel, config = {} } = options

  // 合并配置
  const dragConfig: DragDropConfig = { ...DEFAULT_DRAG_DROP_CONFIG, ...config }

  // 拖拽状态
  const dragState = reactive<DragDropState>({
    isDragging: false,
    draggedColumn: null,
    dropTarget: null,
    dragStartIndex: -1,
    dropPosition: null,
    dragPreview: null,
    validDropZones: new Set(),
    dragStartPosition: { x: 0, y: 0 }
  })

  // 响应式状态
  const isDragging = computed(() => dragState.isDragging)

  // 拖拽检测相关
  let dragDetectionTimer: number | null = null
  let dragThresholdExceeded = false

  /**
   * 获取列所属的组
   */
  const getColumnGroup = (column: TableColumn): ColumnGroup => {
    if (column.fixed === 'left') return 'left'
    if (column.fixed === 'right') return 'right'
    return 'normal'
  }

  /**
   * 验证拖拽目标是否有效
   */
  const validateDrop = (
    draggedColumn: TableColumn,
    targetColumn: TableColumn
  ): DragValidationResult => {
    // 基础验证
    if (!draggedColumn || !targetColumn) {
      return { isValid: false, reason: 'Missing column data', canDrop: false }
    }

    // 自己不能拖拽到自己
    if (draggedColumn.key === targetColumn.key) {
      return { isValid: false, reason: 'Cannot drop on self', canDrop: false }
    }

    // 检查拖拽是否启用
    if (!dragConfig.enabled || draggedColumn.draggable === false) {
      return { isValid: false, reason: 'Dragging disabled', canDrop: false }
    }

    // 禁止拖拽特殊列类型（根据配置决定）
    if (!dragConfig.allowSpecialColumnDrag && draggedColumn.type && dragConfig.disabledColumnTypes.includes(draggedColumn.type)) {
      return { isValid: false, reason: 'Cannot drag special columns', canDrop: false }
    }

    // 组约束检查
    if (dragConfig.constrainToGroup) {
      const draggedGroup = getColumnGroup(draggedColumn)
      const targetGroup = getColumnGroup(targetColumn)

      if (draggedGroup !== targetGroup) {
        return { isValid: false, reason: 'Cannot move between groups', canDrop: false }
      }
    }

    // 特殊列类型保护 - 不能拖拽到特殊列上（根据配置决定）
    if (!dragConfig.allowSpecialColumnDrag && targetColumn.type && dragConfig.disabledColumnTypes.includes(targetColumn.type)) {
      return { isValid: false, reason: 'Cannot drop on special columns', canDrop: false }
    }

    return { isValid: true, canDrop: true }
  }

  /**
   * 重新排列列顺序
   */
  const reorderColumns = (
    draggedKey: string,
    targetKey: string,
    position: 'before' | 'after'
  ): string[] => {
    const currentOrder = columns.map(col => col.key)
    const draggedIndex = currentOrder.indexOf(draggedKey)
    const targetIndex = currentOrder.indexOf(targetKey)

    if (draggedIndex === -1 || targetIndex === -1) {
      throw new Error('Invalid column keys for reordering')
    }

    // 创建新的顺序数组
    const newOrder = [...currentOrder]

    // 移除被拖拽的列
    newOrder.splice(draggedIndex, 1)

    // 计算新的插入位置
    let insertIndex = targetIndex
    if (draggedIndex < targetIndex) {
      insertIndex = targetIndex - 1 // 因为移除了前面的元素，索引需要调整
    }

    if (position === 'after') {
      insertIndex += 1
    }

    // 插入到新位置
    newOrder.splice(insertIndex, 0, draggedKey)

    return newOrder
  }

  /**
   * 创建拖拽预览元素
   */
  const createDragPreview = (_column: TableColumn, sourceElement: HTMLElement): HTMLElement => {
    const preview = document.createElement('div')
    preview.className = 'column-drag-preview'

    // 复制源元素的内容和样式
    const rect = sourceElement.getBoundingClientRect()
    preview.innerHTML = sourceElement.innerHTML

    // 应用拖拽预览样式
    preview.style.cssText = `
      position: fixed;
      top: ${rect.top}px;
      left: ${rect.left}px;
      width: ${rect.width}px;
      height: ${rect.height}px;
      background: var(--table-header-bg);
      border: 2px solid var(--table-primary);
      border-radius: 4px;
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      z-index: 9999;
      pointer-events: none;
      opacity: 0.9;
      transform: rotate(3deg);
      transition: none;
    `

    // 移除交互元素以防止干扰
    const resizer = preview.querySelector('.column-resizer')
    if (resizer) resizer.remove()

    document.body.appendChild(preview)
    return preview
  }

  /**
   * 更新拖拽预览位置
   */
  const updateDragPreview = (event: MouseEvent): void => {
    if (!dragState.dragPreview) return

    requestAnimationFrame(() => {
      if (!dragState.dragPreview) return

      const offsetX = 10
      const offsetY = -10

      dragState.dragPreview.style.left = `${event.clientX + offsetX}px`
      dragState.dragPreview.style.top = `${event.clientY + offsetY}px`
    })
  }

  /**
   * 创建插入指示器
   */
  const createDropIndicator = (
    targetElement: HTMLElement,
    position: 'before' | 'after'
  ): HTMLElement => {
    // 移除现有指示器
    document.querySelectorAll('.drop-indicator').forEach(el => el.remove())

    const indicator = document.createElement('div')
    indicator.className = 'drop-indicator'

    const rect = targetElement.getBoundingClientRect()
    const leftPos = position === 'before' ? rect.left - 2 : rect.right - 1

    indicator.style.cssText = `
      position: fixed;
      top: ${rect.top}px;
      left: ${leftPos}px;
      width: 3px;
      height: ${rect.height}px;
      background: var(--table-primary);
      z-index: 2000;
      pointer-events: none;
      border-radius: 1px;
      box-shadow: 0 0 6px rgba(var(--table-primary-rgb), 0.6);
    `

    document.body.appendChild(indicator)
    return indicator
  }

  /**
   * 计算拖拽位置
   */
  const calculateDropPosition = (
    event: MouseEvent,
    targetElement: HTMLElement
  ): 'before' | 'after' => {
    const rect = targetElement.getBoundingClientRect()
    const mouseX = event.clientX
    const centerX = rect.left + rect.width / 2

    return mouseX < centerX ? 'before' : 'after'
  }

  /**
   * 开始拖拽检测
   */
  const startDragDetection = (event: MouseEvent, column: TableColumn): void => {
    // 检查是否可拖拽
    if (!dragConfig.enabled || column.draggable === false) return

    // 禁止拖拽特殊列类型（根据配置决定）
    if (!dragConfig.allowSpecialColumnDrag && column.type && dragConfig.disabledColumnTypes.includes(column.type)) {
      return
    }

    // 检查是否点击在调整器上
    const target = event.target as HTMLElement
    if (target.closest('.column-resizer')) return

    // 阻止默认行为
    event.preventDefault()

    // 记录起始位置
    dragState.dragStartPosition = { x: event.clientX, y: event.clientY }
    dragThresholdExceeded = false

    // 检测拖拽意图
    const checkDragIntent = (moveEvent: MouseEvent): void => {
      const deltaX = Math.abs(moveEvent.clientX - dragState.dragStartPosition.x)
      const deltaY = Math.abs(moveEvent.clientY - dragState.dragStartPosition.y)

      if (deltaX > dragConfig.dragThreshold || deltaY > dragConfig.dragThreshold) {
        dragThresholdExceeded = true
        startDrag(column, moveEvent)
        cleanupDetection()
      }
    }

    const handleMouseUp = (): void => {
      if (!dragThresholdExceeded) {
        // 没有超过阈值，视为普通点击，不启动拖拽
        cleanupDetection()
      }
    }

    const cleanupDetection = (): void => {
      document.removeEventListener('mousemove', checkDragIntent)
      document.removeEventListener('mouseup', handleMouseUp)
      if (dragDetectionTimer) {
        clearTimeout(dragDetectionTimer)
        dragDetectionTimer = null
      }
    }

    // 添加事件监听器
    document.addEventListener('mousemove', checkDragIntent, { passive: false })
    document.addEventListener('mouseup', handleMouseUp, { once: true })

    // 超时保护，防止内存泄漏
    dragDetectionTimer = setTimeout(cleanupDetection, 5000) as unknown as number
  }

  /**
   * 开始拖拽操作
   */
  const startDrag = (column: TableColumn, event: MouseEvent): void => {
    if (dragState.isDragging) return

    // 查找源元素
    const sourceElement = (event.target as HTMLElement).closest('.header-cell') as HTMLElement
    if (!sourceElement) return

    // 初始化拖拽状态
    dragState.isDragging = true
    dragState.draggedColumn = column
    dragState.dragStartIndex = columns.findIndex(col => col.key === column.key)
    dragState.validDropZones.clear()

    // 计算有效拖拽区域
    columns.forEach(col => {
      const validation = validateDrop(column, col)
      if (validation.canDrop) {
        dragState.validDropZones.add(col.key)
      }
    })

    // 创建拖拽预览
    if (dragConfig.showDragPreview) {
      dragState.dragPreview = createDragPreview(column, sourceElement)
      updateDragPreview(event)
    }

    // 添加拖拽样式
    sourceElement.classList.add('header-cell-dragging')
    document.body.classList.add('column-dragging')

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleGlobalMouseMove, { passive: false })
    document.addEventListener('mouseup', handleGlobalMouseUp, { once: true })
    document.addEventListener('keydown', handleGlobalKeyDown)

    // 触发开始回调
    onDragStart?.(column)
  }

  /**
   * 全局鼠标移动处理
   */
  const handleGlobalMouseMove = (event: MouseEvent): void => {
    if (!dragState.isDragging || !dragState.draggedColumn) return

    // 更新预览位置
    updateDragPreview(event)

    // 检查拖拽目标
    const targetElement = event.target as HTMLElement
    const headerCell = targetElement.closest('.header-cell') as HTMLElement

    if (headerCell) {
      const columnKey = headerCell.getAttribute('data-column-key')
      const targetColumn = columns.find(col => col.key === columnKey)

      if (targetColumn) {
        handleDragOver(event, targetColumn)
      }
    } else {
      // 鼠标离开有效区域，清除拖拽目标
      clearDropTarget()
    }
  }

  /**
   * 处理拖拽悬停
   */
  const handleDragOver = (event: MouseEvent, targetColumn: TableColumn): void => {
    if (!dragState.isDragging || !dragState.draggedColumn) return

    const validation = validateDrop(dragState.draggedColumn, targetColumn)

    if (validation.canDrop) {
      const targetElement = (event.target as HTMLElement).closest('.header-cell') as HTMLElement
      if (!targetElement) return

      // 计算拖拽位置
      const position = calculateDropPosition(event, targetElement)

      // 更新状态
      dragState.dropTarget = targetColumn
      dragState.dropPosition = position

      // 显示插入指示器
      if (dragConfig.showDropIndicator) {
        createDropIndicator(targetElement, position)
      }

      // 添加有效拖拽区域样式
      targetElement.classList.add('drop-zone-valid')
    } else {
      // 无效拖拽区域
      const targetElement = (event.target as HTMLElement).closest('.header-cell') as HTMLElement
      if (targetElement) {
        targetElement.classList.add('drop-zone-invalid')
        setTimeout(() => {
          targetElement.classList.remove('drop-zone-invalid')
        }, 150)
      }

      clearDropTarget()
    }
  }

  /**
   * 处理放下操作
   */
  const handleDrop = (event: MouseEvent, targetColumn: TableColumn): void => {
    if (!dragState.isDragging || !dragState.draggedColumn) return

    event.preventDefault()
    event.stopPropagation()

    const validation = validateDrop(dragState.draggedColumn, targetColumn)

    if (validation.canDrop && dragState.dropPosition) {
      // 计算新的列顺序
      const newOrder = reorderColumns(
        dragState.draggedColumn.key,
        targetColumn.key,
        dragState.dropPosition
      )

      // 创建拖拽事件数据
      const dragEvent: DragEvent = {
        type: 'reorder',
        sourceColumn: dragState.draggedColumn,
        targetColumn,
        sourceIndex: dragState.dragStartIndex,
        targetIndex: newOrder.indexOf(dragState.draggedColumn.key),
        dropPosition: dragState.dropPosition,
        newOrder
      }

      // 触发重排回调
      onColumnReorder?.(newOrder, dragEvent)

      // 成功完成拖拽
      completeDrag(true)
    } else {
      // 无效拖拽，取消操作
      cancelDrag('invalid')
    }
  }

  /**
   * 全局鼠标释放处理
   */
  const handleGlobalMouseUp = (event: MouseEvent): void => {
    if (!dragState.isDragging) return

    // 检查是否在有效的拖拽目标上
    const targetElement = event.target as HTMLElement
    const headerCell = targetElement.closest('.header-cell') as HTMLElement

    if (headerCell && dragState.dropTarget) {
      handleDrop(event, dragState.dropTarget)
    } else {
      // 在无效区域释放，取消拖拽
      cancelDrag('outside')
    }
  }

  /**
   * 全局键盘事件处理
   */
  const handleGlobalKeyDown = (event: KeyboardEvent): void => {
    if (!dragState.isDragging) return

    if (event.key === 'Escape') {
      event.preventDefault()
      cancelDrag('escape')
    }
  }

  /**
   * 清除拖拽目标
   */
  const clearDropTarget = (): void => {
    // 清除插入指示器
    document.querySelectorAll('.drop-indicator').forEach(el => el.remove())

    // 清除拖拽区域样式
    document.querySelectorAll('.drop-zone-valid, .drop-zone-invalid').forEach(el => {
      el.classList.remove('drop-zone-valid', 'drop-zone-invalid')
    })

    // 重置状态
    dragState.dropTarget = null
    dragState.dropPosition = null
  }

  /**
   * 取消拖拽操作
   */
  const cancelDrag = (reason: DragCancelReason): void => {
    if (!dragState.isDragging) return

    completeDrag(false, reason)
  }

  /**
   * 完成拖拽操作（成功或取消）
   */
  const completeDrag = (success: boolean, cancelReason?: DragCancelReason): void => {
    const draggedColumn = dragState.draggedColumn

    // 清理视觉元素
    clearDropTarget()

    if (dragState.dragPreview) {
      dragState.dragPreview.remove()
      dragState.dragPreview = null
    }

    // 移除拖拽样式
    document.querySelectorAll('.header-cell-dragging').forEach(el => {
      el.classList.remove('header-cell-dragging')
    })
    document.body.classList.remove('column-dragging')

    // 清理事件监听器
    document.removeEventListener('mousemove', handleGlobalMouseMove)
    document.removeEventListener('mouseup', handleGlobalMouseUp)
    document.removeEventListener('keydown', handleGlobalKeyDown)

    // 触发完成回调
    if (success) {
      onDragEnd?.(draggedColumn!, true)
    } else {
      onDragEnd?.(draggedColumn!, false)
      if (cancelReason && draggedColumn) {
        onDragCancel?.(cancelReason, draggedColumn)
      }
    }

    // 重置状态
    dragState.isDragging = false
    dragState.draggedColumn = null
    dragState.dropTarget = null
    dragState.dragStartIndex = -1
    dragState.dropPosition = null
    dragState.validDropZones.clear()
  }

  /**
   * 清理资源
   */
  const cleanup = (): void => {
    if (dragState.isDragging) {
      cancelDrag('error')
    }

    if (dragDetectionTimer) {
      clearTimeout(dragDetectionTimer)
      dragDetectionTimer = null
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    dragState,
    isDragging,

    // 方法
    startDragDetection,
    startDrag,
    handleDragOver,
    handleDrop,
    cancelDrag,

    // 验证
    validateDrop,
    getColumnGroup,

    // 工具函数
    reorderColumns,

    // 清理
    cleanup
  }
}
