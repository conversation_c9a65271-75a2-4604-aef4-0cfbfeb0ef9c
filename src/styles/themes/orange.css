[data-theme='orange'] {
  /* Primary colors */
  --table-primary: #ea580c;
  --table-primary-hover: #dc2626;
  --table-primary-active: #c2410c;
  --table-primary-rgb: 234, 88, 12;

  /* Background colors */
  --table-bg: #fefefe;
  --table-bg-secondary: #fef7f0;
  --table-background: #fefefe;
  --table-surface: #fef7f0;
  --table-header-bg: #fed7aa;
  --table-row-hover-bg: #fde68a;
  --table-row-selected-bg: #fed7aa;
  --table-hover: #fed7aa;

  /* Text colors */
  --table-text: #1c1917;
  --table-text-secondary: #78716c;
  --table-text-disabled: #a8a29e;

  /* Border colors */
  --table-border-color: #d6d3d1;
  --table-border-light-color: #e7e5e4;
  --table-border-hover-color: #a8a29e;
  --table-border-width: 1px;
  --table-border-style: solid;

  /* Composite border variables for backward compatibility */
  --table-border: var(--table-border-width) var(--table-border-style) var(--table-border-color);
  --table-border-light: var(--table-border-width) var(--table-border-style)
    var(--table-border-light-color);
  --table-border-hover: var(--table-border-width) var(--table-border-style)
    var(--table-border-hover-color);

  /* Stripe variables */
  --table-stripe-bg-primary: #fef7f0;
  --table-stripe-bg-subtle: #fffbf5;
  --table-stripe-bg-high-contrast: #fed7aa;
  --table-stripe-bg-mobile: #fef7f0;
  --table-primary-bg-5: rgba(234, 88, 12, 0.05);

  /* Status colors */
  --table-success: #22c55e;
  --table-warning: #f59e0b;
  --table-error: #ef4444;
  --table-info: #3b82f6;

  /* Shadow */
  --table-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --table-shadow-hover: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-left: 8px 0px 10px -5px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-right: -8px 0px 10px -5px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-header-left: 2px 2px 4px -1px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-header-right: -2px 2px 4px -1px rgb(0 0 0 / 0.1);

  /* Spacing */
  --table-padding: 0.75rem;
  --table-padding-sm: 0.5rem;
  --table-padding-lg: 1rem;

  /* Border radius */
  --table-border-radius: 0.375rem;
  --table-border-radius-sm: 0.25rem;

  /* Font */
  --table-font-size: 0.875rem;
  --table-font-size-sm: 0.75rem;
  --table-font-size-lg: 1rem;
  --table-font-weight: 400;
  --table-font-weight-bold: 600;

  /* Transitions */
  --table-transition: all 150ms ease-in-out;
  --table-transition-fast: all 100ms ease-in-out;

  /* Tooltip colors */
  --table-tooltip-bg: rgba(194, 65, 12, 0.9);
  --table-tooltip-text: #fefefe;
}
