[data-theme='enterprise'] {
  /* Primary colors */
  --table-primary: #4f46e5;
  --table-primary-hover: #4338ca;
  --table-primary-active: #3730a3;
  --table-primary-rgb: 79, 70, 229;

  /* Background colors */
  --table-bg: #f8fafc;
  --table-bg-secondary: #ffffff;
  --table-background: #f8fafc;
  --table-surface: #ffffff;
  --table-header-bg: #e2e8f0;
  --table-row-hover-bg: #f1f5f9;
  --table-row-selected-bg: #e0e7ff;
  --table-hover: #e2e8f0;

  /* Text colors */
  --table-text: #0f172a;
  --table-text-secondary: #475569;
  --table-text-disabled: #64748b;

  /* Border colors */
  --table-border-color: #cbd5e1;
  --table-border-light-color: #e2e8f0;
  --table-border-hover-color: #94a3b8;
  --table-border-width: 1px;
  --table-border-style: solid;

  /* Composite border variables for backward compatibility */
  --table-border: var(--table-border-width) var(--table-border-style) var(--table-border-color);
  --table-border-light: var(--table-border-width) var(--table-border-style)
    var(--table-border-light-color);
  --table-border-hover: var(--table-border-width) var(--table-border-style)
    var(--table-border-hover-color);

  /* Stripe variables */
  --table-stripe-bg-primary: #ffffff;
  --table-stripe-bg-subtle: #fcfcfd;
  --table-stripe-bg-high-contrast: #f1f5f9;
  --table-stripe-bg-mobile: #ffffff;
  --table-primary-bg-5: rgba(79, 70, 229, 0.05);

  /* Status colors */
  --table-success: #10b981;
  --table-warning: #f59e0b;
  --table-error: #ef4444;
  --table-info: #06b6d4;

  /* Shadow */
  --table-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --table-shadow-hover: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-left: 8px 0px 10px -5px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-right: -8px 0px 10px -5px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-header-left: 2px 2px 4px -1px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-header-right: -2px 2px 4px -1px rgb(0 0 0 / 0.1);

  /* Tooltip colors */
  --table-tooltip-bg: rgba(0, 0, 0, 0.8);
  --table-tooltip-text: #ffffff;
}
