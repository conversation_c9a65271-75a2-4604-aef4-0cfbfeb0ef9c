:root {
  /* Primary colors */
  --table-primary: #3b82f6;
  --table-primary-hover: #2563eb;
  --table-primary-active: #1d4ed8;
  --table-primary-rgb: 59, 130, 246;

  /* Background colors */
  --table-bg: #ffffff;
  --table-bg-secondary: #f8fafc;
  --table-background: #ffffff;
  --table-surface: #f8fafc;
  --table-header-bg: #f1f5f9;
  --table-row-hover-bg: #eef2f6;
  --table-row-selected-bg: #dbeafe;
  --table-hover: #f1f5f9;

  /* Text colors */
  --table-text: #1e293b;
  --table-text-secondary: #64748b;
  --table-text-disabled: #94a3b8;

  /* Border colors */
  --table-border-color: #e2e8f0;
  --table-border-light-color: #f1f5f9;
  --table-border-hover-color: #cbd5e1;
  --table-border-width: 1px;
  --table-border-style: solid;

  /* Composite border variables for backward compatibility */
  --table-border: var(--table-border-width) var(--table-border-style) var(--table-border-color);
  --table-border-light: var(--table-border-width) var(--table-border-style)
    var(--table-border-light-color);
  --table-border-hover: var(--table-border-width) var(--table-border-style)
    var(--table-border-hover-color);

  /* Stripe variables */
  --table-stripe-bg-primary: #f8fafc;
  --table-stripe-bg-subtle: #fafbfc;
  --table-stripe-bg-high-contrast: #f1f5f9;
  --table-stripe-bg-mobile: #f8fafc;
  --table-primary-bg-5: rgba(59, 130, 246, 0.05);

  /* Status colors */
  --table-success: #10b981;
  --table-warning: #f59e0b;
  --table-error: #ef4444;
  --table-info: #06b6d4;

  /* Shadow */
  --table-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --table-shadow-hover: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-left: 8px 0px 10px -5px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-right: -8px 0px 10px -5px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-header-left: 2px 2px 4px -1px rgb(0 0 0 / 0.1);
  --table-shadow-fixed-header-right: -2px 2px 4px -1px rgb(0 0 0 / 0.1);

  /* Spacing */
  --table-padding: 0.75rem;
  --table-padding-sm: 0.5rem;
  --table-padding-lg: 1rem;

  /* Border radius */
  --table-border-radius: 0.375rem;
  --table-border-radius-sm: 0.25rem;

  /* Font */
  --table-font-size: 0.875rem;
  --table-font-size-sm: 0.75rem;
  --table-font-size-lg: 1rem;
  --table-font-weight: 400;
  --table-font-weight-bold: 600;

  /* Transitions */
  --table-transition: all 150ms ease-in-out;
  --table-transition-fast: all 100ms ease-in-out;

  /* Tooltip colors */
  --table-tooltip-bg: rgba(0, 0, 0, 0.8);
  --table-tooltip-text: #ffffff;
}
