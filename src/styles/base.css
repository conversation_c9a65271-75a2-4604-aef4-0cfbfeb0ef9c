/* Base styles and CSS variables for Vue Table Component */
@import 'tailwindcss';
@import './themes/default.css';
@import './themes/dark.css';
@import './themes/enterprise.css';
@import './themes/orange.css';
@import './components/table-borders.css';
@import './components/table-stripes.css';
@import './components/table-cell-unified.css';

/* Global reset for full screen layout */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Tailwind CSS 4 Theme Configuration */
@theme {
  /* Colors */
  --color-table-primary: var(--table-primary);
  --color-table-primary-hover: var(--table-primary-hover);
  --color-table-primary-active: var(--table-primary-active);
  --color-table-bg: var(--table-bg);
  --color-table-bg-secondary: var(--table-bg-secondary);
  --color-table-header-bg: var(--table-header-bg);
  --color-table-row-hover-bg: var(--table-row-hover-bg);
  --color-table-row-selected-bg: var(--table-row-selected-bg);
  --color-table-text: var(--table-text);
  --color-table-text-secondary: var(--table-text-secondary);
  --color-table-text-disabled: var(--table-text-disabled);
  --color-table-border: var(--table-border);
  --color-table-border-light: var(--table-border-light);
  --color-table-success: var(--table-success);
  --color-table-warning: var(--table-warning);
  --color-table-error: var(--table-error);
  --color-table-info: var(--table-info);

  /* Shadcn-style colors for UI components */
  --color-primary: 222.2 84% 4.9%;
  --color-primary-foreground: 210 40% 98%;
  --color-muted: 210 40% 96%;
  --color-muted-foreground: 215.4 16.3% 46.9%;
  --color-accent: 210 40% 96%;
  --color-accent-foreground: 222.2 84% 4.9%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 222.2 84% 4.9%;
  --color-border: 214.3 31.8% 91.4%;
  --color-ring: 222.2 84% 4.9%;

  /* Spacing */
  --spacing-table-sm: var(--table-padding-sm);
  --spacing-table-md: var(--table-padding);
  --spacing-table-lg: var(--table-padding-lg);

  /* Border Radius */
  --radius-table: var(--table-border-radius);
  --radius-table-sm: var(--table-border-radius-sm);

  /* Font Size */
  --text-table-sm: var(--table-font-size-sm);
  --text-table-base: var(--table-font-size);
  --text-table-lg: var(--table-font-size-lg);

  /* Font Weight */
  --font-weight-table-normal: var(--table-font-weight);
  --font-weight-table-bold: var(--table-font-weight-bold);

  /* Box Shadow */
  --shadow-table: var(--table-shadow);
  --shadow-table-hover: var(--table-shadow-hover);

  /* Transitions */
  --transition-duration-table: var(--table-transition);
  --transition-duration-table-fast: var(--table-transition-fast);
}

/* Base component styles */
.vue-table-container {
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
  color: var(--table-text);
  background-color: var(--table-bg);
  transition: all 150ms ease-in-out;
}

/* Theme transition styles */
.vue-table-theme-provider {
  transition:
    background-color 150ms ease-in-out,
    color 150ms ease-in-out;
}

/* Smooth transitions for theme changes */
.vue-table-theme-provider * {
  transition:
    background-color 150ms ease-in-out,
    border-color 150ms ease-in-out,
    color 150ms ease-in-out,
    box-shadow 150ms ease-in-out,
    opacity 150ms ease-in-out;
}

/* Disable transitions when requested */
.vue-table-theme-provider.theme-transitions-disabled,
.vue-table-theme-provider.theme-transitions-disabled * {
  transition: none !important;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .vue-table-theme-provider,
  .vue-table-theme-provider * {
    transition: none !important;
  }
}

/* Enhanced theme-specific styles */
[data-theme='default'] {
  color-scheme: light;
}

[data-theme='dark'] {
  color-scheme: dark;
}

[data-theme='enterprise'] {
  color-scheme: light;
}

[data-theme='orange'] {
  color-scheme: light;
}