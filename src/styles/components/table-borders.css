/**
 * 表格边框统一样式管理
 * 提供语义化的边框类，替代硬编码样式
 */

/* ========================================
   基础边框样式
   ======================================== */

/* 容器级边框变体 */
.table-border-container-default {
  border: var(--table-border);
}

.table-border-container-full {
  border: var(--table-border);
}

.table-border-container-outer {
  border: var(--table-border);
}

.table-border-container-inner {
  border: none;
}

.table-border-container-none {
  border: none;
}

/* ========================================
   表头边框样式
   ======================================== */

.table-border-header-default {
  border-bottom: var(--table-border);
}

.table-border-header-full {
  border-bottom: var(--table-border);
}

.table-border-header-outer {
  border-bottom: var(--table-border);
}

.table-border-header-inner {
  border-bottom: var(--table-border);
}

.table-border-header-none {
  border-bottom: none;
}

/* 表头单元格边框 */
.table-border-header-cell-default {
  /* border-right: var(--table-border); */
  border-right: none;
}

.table-border-header-cell-full {
  border-right: var(--table-border);
}

.table-border-header-cell-outer {
  border-right: none;
}

.table-border-header-cell-inner {
  border-right: var(--table-border);
}

.table-border-header-cell-none {
  border-right: none;
}

/* 表头最后一列 */
.table-border-header-cell-default:last-child,
.table-border-header-cell-full:last-child,
.table-border-header-cell-outer:last-child,
.table-border-header-cell-inner:last-child,
.table-border-header-cell-none:last-child {
  border-right: none;
}

/* ========================================
   单元格边框样式
   ======================================== */

/* 数据单元格边框 */
.table-border-cell-default {
  border-bottom: var(--table-border);
}

.table-border-cell-full {
  border-right: var(--table-border);
  border-bottom: var(--table-border);
}

.table-border-cell-outer {
  border-right: none;
  border-bottom: none;
}

.table-border-cell-inner {
  border-right: var(--table-border);
  border-bottom: var(--table-border);
}

.table-border-cell-none {
  border-right: none;
  border-bottom: none;
}

/* 单元格最后一列 */
.table-border-cell-default:last-child,
.table-border-cell-full:last-child,
.table-border-cell-outer:last-child,
.table-border-cell-inner:last-child,
.table-border-cell-none:last-child {
  border-right: none;
}

/* ========================================
   固定列边框样式
   ======================================== */

/* 左固定列边框 - 使用高特异性避免 !important */
.table-container .table-border-fixed-left {
  position: sticky;
  z-index: 10;
  /* background-color: var(--table-bg); */
}

/* 确保固定列头部继承底边框样式，并保持标题背景色 */
.table-container .header-cell.table-border-fixed-left {
  border-bottom: none;
  background-color: var(--table-header-bg) !important;
}

.table-container .table-border-fixed-left.table-border-cell-full,
.table-container .table-border-fixed-left.table-border-cell-inner {
  border-right: var(--table-border);
}

.table-container .table-border-fixed-left.table-border-cell-default,
.table-container .table-border-fixed-left.table-border-cell-outer,
.table-container .table-border-fixed-left.table-border-cell-none {
  border-right: none;
}

.table-container .table-border-fixed-left.table-border-header-cell-full,
.table-container .table-border-fixed-left.table-border-header-cell-inner {
  border-right: var(--table-border);
}

.table-container .table-border-fixed-left.table-border-header-cell-default,
.table-container .table-border-fixed-left.table-border-header-cell-outer,
.table-container .table-border-fixed-left.table-border-header-cell-none {
  border-right: none;
}

/* 右固定列边框 */
.table-container .table-border-fixed-right {
  position: sticky;
  z-index: 10;
  /* background-color: var(--table-bg); */
}

/* 确保固定列头部继承底边框样式，并保持标题背景色 */
.table-container .header-cell.table-border-fixed-right {
  border-bottom: var(--table-border);
  background-color: var(--table-header-bg) !important;
}

.table-container .table-border-fixed-right.table-border-cell-full,
.table-container .table-border-fixed-right.table-border-cell-inner {
  /* border-left: var(--table-border); */
  border-left: none;
}

.table-container .table-border-fixed-right.table-border-cell-default,
.table-container .table-border-fixed-right.table-border-cell-outer,
.table-container .table-border-fixed-right.table-border-cell-none {
  border-left: none;
}

.table-container .table-border-fixed-right.table-border-header-cell-full,
.table-container .table-border-fixed-right.table-border-header-cell-inner {
  /* border-left: var(--table-border); */
  border-left: none;
}

.table-container .table-border-fixed-right.table-border-header-cell-default,
.table-container .table-border-fixed-right.table-border-header-cell-outer,
.table-container .table-border-fixed-right.table-border-header-cell-none {
  border-left: none;
}

/* ========================================
   固定列分隔阴影
   ======================================== */

.table-border-fixed-left-shadow {
  box-shadow: var(--table-shadow-fixed-left);
}

.table-border-fixed-right-shadow {
  box-shadow: var(--table-shadow-fixed-right);
}

.table-border-fixed-header-left-shadow {
  box-shadow: var(--table-shadow-fixed-header-left);
}

.table-border-fixed-header-right-shadow {
  box-shadow: var(--table-shadow-fixed-header-right);
}

/* ========================================
   交互式边框样式
   ======================================== */

/* 悬停状态边框 */
.table-border-hover {
  border-color: var(--table-border-hover-color);
}

/* 选中状态边框 */
.table-border-selected {
  border-color: var(--table-primary);
}

/* 编辑状态边框 */
.table-border-editing {
  border-color: var(--table-primary);
  box-shadow: 0 0 0 1px var(--table-primary);
}

/* ========================================
   输入组件边框样式
   ======================================== */

.table-input-border {
  border: var(--table-border);
  border-radius: var(--table-border-radius);
  transition:
    border-color 150ms ease-in-out,
    box-shadow 150ms ease-in-out;
}

.table-input-border:focus {
  border-color: var(--table-primary);
  box-shadow: 0 0 0 2px rgba(var(--table-primary-rgb), 0.2);
  outline: none;
}

.table-input-border:hover:not(:focus) {
  border-color: var(--table-border-hover-color);
}

/* ========================================
   分页组件边框样式
   ======================================== */

.table-pagination-border {
  border-top: var(--table-border);
}

.table-pagination-button-border {
  border: var(--table-border);
  border-radius: var(--table-border-radius);
  transition: all 150ms ease-in-out;
}

.table-pagination-button-border:hover {
  border-color: var(--table-primary);
}

.table-pagination-button-border:focus {
  border-color: var(--table-primary);
  box-shadow: 0 0 0 2px rgba(var(--table-primary-rgb), 0.2);
  outline: none;
}

.table-pagination-button-border.active {
  border-color: var(--table-primary);
  background-color: var(--table-primary);
  color: white;
}

/* ========================================
   响应式边框样式
   ======================================== */

@media (max-width: 768px) {
  /* 移动端边框优化 */
  .table-border-cell-full,
  .table-border-cell-inner {
    border-bottom-width: 1px;
  }

  .table-border-header-cell-full,
  .table-border-header-cell-inner {
    border-right-width: 1px;
  }
}

/* ========================================
   高对比度模式支持
   ======================================== */

@media (prefers-contrast: high) {
  .table-border-container-default,
  .table-border-container-full,
  .table-border-container-outer {
    border-width: 2px;
  }

  .table-border-cell-default,
  .table-border-cell-full,
  .table-border-cell-inner,
  .table-border-header-cell-default,
  .table-border-header-cell-full,
  .table-border-header-cell-inner {
    border-width: 2px;
  }
}

/* ========================================
   减少动画模式支持
   ======================================== */

@media (prefers-reduced-motion: reduce) {
  .table-input-border,
  .table-pagination-button-border {
    transition: none;
  }
}
