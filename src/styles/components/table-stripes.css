/**
 * 表格斑马行统一样式管理
 * 提供语义化的斑马行类，替代硬编码样式
 */

/* ========================================
   基础斑马行样式
   ======================================== */

/* 容器级斑马行配置 */
.table-stripe-container-default {
  /* 容器不直接设置斑马行样式，由行控制 */
}

.table-stripe-container-subtle {
  /* 容器不直接设置斑马行样式，由行控制 */
}

.table-stripe-container-none {
  /* 容器不直接设置斑马行样式，由行控制 */
}

/* ========================================
   行级斑马行样式
   ======================================== */

/* 默认斑马行 - 奇偶行不同背景 */
.table-stripe-row-default-even {
  background-color: var(--table-bg) !important;
}

.table-stripe-row-default-odd {
  background-color: var(--table-stripe-bg-primary) !important;
}

/* 淡化斑马行 - 更轻微的对比度 */
.table-stripe-row-subtle-even {
  background-color: var(--table-bg) !important;
}

.table-stripe-row-subtle-odd {
  background-color: var(--table-stripe-bg-subtle) !important;
}

/* 无斑马行 */
.table-stripe-row-none-even,
.table-stripe-row-none-odd {
  background-color: var(--table-bg) !important;
}

/* ========================================
   状态与斑马行的优先级管理
   ======================================== */

/* 选中状态优先级高于斑马行 */
.table-stripe-row-selected {
  background-color: var(--table-row-selected-bg) !important;
}

/* 悬停状态在斑马行基础上叠加 */
.table-stripe-row-hover:not(.table-stripe-row-selected) {
  background-color: var(--table-row-hover-bg) !important;
}

/* 编辑状态优先级高于斑马行 */
.table-stripe-row-editing {
  background-color: var(--table-primary-bg-5) !important;
}

/* ========================================
   固定列斑马行样式
   ======================================== */

/* 固定列需要继承行的背景色 */
.table-stripe-cell-fixed-default-even {
  background-color: var(--table-bg) !important;
}

.table-stripe-cell-fixed-default-odd {
  background-color: var(--table-stripe-bg-primary) !important;
}

.table-stripe-cell-fixed-subtle-even {
  background-color: var(--table-bg) !important;
}

.table-stripe-cell-fixed-subtle-odd {
  background-color: var(--table-stripe-bg-subtle) !important;
}

.table-stripe-cell-fixed-none-even,
.table-stripe-cell-fixed-none-odd {
  background-color: var(--table-bg) !important;
}

/* 固定列状态样式 */
.table-stripe-cell-fixed-selected {
  background-color: var(--table-row-selected-bg) !important;
}

.table-stripe-cell-fixed-hover:not(.table-stripe-cell-fixed-selected) {
  background-color: var(--table-row-hover-bg) !important;
}

.table-stripe-cell-fixed-editing {
  background-color: var(--table-primary-bg-5) !important;
}

/* ========================================
   响应式斑马行样式
   ======================================== */

@media (max-width: 768px) {
  /* 移动端可以减少斑马行对比度 */
  .table-stripe-row-default-odd {
    background-color: var(--table-stripe-bg-mobile, var(--table-stripe-bg-primary)) !important;
  }
}

/* ========================================
   高对比度模式支持
   ======================================== */

@media (prefers-contrast: high) {
  .table-stripe-row-default-odd,
  .table-stripe-cell-fixed-default-odd {
    background-color: var(--table-stripe-bg-high-contrast) !important;
  }
}

/* ========================================
   减少动画模式支持
   ======================================== */

@media (prefers-reduced-motion: reduce) {
  .table-stripe-row-default-even,
  .table-stripe-row-default-odd,
  .table-stripe-row-subtle-even,
  .table-stripe-row-subtle-odd,
  .table-stripe-cell-fixed-default-even,
  .table-stripe-cell-fixed-default-odd,
  .table-stripe-cell-fixed-subtle-even,
  .table-stripe-cell-fixed-subtle-odd {
    transition: none;
  }
}
