/**
 * 统一单元格样式系统 - 基于 DRY、KISS、SOLID、YAGNI 原则
 * 
 * 架构设计:
 * 1. Foundation Layer: 原子级样式单元
 * 2. Composition Layer: 状态组合逻辑  
 * 3. Application Layer: 组件级样式应用
 */

/* ========================================
   Foundation Layer: 原子级样式单元
   ======================================== */

/* 基础单元格样式 */
@utility cell-foundation {
  padding: 0.75rem 1rem;
  color: var(--table-text);
  transition: all 150ms ease-in-out;
  overflow: hidden;
  flex-shrink: 0;
}

/* 边框原子 */
@utility cell-border-right {
  border-right: var(--table-border);
}
@utility cell-border-bottom {
  border-bottom: var(--table-border);
}
@utility cell-border-none {
  border: none;
}

/* 背景原子 */
@utility cell-bg-default {
  background-color: var(--table-bg);
}
@utility cell-bg-header {
  background-color: var(--table-header-bg);
}
@utility cell-bg-stripe-odd {
  background-color: var(--table-stripe-bg-primary);
}
@utility cell-bg-stripe-subtle {
  background-color: var(--table-stripe-bg-subtle);
}
@utility cell-bg-selected {
  background-color: var(--table-row-selected-bg);
}
@utility cell-bg-hover {
  background-color: var(--table-row-hover-bg);
}
@utility cell-bg-editing {
  background-color: var(--table-primary-bg-5);
}

/* 位置原子 */
@utility cell-position-sticky {
  position: sticky;
  z-index: 10;
}
@utility cell-align-center {
  text-align: center;
  justify-content: center;
}
@utility cell-align-right {
  text-align: right;
  justify-content: flex-end;
}

/* 阴影原子 */
@utility cell-shadow-left {
  box-shadow: var(--table-shadow-fixed-left);
}
@utility cell-shadow-right {
  box-shadow: var(--table-shadow-fixed-right);
}

/* ========================================
   Composition Layer: 状态组合逻辑
   ======================================== */

/* 单元格类型组合 */
.cell-type-header {
  @apply cell-foundation cell-bg-header cell-border-bottom;
  font-weight: 600;
  min-height: 48px;
  display: flex;
  align-items: center;
}

.cell-type-seq {
  @apply cell-foundation cell-bg-header cell-align-center;
  min-width: 60px;
  font-family: ui-monospace, 'SF Mono', monospace;
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--table-text-secondary);
}

.cell-type-selection {
  @apply cell-foundation cell-bg-header cell-align-center;
  min-width: 60px;
}

.cell-type-content {
  @apply cell-foundation cell-bg-default;
}

/* 固定列组合 */
.cell-fixed-left {
  @apply cell-position-sticky;
  left: var(--table-fixed-left-offset, 0);
}

.cell-fixed-right {
  @apply cell-position-sticky;
  right: var(--table-fixed-right-offset, 0);
}

.cell-fixed-left-boundary {
  @apply cell-shadow-left;
}

.cell-fixed-right-boundary {
  @apply cell-shadow-right;
}

/* 边框组合 - 基于边框配置类型 */
.cell-border-full {
  @apply cell-border-right cell-border-bottom;
}

.cell-border-inner {
  @apply cell-border-right cell-border-bottom;
}

.cell-border-default {
  @apply cell-border-bottom;
}

.cell-border-outer {
  @apply cell-border-none;
}

/* 斑马纹组合 */
.cell-stripe-default-even {
  @apply cell-bg-default;
}

.cell-stripe-default-odd {
  @apply cell-bg-stripe-odd;
}

.cell-stripe-subtle-even {
  @apply cell-bg-default;
}

.cell-stripe-subtle-odd {
  @apply cell-bg-stripe-subtle;
}

.cell-stripe-none {
  @apply cell-bg-default;
}

/* ========================================
   Application Layer: 状态优先级系统
   ======================================== */

/* 状态优先级: editing > selected > hover > stripe > default */

/* 基础交互状态 - 仅在启用悬停模式时生效 */
.table-row.row-cell-hover .cell-interactive:hover:not(.cell-disabled):not(.cell-selected):not(.cell-editing) {
  @apply cell-bg-hover;
}

.cell-selected:not(.cell-editing) {
  @apply cell-bg-selected;
}

.cell-editing {
  @apply cell-bg-editing;
  box-shadow: inset 0 0 0 1px var(--table-primary);
}

.cell-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 特殊列状态继承 */
.cell-type-seq.cell-selected,
.cell-type-selection.cell-selected,
.cell-type-seq.cell-editing,
.cell-type-selection.cell-editing {
  @apply cell-bg-header; /* 序号列和选择列始终保持头部样式 */
}

/* ========================================
   Responsive & Accessibility
   ======================================== */

@media (max-width: 768px) {
  .cell-foundation {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .cell-type-seq,
  .cell-type-selection {
    min-width: 48px;
    padding: 0.5rem;
  }
}

@media (prefers-contrast: high) {
  .cell-border-right,
  .cell-border-bottom {
    border-width: 2px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .cell-foundation,
  .cell-interactive {
    transition: none !important;
  }
}

/* Focus 状态 */
.cell-foundation:focus,
.cell-foundation:focus-visible {
  outline: none;
  box-shadow:
    0 0 0 2px var(--table-primary),
    0 0 0 4px rgba(var(--table-primary-rgb), 0.2);
}
