import { describe, it, expect } from 'vitest'
import {
  parseTemplate,
  getTemplateVariable,
  renderTemplate,
  validateTemplate,
  createTemplateRenderer,
  type TemplateContext
} from '../template'
import type { TableColumn } from '../../types'

// 测试用的模拟数据
const mockContext: TemplateContext = {
  value: 'test-value',
  row: {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    age: 30,
    active: true,
    address: {
      city: 'New York',
      country: 'USA'
    },
    tags: ['admin', 'user']
  },
  column: {
    key: 'name',
    title: '姓名',
    width: 100,
    sortable: true
  } as TableColumn,
  index: 0
}

describe('Template Utils', () => {
  describe('parseTemplate', () => {
    it('应该解析简单的模板变量', () => {
      const variables = parseTemplate('Hello {{name}}!')
      expect(variables).toEqual(['name'])
    })

    it('应该解析多个模板变量', () => {
      const variables = parseTemplate('{{name}} ({{email}}) - Age: {{age}}')
      expect(variables).toEqual(['name', 'email', 'age'])
    })

    it('应该解析嵌套属性', () => {
      const variables = parseTemplate('City: {{row.address.city}}')
      expect(variables).toEqual(['row.address.city'])
    })

    it('应该去除重复变量', () => {
      const variables = parseTemplate('{{name}} and {{name}} again')
      expect(variables).toEqual(['name'])
    })

    it('应该处理空白字符', () => {
      const variables = parseTemplate('{{ name }} and {{  email  }}')
      expect(variables).toEqual([' name ', '  email  '])
    })

    it('应该返回空数组当没有变量时', () => {
      const variables = parseTemplate('No variables here')
      expect(variables).toEqual([])
    })
  })

  describe('getTemplateVariable', () => {
    it('应该获取 value 变量', () => {
      const result = getTemplateVariable('value', mockContext)
      expect(result).toBe('test-value')
    })

    it('应该获取 index 变量', () => {
      const result = getTemplateVariable('index', mockContext)
      expect(result).toBe(0)
    })

    it('应该获取 row 属性', () => {
      const result = getTemplateVariable('row.name', mockContext)
      expect(result).toBe('John Doe')
    })

    it('应该获取 column 属性', () => {
      const result = getTemplateVariable('column.title', mockContext)
      expect(result).toBe('姓名')
    })

    it('应该获取嵌套属性', () => {
      const result = getTemplateVariable('row.address.city', mockContext)
      expect(result).toBe('New York')
    })

    it('应该从 row 中获取简单属性名', () => {
      const result = getTemplateVariable('name', mockContext)
      expect(result).toBe('John Doe')
    })

    it('应该返回 undefined 当属性不存在时', () => {
      const result = getTemplateVariable('row.nonexistent', mockContext)
      expect(result).toBe(undefined)
    })

    it('应该处理数组属性', () => {
      const result = getTemplateVariable('row.tags', mockContext)
      expect(result).toEqual(['admin', 'user'])
    })
  })

  describe('renderTemplate', () => {
    it('应该渲染简单模板', () => {
      const result = renderTemplate('Hello {{name}}!', mockContext)
      expect(result).toBe('Hello John Doe!')
    })

    it('应该渲染复杂模板', () => {
      const template = '{{row.name}} ({{row.email}}) - Age: {{row.age}}'
      const result = renderTemplate(template, mockContext)
      expect(result).toBe('John Doe (<EMAIL>) - Age: 30')
    })

    it('应该渲染嵌套属性', () => {
      const result = renderTemplate('City: {{row.address.city}}', mockContext)
      expect(result).toBe('City: New York')
    })

    it('应该处理布尔值', () => {
      const result = renderTemplate('Active: {{row.active}}', mockContext)
      expect(result).toBe('Active: 是')
    })

    it('应该处理数组', () => {
      const result = renderTemplate('Tags: {{row.tags}}', mockContext)
      expect(result).toBe('Tags: [&quot;admin&quot;,&quot;user&quot;]')
    })

    it('应该使用默认值处理不存在的变量', () => {
      const result = renderTemplate('Value: {{nonexistent}}', mockContext, {
        defaultValue: 'N/A'
      })
      expect(result).toBe('Value: N&#x2f;A')
    })

    it('应该转义HTML字符', () => {
      const contextWithHtml: TemplateContext = {
        ...mockContext,
        value: '<script>alert("xss")</script>'
      }
      const result = renderTemplate('Value: {{value}}', contextWithHtml)
      expect(result).toBe('Value: &lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2f;script&gt;')
    })

    it('应该可以禁用HTML转义', () => {
      const contextWithHtml: TemplateContext = {
        ...mockContext,
        value: '<strong>Bold</strong>'
      }
      const result = renderTemplate('Value: {{value}}', contextWithHtml, {
        escapeHtml: false
      })
      expect(result).toBe('Value: <strong>Bold</strong>')
    })

    it('应该处理错误并返回原始模板', () => {
      const result = renderTemplate('{{invalid.very.deep.path}}', mockContext)
      expect(result).toBe('')
    })

    it('应该在 throwOnError 为 true 时抛出错误', () => {
      // 测试抛出错误的情况 - 使用无效变量名
      expect(() => {
        renderTemplate('{{123invalid}}', mockContext, { throwOnError: true })
      }).toThrow('模板变量')
    })
  })

  describe('validateTemplate', () => {
    it('应该验证有效的模板', () => {
      const result = validateTemplate('Hello {{name}}!')
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])
    })

    it('应该检测不匹配的开始括号', () => {
      const result = validateTemplate('Hello {{name}!')
      expect(result.valid).toBe(false)
      expect(result.errors[0]).toContain('未闭合的起始括号')
    })

    it('应该检测不匹配的结束括号', () => {
      const result = validateTemplate('Hello name}}!')
      expect(result.valid).toBe(false)
      expect(result.errors[0]).toContain('不匹配的结束括号')
    })

    it('应该检测空的变量名', () => {
      const result = validateTemplate('Hello {{   }}!')
      expect(result.valid).toBe(false)
      expect(result.errors[0]).toContain('空的变量名')
    })

    it('应该检测无效的变量名', () => {
      const result = validateTemplate('Hello {{123invalid}}!')
      expect(result.valid).toBe(false)
      expect(result.errors[0]).toContain('无效的变量名')
    })

    it('应该允许有效的嵌套变量名', () => {
      const result = validateTemplate('Hello {{row.user.name}}!')
      expect(result.valid).toBe(true)
    })
  })

  describe('createTemplateRenderer', () => {
    it('应该创建可复用的渲染器', () => {
      const renderer = createTemplateRenderer('Hello {{name}}!')
      const result = renderer(mockContext)
      expect(result).toBe('Hello John Doe!')
    })

    it('应该在模板无效时抛出错误', () => {
      expect(() => {
        createTemplateRenderer('Hello {{name}!')
      }).toThrow('模板无效')
    })

    it('应该使用提供的选项', () => {
      const renderer = createTemplateRenderer('Value: {{nonexistent}}', {
        defaultValue: 'Default'
      })
      const result = renderer(mockContext)
      expect(result).toBe('Value: Default')
    })
  })

  describe('特殊情况测试', () => {
    it('应该处理 null 和 undefined 值', () => {
      const contextWithNulls: TemplateContext = {
        ...mockContext,
        row: {
          ...mockContext.row,
          nullValue: null,
          undefinedValue: undefined
        }
      }

      const result1 = renderTemplate('Null: {{row.nullValue}}', contextWithNulls)
      const result2 = renderTemplate('Undefined: {{row.undefinedValue}}', contextWithNulls)

      expect(result1).toBe('Null: ')
      expect(result2).toBe('Undefined: ')
    })

    it('应该处理复杂的对象', () => {
      const contextWithObject: TemplateContext = {
        ...mockContext,
        row: {
          ...mockContext.row,
          complexObject: { a: 1, b: { c: 2 } }
        }
      }

      const result = renderTemplate('Object: {{row.complexObject}}', contextWithObject)
      expect(result).toBe('Object: {&quot;a&quot;:1,&quot;b&quot;:{&quot;c&quot;:2}}')
    })

    it('应该处理循环引用的对象', () => {
      const circularObj: any = { name: 'circular' }
      circularObj.self = circularObj

      const contextWithCircular: TemplateContext = {
        ...mockContext,
        row: {
          ...mockContext.row,
          circular: circularObj
        }
      }

      const result = renderTemplate('Circular: {{row.circular}}', contextWithCircular)
      expect(result).toBe('Circular: [Object]')
    })
  })

  describe('表达式计算功能', () => {
    it('应该支持数值运算', () => {
      const result = renderTemplate('结果: {{value + 100}}', {
        ...mockContext,
        value: 25
      })
      expect(result).toBe('结果: 125')
    })

    it('应该支持字符串连接', () => {
      const result = renderTemplate('全名: {{row.name + " " + row.email}}', mockContext)
      expect(result).toBe('全名: <NAME_EMAIL>')
    })

    it('应该支持复杂表达式', () => {
      const result = renderTemplate('计算: {{(value * 2) + 10}}', {
        ...mockContext,
        value: 15
      })
      expect(result).toBe('计算: 40')
    })

    it('应该支持比较运算', () => {
      const result = renderTemplate('成年: {{row.age >= 18}}', mockContext)
      expect(result).toBe('成年: true')
    })

    it('应该处理无效表达式', () => {
      const result = renderTemplate('错误: {{invalid + + syntax}}', mockContext)
      expect(result).toBe('错误: invalid + + syntax')
    })

    it('应该支持嵌套属性的运算', () => {
      const result = renderTemplate('索引计算: {{index + 1}}', mockContext)
      expect(result).toBe('索引计算: 1')
    })

    it('应该处理null值的运算', () => {
      const contextWithNull: TemplateContext = {
        ...mockContext,
        value: null
      }
      const result = renderTemplate('空值: {{value + 10}}', contextWithNull)
      expect(result).toBe('空值: 10')
    })

    it('应该支持字符串和数字混合运算', () => {
      const result = renderTemplate('年龄描述: {{row.name + "今年" + row.age + "岁"}}', mockContext)
      expect(result).toBe('年龄描述: John Doe今年30岁')
    })
  })
})
