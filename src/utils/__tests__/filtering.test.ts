// Unit tests for filtering utilities
import { describe, it, expect } from 'vitest'
import {
  applyFilterCondition,
  applyFilterGroup,
  applyTextSearch,
  applyComplexFilter,
  applyAllFilters,
  createFilterCondition,
  createFilterGroup,
  validateFilterCondition,
  validateFilterGroup,
  getFilterableColumns,
  getColumnUniqueValues,
  highlightMatches,
  valueToString,
  getNestedValue,
  type FilterCondition,
  type FilterGroup,
  type SearchConfig
} from '../filtering'
import type { TableRow, TableColumn } from '../../types'

// Test data
const testColumns: TableColumn[] = [
  { key: 'name', title: 'Name', filterable: true },
  { key: 'age', title: 'Age', filterable: true },
  { key: 'email', title: 'Email', filterable: true },
  { key: 'status', title: 'Status', filterable: true },
  { key: 'score', title: 'Score', filterable: true },
  { key: 'date', title: 'Date', filterable: true },
  { key: 'tags', title: 'Tags', filterable: true },
  { key: 'internal', title: 'Internal', filterable: false }
]

const testData: TableRow[] = [
  {
    _id: 1,
    name: '<PERSON>',
    age: 30,
    email: '<EMAIL>',
    status: 'active',
    score: 85.5,
    date: '2023-01-15',
    tags: ['developer', 'senior'],
    internal: 'secret'
  },
  {
    _id: 2,
    name: 'Jane Smith',
    age: 25,
    email: '<EMAIL>',
    status: 'inactive',
    score: 92.0,
    date: '2023-02-20',
    tags: ['designer', 'junior'],
    internal: 'confidential'
  },
  {
    _id: 3,
    name: 'Bob Johnson',
    age: 35,
    email: '<EMAIL>',
    status: 'active',
    score: 78.3,
    date: '2023-03-10',
    tags: ['manager'],
    internal: 'public'
  },
  {
    _id: 4,
    name: 'Alice Brown',
    age: null,
    email: '<EMAIL>',
    status: 'pending',
    score: null,
    date: null,
    tags: [],
    internal: null
  }
]

describe('Filtering Utilities', () => {
  describe('valueToString', () => {
    it('should convert various values to strings', () => {
      expect(valueToString(null)).toBe('')
      expect(valueToString(undefined)).toBe('')
      expect(valueToString(123)).toBe('123')
      expect(valueToString('hello')).toBe('hello')
      expect(valueToString(true)).toBe('true')
      expect(valueToString({ a: 1 })).toBe('{"a":1}')
      expect(valueToString([1, 2, 3])).toBe('[1,2,3]')
    })
  })

  describe('getNestedValue', () => {
    it('should get nested values from objects', () => {
      const obj = { a: { b: { c: 'value' } } }
      expect(getNestedValue(obj, 'a.b.c')).toBe('value')
      expect(getNestedValue(obj, 'a.b')).toEqual({ c: 'value' })
      expect(getNestedValue(obj, 'a.x')).toBeUndefined()
      expect(getNestedValue(obj, 'name')).toBeUndefined()
    })

    it('should handle simple keys', () => {
      const obj = { name: 'John', age: 30 }
      expect(getNestedValue(obj, 'name')).toBe('John')
      expect(getNestedValue(obj, 'age')).toBe(30)
    })
  })

  describe('applyFilterCondition', () => {
    it('should apply equals filter', () => {
      const condition: FilterCondition = {
        column: 'status',
        operator: 'equals',
        value: 'active'
      }

      expect(applyFilterCondition(testData[0], condition)).toBe(true)
      expect(applyFilterCondition(testData[1], condition)).toBe(false)
    })

    it('should apply contains filter', () => {
      const condition: FilterCondition = {
        column: 'name',
        operator: 'contains',
        value: 'John'
      }

      expect(applyFilterCondition(testData[0], condition)).toBe(true)
      expect(applyFilterCondition(testData[2], condition)).toBe(true)
      expect(applyFilterCondition(testData[1], condition)).toBe(false)
    })

    it('should handle case sensitivity', () => {
      const caseSensitive: FilterCondition = {
        column: 'name',
        operator: 'contains',
        value: 'john',
        caseSensitive: true
      }

      const caseInsensitive: FilterCondition = {
        column: 'name',
        operator: 'contains',
        value: 'john',
        caseSensitive: false
      }

      expect(applyFilterCondition(testData[0], caseSensitive)).toBe(false)
      expect(applyFilterCondition(testData[0], caseInsensitive)).toBe(true)
    })

    it('should apply numeric comparisons', () => {
      const greaterThan: FilterCondition = {
        column: 'age',
        operator: 'greaterThan',
        value: 28
      }

      const lessThan: FilterCondition = {
        column: 'score',
        operator: 'lessThan',
        value: 80
      }

      expect(applyFilterCondition(testData[0], greaterThan)).toBe(true)
      expect(applyFilterCondition(testData[1], greaterThan)).toBe(false)
      expect(applyFilterCondition(testData[2], lessThan)).toBe(true)
      expect(applyFilterCondition(testData[1], lessThan)).toBe(false)
    })

    it('should apply between filter', () => {
      const condition: FilterCondition = {
        column: 'age',
        operator: 'between',
        value: [25, 32]
      }

      expect(applyFilterCondition(testData[0], condition)).toBe(true)
      expect(applyFilterCondition(testData[1], condition)).toBe(true)
      expect(applyFilterCondition(testData[2], condition)).toBe(false)
    })

    it('should apply in filter', () => {
      const condition: FilterCondition = {
        column: 'status',
        operator: 'in',
        value: ['active', 'pending']
      }

      expect(applyFilterCondition(testData[0], condition)).toBe(true)
      expect(applyFilterCondition(testData[1], condition)).toBe(false)
      expect(applyFilterCondition(testData[3], condition)).toBe(true)
    })

    it('should apply isEmpty and isNotEmpty filters', () => {
      const isEmpty: FilterCondition = {
        column: 'age',
        operator: 'isEmpty',
        value: null
      }

      const isNotEmpty: FilterCondition = {
        column: 'age',
        operator: 'isNotEmpty',
        value: null
      }

      expect(applyFilterCondition(testData[3], isEmpty)).toBe(true)
      expect(applyFilterCondition(testData[0], isEmpty)).toBe(false)
      expect(applyFilterCondition(testData[0], isNotEmpty)).toBe(true)
      expect(applyFilterCondition(testData[3], isNotEmpty)).toBe(false)
    })

    it('should apply regex filter', () => {
      const condition: FilterCondition = {
        column: 'email',
        operator: 'regex',
        value: '.*@example\\.com$'
      }

      expect(applyFilterCondition(testData[0], condition)).toBe(true)
      expect(applyFilterCondition(testData[1], condition)).toBe(true)
      expect(applyFilterCondition(testData[2], condition)).toBe(false)
    })
  })

  describe('applyFilterGroup', () => {
    it('should apply AND filter group', () => {
      const group: FilterGroup = {
        conditions: [
          { column: 'status', operator: 'equals', value: 'active' },
          { column: 'age', operator: 'greaterThan', value: 28 }
        ],
        operator: 'and'
      }

      expect(applyFilterGroup(testData[0], group)).toBe(true) // John: active, 30
      expect(applyFilterGroup(testData[1], group)).toBe(false) // Jane: inactive, 25
      expect(applyFilterGroup(testData[2], group)).toBe(true) // Bob: active, 35
    })

    it('should apply OR filter group', () => {
      const group: FilterGroup = {
        conditions: [
          { column: 'status', operator: 'equals', value: 'inactive' },
          { column: 'age', operator: 'greaterThan', value: 32 }
        ],
        operator: 'or'
      }

      expect(applyFilterGroup(testData[0], group)).toBe(false) // John: active, 30
      expect(applyFilterGroup(testData[1], group)).toBe(true) // Jane: inactive, 25
      expect(applyFilterGroup(testData[2], group)).toBe(true) // Bob: active, 35
    })

    it('should handle nested filter groups', () => {
      const nestedGroup: FilterGroup = {
        conditions: [
          {
            conditions: [
              { column: 'status', operator: 'equals', value: 'active' },
              { column: 'age', operator: 'greaterThan', value: 28 }
            ],
            operator: 'and'
          },
          { column: 'score', operator: 'greaterThan', value: 90 }
        ],
        operator: 'or'
      }

      expect(applyFilterGroup(testData[0], nestedGroup)).toBe(true) // John: matches first group
      expect(applyFilterGroup(testData[1], nestedGroup)).toBe(true) // Jane: score > 90
      expect(applyFilterGroup(testData[2], nestedGroup)).toBe(true) // Bob: matches first group
    })
  })

  describe('applyTextSearch', () => {
    it('should search across multiple columns', () => {
      const searchConfig: SearchConfig = {
        text: 'john',
        caseSensitive: false
      }

      const result = applyTextSearch(testData, searchConfig, testColumns)
      expect(result.data).toHaveLength(2) // John Doe and Bob Johnson
      expect(result.data[0].name).toBe('John Doe')
      expect(result.data[1].name).toBe('Bob Johnson')
    })

    it('should respect case sensitivity', () => {
      const caseSensitive: SearchConfig = {
        text: 'john',
        caseSensitive: true
      }

      const caseInsensitive: SearchConfig = {
        text: 'john',
        caseSensitive: false
      }

      const sensitiveResult = applyTextSearch(testData, caseSensitive, testColumns)
      const insensitiveResult = applyTextSearch(testData, caseInsensitive, testColumns)

      // Case sensitive search for 'john' should find '<EMAIL>' in email
      expect(sensitiveResult.data).toHaveLength(1) // <NAME_EMAIL>
      expect(insensitiveResult.data).toHaveLength(2) // Finds both John Doe and Bob Johnson
    })

    it('should search specific columns only', () => {
      const searchConfig: SearchConfig = {
        text: 'example',
        columns: ['email'],
        caseSensitive: false
      }

      const result = applyTextSearch(testData, searchConfig, testColumns)
      expect(result.data).toHaveLength(2) // John and Jane have @example.com
    })

    it('should generate highlight information', () => {
      const searchConfig: SearchConfig = {
        text: 'john',
        caseSensitive: false,
        highlight: true
      }

      const result = applyTextSearch(testData, searchConfig, testColumns)
      expect(result.highlightInfo).toBeDefined()
      expect(result.highlightInfo?.size).toBeGreaterThan(0)
    })

    it('should support regex search', () => {
      const searchConfig: SearchConfig = {
        text: '\\d{2}',
        regex: true,
        caseSensitive: false
      }

      const result = applyTextSearch(testData, searchConfig, testColumns)
      expect(result.data.length).toBeGreaterThan(0) // Should find rows with numbers
    })
  })

  describe('applyComplexFilter', () => {
    it('should apply complex filter with multiple conditions', () => {
      const filterGroup: FilterGroup = {
        conditions: [
          { column: 'status', operator: 'equals', value: 'active' },
          { column: 'score', operator: 'greaterThan', value: 80 }
        ],
        operator: 'and'
      }

      const result = applyComplexFilter(testData, filterGroup)
      expect(result).toHaveLength(1) // Only John meets both conditions
      expect(result[0].name).toBe('John Doe')
    })
  })

  describe('applyAllFilters', () => {
    it('should combine search and complex filters', () => {
      const searchConfig: SearchConfig = {
        text: 'active',
        caseSensitive: false
      }

      const filterGroup: FilterGroup = {
        conditions: [{ column: 'age', operator: 'greaterThan', value: 28 }],
        operator: 'and'
      }

      const result = applyAllFilters(testData, searchConfig, filterGroup, testColumns)
      expect(result.data).toHaveLength(2) // John and Bob are active and age > 28
    })
  })

  describe('createFilterCondition', () => {
    it('should create filter condition with correct properties', () => {
      const condition = createFilterCondition('name', 'contains', 'John', true)

      expect(condition).toEqual({
        column: 'name',
        operator: 'contains',
        value: 'John',
        caseSensitive: true
      })
    })
  })

  describe('createFilterGroup', () => {
    it('should create filter group with conditions', () => {
      const conditions = [
        createFilterCondition('status', 'equals', 'active'),
        createFilterCondition('age', 'greaterThan', 25)
      ]

      const group = createFilterGroup(conditions, 'and')

      expect(group).toEqual({
        conditions,
        operator: 'and'
      })
    })
  })

  describe('validateFilterCondition', () => {
    it('should validate correct filter conditions', () => {
      const validCondition: FilterCondition = {
        column: 'name',
        operator: 'contains',
        value: 'test'
      }

      expect(validateFilterCondition(validCondition, testColumns)).toBe(true)
    })

    it('should reject invalid filter conditions', () => {
      const invalidColumn: FilterCondition = {
        column: 'nonexistent',
        operator: 'contains',
        value: 'test'
      }

      const nonFilterableColumn: FilterCondition = {
        column: 'internal',
        operator: 'contains',
        value: 'test'
      }

      const missingValue: FilterCondition = {
        column: 'name',
        operator: 'contains',
        value: undefined
      }

      expect(validateFilterCondition(invalidColumn, testColumns)).toBe(false)
      expect(validateFilterCondition(nonFilterableColumn, testColumns)).toBe(false)
      expect(validateFilterCondition(missingValue, testColumns)).toBe(false)
    })

    it('should validate operator-specific requirements', () => {
      const validBetween: FilterCondition = {
        column: 'age',
        operator: 'between',
        value: [20, 40]
      }

      const invalidBetween: FilterCondition = {
        column: 'age',
        operator: 'between',
        value: [20] // Should have 2 values
      }

      const validIn: FilterCondition = {
        column: 'status',
        operator: 'in',
        value: ['active', 'inactive']
      }

      const invalidIn: FilterCondition = {
        column: 'status',
        operator: 'in',
        value: 'active' // Should be array
      }

      expect(validateFilterCondition(validBetween, testColumns)).toBe(true)
      expect(validateFilterCondition(invalidBetween, testColumns)).toBe(false)
      expect(validateFilterCondition(validIn, testColumns)).toBe(true)
      expect(validateFilterCondition(invalidIn, testColumns)).toBe(false)
    })
  })

  describe('validateFilterGroup', () => {
    it('should validate correct filter groups', () => {
      const validGroup: FilterGroup = {
        conditions: [
          { column: 'name', operator: 'contains', value: 'test' },
          { column: 'age', operator: 'greaterThan', value: 25 }
        ],
        operator: 'and'
      }

      expect(validateFilterGroup(validGroup, testColumns)).toBe(true)
    })

    it('should reject invalid filter groups', () => {
      const invalidOperator: FilterGroup = {
        conditions: [{ column: 'name', operator: 'contains', value: 'test' }],
        operator: 'invalid' as any
      }

      const invalidCondition: FilterGroup = {
        conditions: [{ column: 'nonexistent', operator: 'contains', value: 'test' }],
        operator: 'and'
      }

      expect(validateFilterGroup(invalidOperator, testColumns)).toBe(false)
      expect(validateFilterGroup(invalidCondition, testColumns)).toBe(false)
    })
  })

  describe('getFilterableColumns', () => {
    it('should return only filterable columns', () => {
      const filterable = getFilterableColumns(testColumns)
      expect(filterable).toHaveLength(7) // All except 'internal'
      expect(filterable.every(col => col.filterable !== false)).toBe(true)
    })
  })

  describe('getColumnUniqueValues', () => {
    it('should extract unique values from column', () => {
      const statusValues = getColumnUniqueValues(testData, 'status')
      expect(statusValues).toEqual(['active', 'inactive', 'pending'])
    })

    it('should respect limit parameter', () => {
      const limitedValues = getColumnUniqueValues(testData, 'status', 2)
      expect(limitedValues).toHaveLength(2)
    })

    it('should handle null/undefined values', () => {
      const ageValues = getColumnUniqueValues(testData, 'age')
      expect(ageValues).toEqual([25, 30, 35]) // null values excluded
    })
  })

  describe('highlightMatches', () => {
    it('should highlight text matches', () => {
      const text = 'Hello world, hello universe'
      const matches = [
        { start: 0, end: 5, text: 'Hello' },
        { start: 13, end: 18, text: 'hello' }
      ]

      const result = highlightMatches(text, matches)
      expect(result).toBe(
        '<span class="highlight">Hello</span> world, <span class="highlight">hello</span> universe'
      )
    })

    it('should handle custom highlight class', () => {
      const text = 'test'
      const matches = [{ start: 0, end: 4, text: 'test' }]

      const result = highlightMatches(text, matches, 'custom-highlight')
      expect(result).toBe('<span class="custom-highlight">test</span>')
    })

    it('should handle empty matches', () => {
      const text = 'no matches'
      const result = highlightMatches(text, [])
      expect(result).toBe('no matches')
    })
  })
})
