import { describe, it, expect } from 'vitest'
import {
  validateTableColumn,
  validateTableRow,
  validateTableConfig,
  getDefaultTableColumn,
  getDefaultTableRow,
  getDefaultTableConfig,
  TableValidationError,
  TableConfigError
} from '../validation'
import type { TableColumn, TableRow, TableConfig } from '../../types'

describe('Validation Utilities', () => {
  describe('validateTableColumn', () => {
    it('should validate a valid column', () => {
      const column: TableColumn = {
        key: 'name',
        title: 'Name'
      }
      expect(validateTableColumn(column)).toBe(true)
    })

    it('should reject column without key', () => {
      const column = {
        title: 'Name'
      }
      expect(validateTableColumn(column)).toBe(false)
    })

    it('should reject column without title', () => {
      const column = {
        key: 'name'
      }
      expect(validateTableColumn(column)).toBe(false)
    })

    it('should reject column with invalid fixed value', () => {
      const column = {
        key: 'name',
        title: 'Name',
        fixed: 'invalid'
      }
      expect(validateTableColumn(column)).toBe(false)
    })
  })

  describe('validateTableRow', () => {
    it('should validate a valid row', () => {
      const row: TableRow = {
        name: 'John',
        age: 30
      }
      expect(validateTableRow(row)).toBe(true)
    })

    it('should reject non-object values', () => {
      expect(validateTableRow(null)).toBe(false)
      expect(validateTableRow(undefined)).toBe(false)
      expect(validateTableRow('string')).toBe(false)
      expect(validateTableRow(123)).toBe(false)
      expect(validateTableRow([])).toBe(false)
    })
  })

  describe('validateTableConfig', () => {
    it('should validate a valid config', () => {
      const config: TableConfig = {
        columns: [
          { key: 'name', title: 'Name' },
          { key: 'age', title: 'Age' }
        ],
        data: [
          { name: 'John', age: 30 },
          { name: 'Jane', age: 25 }
        ]
      }
      expect(validateTableConfig(config)).toBe(true)
    })

    it('should reject config without columns', () => {
      const config = {
        data: []
      }
      expect(validateTableConfig(config)).toBe(false)
    })

    it('should reject config with empty columns array', () => {
      const config = {
        columns: [],
        data: []
      }
      expect(validateTableConfig(config)).toBe(false)
    })
  })

  describe('getDefaultTableColumn', () => {
    it('should apply default values to column', () => {
      const column = getDefaultTableColumn({
        key: 'name',
        title: 'Name'
      })

      expect(column.sortable).toBe(true)
      expect(column.filterable).toBe(true)
      expect(column.editable).toBe(false)
      expect(column.align).toBe('left')
      expect(column.minWidth).toBe(80)
    })

    it('should preserve provided values', () => {
      const column = getDefaultTableColumn({
        key: 'name',
        title: 'Name',
        sortable: false,
        align: 'center',
        minWidth: 100
      })

      expect(column.sortable).toBe(false)
      expect(column.align).toBe('center')
      expect(column.minWidth).toBe(100)
    })
  })

  describe('getDefaultTableRow', () => {
    it('should apply default values to row', () => {
      const row = getDefaultTableRow({
        name: 'John',
        age: 30
      })

      expect(row.name).toBe('John')
      expect(row.age).toBe(30)
      expect(row._selected).toBe(false)
      expect(row._editing).toBe(false)
      expect(row._disabled).toBe(false)
      expect(row._id).toBeDefined()
    })

    it('should preserve provided meta values', () => {
      const row = getDefaultTableRow({
        name: 'John',
        _selected: true,
        _id: 'custom-id'
      })

      expect(row._selected).toBe(true)
      expect(row._id).toBe('custom-id')
    })
  })

  describe('getDefaultTableConfig', () => {
    it('should apply default values to config', () => {
      const config = getDefaultTableConfig({
        columns: [{ key: 'name', title: 'Name' }],
        data: [{ name: 'John' }]
      })

      expect(config.border).toBe(false)
      expect(config.stripe).toEqual({ enabled: false, type: 'none' })
      expect(config.hover).toBe('row')
      expect(config.size).toBe('medium')
      expect(config.loading).toBe(false)
      expect(config.theme?.name).toBe('default')
    })

    it('should process columns and data with defaults', () => {
      const config = getDefaultTableConfig({
        columns: [{ key: 'name', title: 'Name' }],
        data: [{ name: 'John' }]
      })

      expect(config.columns[0].sortable).toBe(true)
      expect(config.data[0]._selected).toBe(false)
      expect(config.data[0]._id).toBeDefined()
    })
  })

  describe('Error classes', () => {
    it('should create TableValidationError correctly', () => {
      const error = new TableValidationError('Test error', 'field', 'value')
      expect(error.name).toBe('TableValidationError')
      expect(error.message).toBe('Test error')
      expect(error.field).toBe('field')
      expect(error.value).toBe('value')
    })

    it('should create TableConfigError correctly', () => {
      const error = new TableConfigError('Config error', { test: true })
      expect(error.name).toBe('TableConfigError')
      expect(error.message).toBe('Config error')
      expect(error.config).toEqual({ test: true })
    })
  })
})
