/**
 * 安全的模板渲染工具
 * 替代不安全的 Function 构造函数，防止 XSS 攻击
 */

import type { TableColumn, TableRow } from '@/types'

export interface TemplateContext {
  value: unknown
  row: TableRow
  column: TableColumn
  index: number
}

/**
 * 安全的模板变量替换器
 * 只支持基本的变量插值，不执行任意代码
 */
export function renderTemplateSafe(template: string, context: TemplateContext): string {
  if (!template || typeof template !== 'string') {
    return String(context.value || '')
  }

  try {
    // 只支持安全的变量替换，不执行表达式
    return template.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
      const trimmed = expression.trim()
      
      // 只允许简单的点记法访问，不允许函数调用或复杂表达式
      if (!/^[a-zA-Z_$][a-zA-Z0-9_$.]*$/.test(trimmed)) {
        console.warn(`Unsafe template expression blocked: ${trimmed}`)
        return match // 返回原始模板，不解析
      }

      const value = getVariableValueSafe(trimmed, context)
      return escapeHtml(String(value || ''))
    })
  } catch (error) {
    console.warn('Template rendering error:', error)
    return escapeHtml(String(context.value || ''))
  }
}

/**
 * 安全地获取变量值 - 只支持基本的点记法
 */
function getVariableValueSafe(path: string, context: TemplateContext): unknown {
  const parts = path.split('.')
  const rootKey = parts[0]

  // 只允许访问预定义的上下文属性
  const allowedRoots = ['value', 'row', 'column', 'index']
  if (!allowedRoots.includes(rootKey)) {
    console.warn(`Blocked access to undefined template variable: ${rootKey}`)
    return ''
  }

  let current = (context as any)[rootKey]
  
  // 安全地遍历对象属性
  for (let i = 1; i < parts.length && current != null; i++) {
    const key = parts[i]
    // 只允许访问字符串和数字键，防止原型污染
    if (typeof key === 'string' && key in Object(current)) {
      current = current[key]
    } else {
      return ''
    }
  }

  return current
}

/**
 * HTML 转义函数，防止 XSS 攻击
 */
function escapeHtml(unsafe: string): string {
  if (typeof unsafe !== 'string') {
    return String(unsafe || '')
  }

  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
}

/**
 * 验证模板是否安全
 */
export function validateTemplate(template: string): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!template || typeof template !== 'string') {
    return { valid: true, errors: [] }
  }

  // 检查是否包含危险的模板语法
  const dangerousPatterns = [
    /\{\{.*[()[\]{}].*\}\}/g, // 函数调用、数组访问等
    /\{\{.*[+\-*/=<>!&|].*\}\}/g, // 运算符
    /\{\{.*__.*\}\}/g, // 双下划线（通常用于访问原型）
    /\{\{.*constructor.*\}\}/g, // 构造函数访问
    /\{\{.*eval.*\}\}/g, // eval 函数
  ]

  for (const pattern of dangerousPatterns) {
    if (pattern.test(template)) {
      errors.push(`Template contains potentially unsafe expression: ${template}`)
    }
  }

  return { valid: errors.length === 0, errors }
}