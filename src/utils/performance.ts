// Performance utilities for benchmarking virtual scrolling

// Type definition for memory info
interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

export const performance = window.performance || {
  now: () => Date.now(),
  mark: () => {},
  measure: () => {},
  clearMarks: () => {},
  clearMeasures: () => {}
}

/**
 * 简单的性能基准测试工具
 */
export class PerformanceBenchmark {
  private marks: Map<string, number> = new Map()
  private results: Map<string, number[]> = new Map()

  /**
   * 开始测量
   */
  start(name: string) {
    this.marks.set(name, performance.now())
  }

  /**
   * 结束测量并记录结果
   */
  end(name: string): number {
    const startTime = this.marks.get(name)
    if (!startTime) {
      throw new Error(`No start mark found for "${name}"`)
    }

    const duration = performance.now() - startTime

    // 记录结果
    if (!this.results.has(name)) {
      this.results.set(name, [])
    }
    this.results.get(name)!.push(duration)

    // 清除标记
    this.marks.delete(name)

    return duration
  }

  /**
   * 获取测试结果统计
   */
  getStats(name: string) {
    const results = this.results.get(name)
    if (!results || results.length === 0) {
      return null
    }

    const sorted = [...results].sort((a, b) => a - b)
    const sum = results.reduce((a, b) => a + b, 0)

    return {
      count: results.length,
      min: Math.min(...results),
      max: Math.max(...results),
      avg: sum / results.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    }
  }

  /**
   * 清除所有结果
   */
  clear() {
    this.marks.clear()
    this.results.clear()
  }

  /**
   * 获取所有测试名称
   */
  getTestNames(): string[] {
    return Array.from(this.results.keys())
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const testNames = this.getTestNames()
    if (testNames.length === 0) {
      return 'No performance data available'
    }

    let report = '=== Performance Benchmark Report ===\n\n'

    for (const name of testNames) {
      const stats = this.getStats(name)
      if (stats) {
        report += `${name}:\n`
        report += `  Count: ${stats.count}\n`
        report += `  Average: ${stats.avg.toFixed(2)}ms\n`
        report += `  Min/Max: ${stats.min.toFixed(2)}ms / ${stats.max.toFixed(2)}ms\n`
        report += `  Median: ${stats.median.toFixed(2)}ms\n`
        report += `  P95: ${stats.p95.toFixed(2)}ms\n`
        report += `  P99: ${stats.p99.toFixed(2)}ms\n\n`
      }
    }

    return report
  }
}

/**
 * 全局性能基准测试实例
 */
export const benchmark = new PerformanceBenchmark()

/**
 * FPS 计算器
 */
export class FPSCounter {
  private frames: number[] = []
  private lastTime = 0

  update() {
    const now = performance.now()
    if (this.lastTime > 0) {
      const delta = now - this.lastTime
      this.frames.push(1000 / delta) // 转换为 FPS

      // 只保留最近 60 帧的数据
      if (this.frames.length > 60) {
        this.frames.shift()
      }
    }
    this.lastTime = now
  }

  getFPS(): number {
    if (this.frames.length === 0) return 0

    const sum = this.frames.reduce((a, b) => a + b, 0)
    return Math.round(sum / this.frames.length)
  }

  reset() {
    this.frames = []
    this.lastTime = 0
  }
}

/**
 * 内存使用监控
 */
export function getMemoryInfo(): MemoryInfo | null {
  if ('memory' in performance) {
    return (performance as unknown as { memory: MemoryInfo }).memory
  }
  return null
}

/**
 * 简单的延迟测量
 */
export function measureDelay(fn: () => void | Promise<void>): Promise<number> {
  return new Promise(resolve => {
    const start = performance.now()
    Promise.resolve(fn()).then(() => {
      const end = performance.now()
      resolve(end - start)
    })
  })
}

/**
 * DOM 节点计数器
 */
export function countDOMNodes(container?: HTMLElement): number {
  const root = container || document.body
  return root.querySelectorAll('*').length
}

/**
 * 滚动性能测试
 */
export async function testScrollPerformance(
  container: HTMLElement,
  scrollAmount: number = 1000,
  steps: number = 10
): Promise<{
  avgTime: number
  maxTime: number
  fps: number[]
}> {
  const times: number[] = []
  const fpsCounter = new FPSCounter()

  const stepSize = scrollAmount / steps

  for (let i = 0; i < steps; i++) {
    const start = performance.now()

    container.scrollTop = stepSize * i

    // 等待重绘
    await new Promise(resolve => requestAnimationFrame(resolve))

    const end = performance.now()
    times.push(end - start)
    fpsCounter.update()
  }

  return {
    avgTime: times.reduce((a, b) => a + b, 0) / times.length,
    maxTime: Math.max(...times),
    fps: [fpsCounter.getFPS()]
  }
}
