/**
 * 单元格样式管理器 - 遵循 SOLID 原则的统一样式计算
 */

import type { TableColumn, TableRow, BorderConfig, StripeConfig } from '@/types'

/**
 * 单一职责: 单元格类型识别
 */
export function getCellType(column: TableColumn): 'header' | 'seq' | 'selection' | 'content' {
  if (column.type === 'seq') return 'seq'
  if (column.type === 'selection') return 'selection'
  return 'content'
}

/**
 * 单一职责: 边框样式计算
 */
export function getCellBorderClasses(
  borderConfig: BorderConfig,
  column: TableColumn
): string[] {
  const classes: string[] = []
  const type = borderConfig.type

  // 基础边框类型
  classes.push(`cell-border-${type}`)

  // 固定列边框处理
  if (column.fixed) {
    classes.push('cell-position-sticky')
    
    if (column.fixed === 'left') {
      classes.push('cell-fixed-left')
      if (column._isLastLeftFixed) {
        classes.push('cell-fixed-left-boundary')
      }
    } else if (column.fixed === 'right') {
      classes.push('cell-fixed-right')
      if (column._isFirstRightFixed) {
        classes.push('cell-fixed-right-boundary')
      }
    }
  }

  return classes
}

/**
 * 单一职责: 背景样式计算
 */
export function getCellBackgroundClasses(
  stripeConfig: StripeConfig,
  rowIndex: number,
  cellType: ReturnType<typeof getCellType>,
  states: {
    selected?: boolean
    editing?: boolean
    hover?: boolean
  } = {}
): string[] {
  const classes: string[] = []

  // 特殊列类型始终使用头部背景
  if (cellType === 'seq' || cellType === 'selection') {
    classes.push('cell-type-seq')
    return classes
  }

  // 状态优先级: editing > selected > hover > stripe > default
  if (states.editing) {
    classes.push('cell-editing')
    return classes
  }

  if (states.selected) {
    classes.push('cell-selected')
    return classes
  }

  // 斑马纹逻辑
  if (stripeConfig.enabled && stripeConfig.type !== 'none') {
    const isOdd = rowIndex % 2 === 1
    const stripeType = stripeConfig.type
    
    if (isOdd) {
      classes.push(`cell-stripe-${stripeType}-odd`)
    } else {
      classes.push(`cell-stripe-${stripeType}-even`)
    }
  } else {
    classes.push('cell-bg-default')
  }

  // 悬停状态（不覆盖选中和编辑）
  if (states.hover) {
    classes.push('cell-interactive')
  }

  return classes
}

/**
 * 单一职责: 位置样式计算
 */
export function getCellPositionStyles(
  column: TableColumn,
  getFixedColumnBorder?: (column: TableColumn) => { classes: string[]; styles: Record<string, string> }
): Record<string, string> {
  const styles: Record<string, string> = {}

  // 宽度设置
  if (column.width) {
    const width = typeof column.width === 'number' ? `${column.width}px` : column.width
    styles.width = width
    styles.minWidth = width
    styles.maxWidth = width
  }

  if (column.minWidth) {
    styles.minWidth = `${column.minWidth}px`
  }

  if (column.maxWidth) {
    styles.maxWidth = `${column.maxWidth}px`
  }

  // 对齐方式
  if (column.align) {
    styles.textAlign = column.align
  }

  // 使用原有的 getFixedColumnBorder 函数来处理固定列位置
  if (column.fixed && getFixedColumnBorder) {
    const fixedBorder = getFixedColumnBorder(column)
    Object.assign(styles, fixedBorder.styles)
  }

  return styles
}

/**
 * 开放/封闭原则: 可扩展的样式组合器
 */
export interface CellStyleOptions {
  column: TableColumn
  row?: TableRow
  rowIndex: number
  borderConfig: BorderConfig
  stripeConfig: StripeConfig
  states?: {
    selected?: boolean
    editing?: boolean
    hover?: boolean
    disabled?: boolean
  }
  getFixedColumnBorder?: (column: TableColumn) => { classes: string[]; styles: Record<string, string> }
}

/**
 * 主要样式计算函数 - 依赖倒置原则
 */
export function calculateCellClasses(options: CellStyleOptions): string[] {
  const {
    column,
    rowIndex,
    borderConfig,
    stripeConfig,
    states = {}
  } = options

  const cellType = getCellType(column)
  const classes: string[] = ['cell-foundation']

  // 类型样式
  classes.push(`cell-type-${cellType}`)

  // 边框样式
  classes.push(...getCellBorderClasses(borderConfig, column))

  // 背景样式
  classes.push(...getCellBackgroundClasses(stripeConfig, rowIndex, cellType, states))

  // 对齐样式
  if (column.align === 'center') classes.push('cell-align-center')
  if (column.align === 'right') classes.push('cell-align-right')

  // 交互状态
  if (states.disabled) classes.push('cell-disabled')
  if (column.editable && cellType === 'content') classes.push('cell-editable')

  return classes
}

/**
 * 样式计算结果接口
 */
export interface CellStyleResult {
  classes: string[]
  styles: Record<string, string>
}

/**
 * 统一样式计算入口 - 接口隔离原则
 */
export function calculateCellStyle(options: CellStyleOptions): CellStyleResult {
  const classes = calculateCellClasses(options)
  
  // 添加原有 getFixedColumnBorder 的类
  if (options.column.fixed && options.getFixedColumnBorder) {
    const fixedBorder = options.getFixedColumnBorder(options.column)
    classes.push(...fixedBorder.classes)
  }
  
  const styles = getCellPositionStyles(options.column, options.getFixedColumnBorder)

  return { classes, styles }
}