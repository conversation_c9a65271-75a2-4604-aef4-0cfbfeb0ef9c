import type { TableColumn, TableRow } from '@/types'

// 模板变量上下文
export interface TemplateContext {
  value: unknown
  row: TableRow
  column: TableColumn
  index: number
}

// 模板渲染选项
export interface TemplateOptions {
  escapeHtml?: boolean // 是否转义HTML，默认true
  defaultValue?: string // 变量不存在时的默认值，默认''
  throwOnError?: boolean // 遇到错误时是否抛出异常，默认false
}

// 模板变量匹配的正则表达式
const TEMPLATE_REGEX = /\{\{([^}]+)\}\}/g

/**
 * 安全地获取对象属性值
 * 支持嵌套属性访问，如 'row.user.name'
 */
function getNestedValue(obj: unknown, path: string): unknown {
  if (!obj || typeof obj !== 'object') {
    return undefined
  }

  const keys = path.split('.')
  let current: unknown = obj

  for (const key of keys) {
    if (current === null || current === undefined || typeof current !== 'object') {
      return undefined
    }
    current = (current as Record<string, unknown>)[key]
  }

  return current
}

/**
 * 转义HTML特殊字符
 */
function escapeHtml(str: string): string {
  const htmlEscapes: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2f;'
  }

  return str.replace(/[&<>"'/]/g, match => htmlEscapes[match])
}

/**
 * 将值转换为字符串
 */
function valueToString(value: unknown): string {
  if (value === null || value === undefined) {
    return ''
  }

  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  if (typeof value === 'object') {
    try {
      return JSON.stringify(value)
    } catch {
      return '[Object]'
    }
  }

  return String(value)
}

/**
 * 解析模板字符串，提取所有变量
 * @param template 模板字符串
 * @returns 变量名数组
 */
export function parseTemplate(template: string): string[] {
  const variables: string[] = []
  let match: RegExpExecArray | null

  // 重置正则表达式的lastIndex
  TEMPLATE_REGEX.lastIndex = 0

  while ((match = TEMPLATE_REGEX.exec(template)) !== null) {
    const variable = match[1] // 保留原始变量（包含空格）
    if (!variables.includes(variable)) {
      variables.push(variable)
    }
  }

  return variables
}

/**
 * 安全地计算简单表达式
 * 支持基本的数学运算和字符串连接
 * @param expression 表达式字符串
 * @param context 模板上下文
 * @returns 计算结果
 */
function evaluateExpression(expression: string, context: TemplateContext): unknown {
  const trimmed = expression.trim()

  // 如果不包含运算符，直接作为变量处理
  if (!/[+\-*/()&|!<>=]/.test(trimmed)) {
    return getVariableValue(trimmed, context)
  }

  try {
    // 替换表达式中的变量
    const processedExpression = trimmed.replace(
      /\b(value|index|row\.\w+(?:\.\w+)*|column\.\w+(?:\.\w+)*)\b/g,
      match => {
        const value = getVariableValue(match, context)

        // 处理不同类型的值
        if (typeof value === 'string') {
          return `"${value.replace(/"/g, '\\"')}"`
        } else if (typeof value === 'number') {
          return String(value)
        } else if (typeof value === 'boolean') {
          return String(value)
        } else if (value === null || value === undefined) {
          return '0' // 将 null/undefined 转为数值 0
        } else {
          return `"${String(value).replace(/"/g, '\\"')}"`
        }
      }
    )

    // 使用 Function 构造函数安全地计算表达式
    // 这比 eval 更安全，因为它不能访问当前作用域
    const result = new Function(`return (${processedExpression})`)()
    return result
  } catch {
    // 如果计算失败，返回原始表达式
    return trimmed
  }
}

/**
 * 获取变量值（内部辅助函数）
 * @param variable 变量名
 * @param context 模板上下文
 * @returns 变量值
 */
function getVariableValue(variable: string, context: TemplateContext): unknown {
  const trimmedVariable = variable.trim()

  // 处理特殊变量
  if (trimmedVariable === 'value') {
    return context.value
  }

  if (trimmedVariable === 'index') {
    return context.index
  }

  // 处理 row.xxx 格式
  if (trimmedVariable.startsWith('row.')) {
    const path = trimmedVariable.substring(4) // 移除 'row.' 前缀
    return getNestedValue(context.row, path)
  }

  // 处理 column.xxx 格式
  if (trimmedVariable.startsWith('column.')) {
    const path = trimmedVariable.substring(7) // 移除 'column.' 前缀
    return getNestedValue(context.column, path)
  }

  // 如果是简单的属性名，尝试从 row 中获取
  return getNestedValue(context.row, trimmedVariable)
}

/**
 * 获取模板变量的值
 * @param variable 变量名，如 'value'、'row.name'、'column.title' 或表达式 'value + 100'
 * @param context 模板上下文
 * @returns 变量值或表达式计算结果
 */
export function getTemplateVariable(variable: string, context: TemplateContext): unknown {
  const trimmedVariable = variable.trim()

  // 如果包含运算符，作为表达式处理
  if (/[+\-*/()&|!<>=]/.test(trimmedVariable)) {
    return evaluateExpression(trimmedVariable, context)
  }

  // 否则作为简单变量处理
  return getVariableValue(trimmedVariable, context)
}

/**
 * 渲染模板字符串
 * @param template 模板字符串
 * @param context 模板上下文
 * @param options 渲染选项
 * @returns 渲染后的字符串
 */
export function renderTemplate(
  template: string,
  context: TemplateContext,
  options: TemplateOptions = {}
): string {
  const { escapeHtml: shouldEscape = true, defaultValue = '', throwOnError = false } = options

  try {
    return template.replace(TEMPLATE_REGEX, (_match, variable) => {
      try {
        const trimmed = variable.trim()

        // 验证变量名
        if (throwOnError && !/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$/.test(trimmed)) {
          throw new Error(`模板变量 '${variable}' 名称无效`)
        }

        const value = getTemplateVariable(variable, context)

        // 如果是表达式计算的结果，直接转换为字符串
        let stringValue: string
        if (/[+\-*/()&|!<>=]/.test(trimmed)) {
          // 表达式结果，保持原始类型转换
          stringValue = value !== undefined ? String(value) : defaultValue
        } else {
          // 普通变量，使用中文化转换
          stringValue = value !== undefined ? valueToString(value) : defaultValue
        }

        return shouldEscape ? escapeHtml(stringValue) : stringValue
      } catch (error) {
        if (throwOnError) {
          throw error instanceof Error
            ? error
            : new Error(`模板变量 '${variable}' 渲染失败: ${error}`)
        }
        return defaultValue
      }
    })
  } catch (error) {
    if (throwOnError) {
      throw error
    }
    return template // 出错时返回原始模板
  }
}

/**
 * 验证模板字符串是否有效
 * @param template 模板字符串
 * @returns 验证结果 { valid: boolean, errors: string[] }
 */
export function validateTemplate(template: string): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // 检查括号是否匹配
  let openBraces = 0
  let i = 0

  while (i < template.length) {
    if (template[i] === '{' && template[i + 1] === '{') {
      openBraces++
      i += 2
    } else if (template[i] === '}' && template[i + 1] === '}') {
      openBraces--
      i += 2
      if (openBraces < 0) {
        errors.push('模板中存在不匹配的结束括号 "}}"')
        break
      }
    } else {
      i++
    }
  }

  if (openBraces > 0) {
    errors.push('模板中存在未闭合的起始括号 "{{"')
  }

  // 检查变量名是否有效
  const variables = parseTemplate(template)
  for (const variable of variables) {
    const trimmed = variable.trim()
    if (!trimmed) {
      errors.push('模板中存在空的变量名')
    } else if (!/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$/.test(trimmed)) {
      errors.push(`无效的变量名: "${variable}"`)
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 创建模板渲染器
 * 返回一个可复用的渲染函数
 */
export function createTemplateRenderer(template: string, options: TemplateOptions = {}) {
  // 预先验证模板
  const validation = validateTemplate(template)
  if (!validation.valid) {
    throw new Error(`模板无效: ${validation.errors.join(', ')}`)
  }

  return (context: TemplateContext): string => {
    return renderTemplate(template, context, options)
  }
}
