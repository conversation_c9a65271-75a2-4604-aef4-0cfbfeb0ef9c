<script setup lang="ts">
import { ref } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import { ThemeSwitcher } from './components/ThemeSwitcher'
import Sidebar from './components/Sidebar/Sidebar.vue'

const router = useRouter()
const currentRoute = ref('/')

// 路由菜单配置
const routes = [
  { path: '/', name: '首页', icon: '🏠' },
  { path: '/basic', name: '基础表格', icon: '📋' },
  { path: '/border', name: '边框表格', icon: '📋' },
  { path: '/sequence-column', name: '序号列', icon: '📋' },
  { path: '/selection-column', name: '选择列', icon: '📋' },
  { path: '/toolbar', name: '工具栏表格', icon: '🛠️' },
  { path: '/pagination', name: '分页表格', icon: '📄' },
  { path: '/empty-loading', name: '空数据与加载', icon: '🔄' },
  { path: '/scroll', name: '滚动表格', icon: '↔️' },
  { path: '/v-scroll', name: '虚拟滚动', icon: '🚀' },
  { path: '/multi-fixed', name: '多固定列', icon: '📌' },
  { path: '/tailwind', name: 'Tailwind验证', icon: '🎨' }
]

const navigateTo = (path: string) => {
  currentRoute.value = path
  router.push(path)
}

// 监听路由变化
router.afterEach((to: { path: string }) => {
  currentRoute.value = to.path
})
</script>

<template>
  <div
    id="app"
    class="min-h-screen flex w-full vue-table-theme-provider"
  >
    <!-- 侧边导航栏 -->
    <Sidebar
      :routes="routes"
      :current-route="currentRoute"
      @navigate="navigateTo"
    />

    <!-- 主内容区域 -->
    <main class="flex-1 overflow-auto bg-muted/30">
      <!-- 顶部标题栏 -->
      <header class="bg-card border-b border-border px-6 py-4 sticky top-0 z-10">
        <div class="flex items-center justify-between">
          <div class="space-y-1">
            <h1 class="text-2xl font-semibold tracking-tight">
              {{ routes.find(r => r.path === currentRoute)?.name || 'Vue Table Component' }}
            </h1>
            <p class="text-sm text-muted-foreground">
              {{
                routes.find(r => r.path === currentRoute)
                  ? '高性能 Vue 3 表格组件演示'
                  : '选择左侧菜单查看演示'
              }}
            </p>
          </div>
          <div class="flex items-center gap-4">
            <!-- 主题切换器 -->
            <ThemeSwitcher
              label="主题:"
              :show-label="true"
              :show-dark-toggle="false"
              :show-transitions-toggle="false"
              size="sm"
            />
            <div class="flex items-center gap-2 text-sm text-muted-foreground">
              <span>Version 1.0.0</span>
              <div class="flex h-2 w-2 items-center justify-center">
                <div class="h-full w-full rounded-full bg-green-600 animate-pulse" />
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- 路由内容区域 -->
      <div class="p-6">
        <RouterView />
      </div>
    </main>
  </div>
</template>

<style scoped>
#app {
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
}

/* 响应式设计 */
@media (max-width: 768px) {
  main {
    margin-left: 0;
  }
}
</style>
