import { createRouter, createWebHistory } from 'vue-router'
import HomePage from '../demo-views/HomePage.vue'
import BasicTableDemo from '../demo-views/BasicTableDemo.vue'
import ToolbarTableDemo from '../demo-views/ToolbarTableDemo.vue'
import PaginationTableDemo from '../demo-views/PaginationTableDemo.vue'
import EmptyAndLoadingDemo from '../demo-views/EmptyAndLoadingDemo.vue'
import ScrollTableDemo from '../demo-views/ScrollTableDemo.vue'
import MultiFixedDemo from '../demo-views/MultiFixedDemo.vue'
import VScrollDemo from '../demo-views/vScrollDemo.vue'
import TailwindDemo from '../demo-views/TailwindDemo.vue'
import SequenceColumnDemo from '../demo-views/SequenceColumnExample.vue'
import SelectionColumnDemo from '../demo-views/SelectionColumnExample.vue'
import BorderDemo from '../demo-views/BorderTableDemo.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: HomePage
    },
    {
      path: '/basic',
      name: 'Basic',
      component: BasicTableDemo
    },
    {
      path: '/border',
      name: 'Border',
      component: BorderDemo
    },
    {
      path: '/sequence-column',
      name: 'SequenceColumn',
      component: SequenceColumnDemo
    },
    {
      path: '/selection-column',
      name: 'SelectionColumn',
      component: SelectionColumnDemo
    },
    {
      path: '/toolbar',
      name: 'Toolbar',
      component: ToolbarTableDemo
    },
    {
      path: '/pagination',
      name: 'Pagination',
      component: PaginationTableDemo
    },
    {
      path: '/empty-loading',
      name: 'EmptyLoading',
      component: EmptyAndLoadingDemo
    },
    {
      path: '/scroll',
      name: 'Scroll',
      component: ScrollTableDemo
    },
    {
      path: '/v-scroll',
      name: 'VScroll',
      component: VScrollDemo
    },
    {
      path: '/multi-fixed',
      name: 'MultiFixed',
      component: MultiFixedDemo
    },
    {
      path: '/tailwind',
      name: 'Tailwind',
      component: TailwindDemo
    }
  ]
})

export default router
